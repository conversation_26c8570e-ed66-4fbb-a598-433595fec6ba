
string Ver = "1.0.194";

// (c)AutoPillock by cheerkin
// WS link: https://steamcommunity.com/sharedfiles/filedetails/?id=3158053231

static bool DesignMode;

static Vector3D GRID_ANGULAR_ACCELERATIONS = new Vector3D(0.5f, 0.5f, 1.02f);

static long? DIAG_IGC = null;
static float MAX_SP = 100f;
static float CC_GAIN = 100f; // how far away the destination point is generated for "direction" shifters
const float G = 9.81f;

static float StoppingPowerQuotient = 0.5f; // 0.9
static bool SmoothPointBlankAcceleration = true;
static float DampeningCutoffSquared = 16f;
static float DampeningCutoffFactor = 0.1f;

static string GGEN_GR_TAG = "";
static int LOGGER_MAX_CHARS = 5000;

static float PMW_FF_REACTION_R = 2500;

static bool IsLargeGrid;

public class Variables
{
	Dictionary<string, ISettable> v = new Dictionary<string, ISettable> {
		{ "wb-range-override", new Variable<float> { value = 0, parser = s => float.Parse(s) } },
		{ "wb-precision-override", new Variable<float> { value = 0, parser = s => float.Parse(s) } },
		{ "hold-thrust-on-rotation", new Variable<bool> { value = false, parser = s => s == "true" } },
		{ "torpedo-fuse-offset", new Variable<float> { value = -0.5f, parser = s => float.Parse(s) } },
		{ "roll-power-factor", new Variable<float> { value = 0.2f, parser = s => float.Parse(s) } },
		{ "sp-limit", new Variable<float> { value = 104.38f, parser = s => { var r = float.Parse(s); MAX_SP = r; return r; } } },
		{ "cc-gain", new Variable<float> { value = 100f, parser = s => { var r = float.Parse(s); CC_GAIN = r; return r; } } },
		{ "dpath-speed-limit", new Variable<float> { value = 30, parser = s => float.Parse(s) } },
		{ "capital-behavior", new Variable<bool> { value = false, parser = s => s == "true" } }, // more conservative control for big ships in certain tasks (i.e. land)
		{ "ripple-increment-interval", new Variable<int> { value = 20, parser = s => int.Parse(s) } },
		{ "ripple-increment-interval-rail", new Variable<int> { value = 180, parser = s => int.Parse(s) } },
		{ "filtering-size", new Variable<float> { value = 6f, parser = s => float.Parse(s) } },
		{ "awareness-range", new Variable<float> { value = 3000f, parser = s => float.Parse(s) } },
		{ "tg-autolink-range", new Variable<float> { value = 500f, parser = s => float.Parse(s) } },
		{ "wb-model-cycle-timeout", new Variable<int> { value = 60, parser = s => int.Parse(s) } },
		{ "diag-igc", new Variable<long?> { value = null, parser = s => { var r = long.Parse(s); DIAG_IGC = r; return r; } } },
		{ "custom-val", new Variable<float> { value = 0f, parser = s => float.Parse(s) } } // for user set in conditionals
	};
	public void Set(string key, string value) { v[key].Set(value); }
	public void Set<T>(string key, T value) { (v[key] as Variable<T>).value = value; }
	public T Get<T>(string key) { return (v[key] as Variable<T>).value; }
	public interface ISettable
	{
		void Set(string v);
	}
	public class Variable<T> : ISettable
	{
		public T value;
		public Func<string, T> parser;
		public void Set(string v) { value = parser(v); }
	}
}

public enum TargetSelection { First, Closest, Random, Loop }
public enum ResponseKind { Ignore, FreeFire, Attack, Kite, Flee }
public enum ResponseState { None, FreeFiring, Attacking }
public enum PState { Disabled = 0, Inert, WP }
public enum ThrustDelegation { None = 0, Vtol, Rover, Clang }
class ӫ{Dictionary<string,bool>Ӭ=new Dictionary<string,bool>{{"suppress-transition-control",false},{
"wingman-circle-rotation",false},{"damp-when-idle",true},{"ignore-user-thruster",false},{"coax-ripple",true},{"suppress-gyro-control",false},{
"aim-to-center",false},{"avoid-carrier",true},{"freefall-target-filter",false},{"wb-snipe-range",false},{"wb-jab",false},{
"patrol-after-response",false},{"allow-1t-up-shifter",false},{"log",false},{"echo",false}};public void ӭ(string ɡ,bool Ӯ){Ӭ[ɡ]=Ӯ;}public void ӯ
(string ɡ){Ӭ[ɡ]=!Ӭ[ɡ];}public bool Ӱ(string ɡ){return Ӭ[ɡ];}public ImmutableArray<MyTuple<string,string>>ӱ(string Ӳ){
return Ӭ.Select(Ę=>new MyTuple<string,string>($"{Ę.Key}: {(Ę.Value?"on":"off")}",$"[toggle:{Ę.Key}],[{Ӳ}]")).ToImmutableArray(
);}public void ӳ(string ɡ,ؿ ª){var Ą=ª.D.Ӱ(ɡ);switch(ɡ){case"suppress-transition-control":if(!Ą)ª.Ǆ().Б();break;case
"vtol":ª.Ǆ().ɛ(0);break;case"log":ª.տ=null;if(Ą)ª.տ=new Ӫ(ʓ.ʗ,ª);break;case"echo":ª.վ=null;if(Ą)ª.վ=new Ӫ(ʓ.ʖ,ª);break;}}}
class Ӫ{StringBuilder ө=new StringBuilder();ؿ ş;Action<string>ӣ;public Ӫ(Action<string>Ӥ,ؿ ª){ӣ=Ӥ;ş=ª;}public void ӥ(string Ì
){ө.AppendLine(Ì);}public void Ӧ(){if(ө.Length>0)ӣ(ө.ToString());ө.Clear();}}Program(){Runtime.UpdateFrequency=
UpdateFrequency.Update100;đ=GridTerminalSystem;ą=new Ć(IGC,new Dictionary<string,Action<string[],ؿ>>{{"set-output",(Ǥ,ª)=>{
IMyTextSurface Ŋ;var ӧ=ª.ف.ƺ.FirstOrDefault(Ë=>Ë.CustomName.Contains(Ǥ[2])&&(Ë is IMyTextSurfaceProvider))as IMyTextSurfaceProvider;Ŋ=
ӧ?.GetSurface(int.Parse(Ǥ[3]));if(Ŋ==null){List<IMyTextPanel>Ө=new List<IMyTextPanel>();GridTerminalSystem.
GetBlocksOfType(Ө,Ë=>Ë.IsSameConstructAs(Me)&&Ë.CustomName.Contains(Ǥ[2]));Ŋ=Ө.FirstOrDefault();}if(Ŋ!=null){ʓ.څ=(Ì)=>Ŋ.WriteText(Ì+
"\n",true);ʓ.ʙ=()=>Ŋ.WriteText("");Ŋ.ContentType=ContentType.TEXT_AND_IMAGE;}}},{"next",(Ǥ,ª)=>ª.Ɖ.Ҁ()},{"create-task",(Ǥ,ª)
=>Ր(Ǥ,ª)},{"inject-task",(Ǥ,ª)=>Ր(Ǥ,ª,true)},{"inject-task-inherit-target",(Ǥ,ª)=>Ր(Ǥ,ª,true,true)},{"remove-task",(Ǥ,ª)=>
ª.Ɖ.Ң(int.Parse(Ǥ[2]))},{"infer-task",(Ǥ,ª)=>Փ(Ǥ)},{"default-task",(Ǥ,ª)=>{ӌ.Ɖ.ԉ(()=>Ն(Ǥ,ӌ,null));ɸ.ʲ=string.Join(":",Ǥ.
Skip(2));}},{"remove-default-task",(Ǥ,ª)=>{ӌ.Ɖ.ԉ(null);ɸ.ʲ=null;}},{"signal",(Ǥ,ª)=>ª.Ɖ.ҕ()},{"repeat",(Ǥ,ª)=>{var Æ=new
List<string>(Վ);Վ.Clear();Ռ(Æ,true);}},{"save-queue",(Ǥ,ª)=>ɸ.ʳ=Վ},{"exec-queue",(Ǥ,ª)=>Ռ(ɸ.ʳ,true)},{"jab",(Ǥ,ª)=>ª.Ɖ.Ѽ(new
و(ӌ,null,null,null))},{"jab2",(Ǥ,ª)=>ª.Ɖ.Ѽ(new و(ӌ,null,null,ӌ.ك.FirstOrDefault()))},{"thrust-delegation",(Ǥ,ª)=>ª.ų.ı(Ǥ[
2])},{"recycle",(Ǥ,ª)=>Ē()},{"set-value",(Ǥ,ª)=>ª.ـ.Set(Ǥ[2],Ǥ[3])},{"set-tag",(Ǥ,ª)=>ª.Ƶ=Ǥ[2]},{"clear-state",(Ǥ,ª)=>{ɸ=
new ʯ(Ì=>Storage=Ì);Save();}},{"clear-defs",(Ǥ,ª)=>Đ()},{"clear-navs",(Ǥ,ª)=>ŝ?.Ě()},{"request-docking",(Ǥ,ª)=>ª.Ɖ.Ҥ()},{
"request-depart",(Ǥ,ª)=>{var Ȯ=ª.ط.WorldMatrix;var Ü=new ڝ(Ȯ.Translation-Ȯ.Forward*100,ӌ,10,false);ª.Ɖ.Ѿ(Ü);}},{"cancel-current-route",(
Ǥ,ª)=>ŝ?.Ŭ()},{"start-su",(Ǥ,ª)=>{var È=Ǥ[2];var Դ=Ǥ.Length>3?long.Parse(Ǥ[3]):(long?)null;if(È=="first"){var Ե=ª.ג.Where
(Ë=>!Ë.م).FirstOrDefault();if(Ե!=null)ª.ֆ(Ե,Դ);}else{foreach(var B in ª.ג.Where(Ë=>!Ë.م&&((È=="all")||(Ë.Ƶ==È))))ª.ֆ(B,Դ)
;}}},{"refresh-su",(Ǥ,ª)=>ª.ט()},{"query-target",(Ǥ,ª)=>{TargetSelection Ŋ=0;var Ԧ=string.Join(":",Ǥ.Skip(4));Dictionary<
string,string>Ɇ;var M=ª.ʆ;if(Ʉ(3,Ǥ,out Ɇ)){var ԧ=ɇ<string>(Ɇ,"TargetSelection");if(ԧ!=null)Enum.TryParse<TargetSelection>(ԧ,
out Ŋ);var Ա=ɇ<float?>(Ɇ,"R");var Ȫ=ɇ<string>(Ɇ,"Pos");if(Ȫ!=null){var Ł=Ȫ.Split(';');M=new Vector3D(double.Parse(Ł[0]),
double.Parse(Ł[1]),double.Parse(Ł[2]));}var Բ=ɇ<string>(Ɇ,"LocationFilter");var Գ=ɇ<string>(Ɇ,"TypeFilter");if(Բ=="Cone"&&Ա.
HasValue)ª.ر.ղ(Ǥ[2],(byte)Ŋ,Գ,ª.ʆ,ª.ų.ľ.WorldMatrix.Forward,Ա.Value,Ԧ);else if(Բ=="Sphere")ª.ر.ղ(Ǥ[2],(byte)Ŋ,Գ,M,Vector3D.Zero,
Ա??ª.ـ.Get<float>("awareness-range"),Ԧ);else ª.ر.ղ(Ǥ[2],(byte)Ŋ,Գ,Vector3D.Zero,Vector3D.Zero,0,Ԧ);}}},{"tmc",(Ǥ,ª)=>ª.պ?
.TryRun("q:"+string.Join(":",Ǥ.Skip(2)))},{"timer",(Ǥ,ª)=>ª.ش.Ă(Ǥ[2])},{"d-path-add",(Ǥ,ª)=>{var Æ=DIAG_IGC??ɷ.ɩ;if(Æ!=0)
{ª.Ѥ.C($"apck.dpath.add:{Ǥ[2]}",ӌ.ط.WorldMatrix,Æ);}}},{"d-path-clear",(Ǥ,ª)=>{if(ɷ.ɩ!=0)ª.Ѥ.C("apck.command",
"command:clear-navs",ɷ.ɩ);}},{"replace-behavior-task",(Ǥ,ª)=>ª.Ɖ.Ӹ(Ǥ[3],Ǥ[4],true,Ǥ[2])},{"replace-behavior-current",(Ǥ,ª)=>ª.Ɖ.Ӹ(Ǥ[2],Ǥ[3],
true)},{"chain-behavior-task",(Ǥ,ª)=>ª.Ɖ.Ӹ(Ǥ[3],Ǥ[4],false,Ǥ[2])},{"chain-behavior-current",(Ǥ,ª)=>ª.Ɖ.Ӹ(Ǥ[2],Ǥ[3],false)},{
"set-response",(Ǥ,ª)=>ª.Ɖ.Ѳ(Ǥ[2])},{"set-targeting-strategy",(Ǥ,ª)=>ʿ.ʸ(Ǥ[2])},{"detonate",(Ǥ,ª)=>{ª.Ƈ();ª.ص.ForEach(Ë=>Ë.Detonate());
}},{"set-response-ovr",(Ǥ,ª)=>ª.Ɖ.ѱ=string.Join(":",Ǥ.Skip(2))},{"w-mod-value",(Ǥ,ª)=>ɷ.ɮ(Ǥ[2],Ǥ[3])},{"wb-cycle-face",(Ǥ
,ª)=>ª.ƈ.ȍ(Ǥ.Length>2?int.Parse(Ǥ[2]):-1)},{"add-condition",(Ǥ,ª)=>ª.Ɖ.ԃ.џ(Ǥ)},{"cmdr-draw-pos",(Ǥ,ª)=>ª.Ѥ.K(ª.ʆ,Ǥ[2],Ǥ[3
],int.Parse(Ǥ[4]),Ǥ[5])},{"cmdr-draw-targetable",(Ǥ,ª)=>ª.Ѥ.K(ª.ʆ,Ǥ[2],Ǥ[3],int.Parse(Ǥ[4]),Ǥ[5],true)},{"delay-cmd",(Ǥ,ª
)=>ʡ.ʦ(()=>ª.Ѥ.Ã(string.Join(":",Ǥ.Skip(3)),ª),int.Parse(Ǥ[2])).ʘ(ɜ)},{"get-toggles",(Ǥ,ª)=>{var Ӳ=string.Join(":",Ǥ.Take
(3));ª.Ѥ.C($"menucommand.get-commands.reply:{Ӳ}",ª.D.ӱ(Ӳ),long.Parse(Ǥ[2]));}},{"get-storage",(Ǥ,ª)=>{var J=long.Parse(Ǥ[
2]);DIAG_IGC=J;ª.Ѥ.C("diag.get-storage.reply",Storage,J);}}});}class Ԁ{ؿ ş;public ѩ ԃ;Func<ڑ>Ԅ;ڑ ԅ;bool Ԇ=true;public
long?ԇ;public Ԁ(ؿ ª){ş=ª;if(ª.ł!=null)Ԇ=false;ԏ();ԃ=new ѩ(ª);ҝ=new ڷ("tgp.local.gridsense.update",false,ş.ر,Æ=>Ҝ.HasValue&&(
Æ.Ζ==Ҝ));}public void ԉ(Func<ڑ>Æ){Ԅ=Æ;if(Æ!=null){ԅ=Ԅ();Ѿ(ԅ);}}int Ԋ;void ԋ(ڑ Æ){Ԋ++;Æ.ډ(Ԋ);}Dictionary<string,Func<ӊ,ӎ?,
Vector3D>>Ԍ;public class ԍ{public bool Ԏ;public List<ӎ>ԁ=new List<ӎ>();public void Ӵ(ԍ ӿ){Ԏ=ӿ.Ԏ;ԁ=new List<ӎ>(ӿ.ԁ);}}Dictionary<
string,ԍ>ӵ=new Dictionary<string,ԍ>();Dictionary<string,ԍ>Ӷ=new Dictionary<string,ԍ>();Dictionary<string,ԍ>ӷ=new Dictionary<
string,ԍ>();public void Ӹ(string Ë,string ӹ,bool Ӻ,string ӻ=null){ԍ Ĵ;if(ӻ!=null){var Ӽ=(Ë=="aim")?Ӷ:(Ë=="move")?ӵ:ӷ;if(!Ӽ.
ContainsKey(ӻ))Ӽ.Add(ӻ,new ԍ());Ĵ=Ӽ[ӻ];}else Ĵ=(Ë=="aim")?Ԣ:(Ë=="move")?ԣ:Ԥ;Ĵ.Ԏ=Ӻ;Ĵ.ԁ.Clear();if(ӹ!="reset")ӽ(Ĵ,ӹ);}void ӽ(ԍ Ĵ,
string Ǯ){foreach(var Ç in Ǯ.Split(',')){var Ӿ=Ç.Split('/');var Ԉ=new ӎ{D=Ԍ[Ӿ[0]]};if(Ӿ.Length>1)Ԉ.ӏ=float.Parse(Ӿ[1]);Ĵ.ԁ.Add
(Ԉ);}}void ԏ(){Ԍ=new Dictionary<string,Func<ӊ,ӎ?,Vector3D>>();Ԍ.Add("AimRestrictPlane",Ӓ.ӟ);Ԍ.Add("BallisticSolver",Ӓ.Ӕ);
Ԍ.Add("SwayTargetXYZ",Ӓ.ҹ);Ԍ.Add("SwayX",Ӓ.Ү);Ԍ.Add("SwayY",Ӓ.ү);Ԍ.Add("CircleFw100",Ӓ.Ӆ);Ԍ.Add("CircleTgPlane",Ӓ.ӆ);Ԍ.
Add("Empty",Ӓ.ڐ);Ԍ.Add("NegGravity",Ӓ.ӕ);Ԍ.Add("NegGravityRejectTilt",Ӓ.Ӗ);Ԍ.Add("Closer",Ӓ.Ӊ);Ԍ.Add("Higher",Ӓ.Ҵ);Ԍ.Add(
"CqbShifter",Ӓ.Ӈ);Ԍ.Add("OrbitPoint",Ӓ.Ӑ);Ԍ.Add("AngularOffsetX",Ӓ.Ҳ);Ԍ.Add("AngularOffsetY",Ӓ.ұ);Ԍ.Add("AngularOffsetV",Ӓ.ҷ);Ԍ.Add(
"AngularOffsetNg",Ӓ.Ӄ);}public int Ԡ=>ԙ.Count;LinkedList<ڑ>ԙ=new LinkedList<ڑ>();LinkedListNode<ڑ>Ԛ;List<MyTuple<int,string,Vector3D>>ԛ=
new List<MyTuple<int,string,Vector3D>>(10);public void Ԝ(){ş.վ?.ӥ($"Cap.TargetId: {ԇ}");ş.վ?.ӥ(
$"Subordinates: {ş.ɷ.ɪ.Count:f2}");var ԝ=ş.ų;if(ş.ł==null){var Ԟ="        ";var м=new MyTuple<string,byte,int,long>(ş.Ƶ,(byte)ş.Ƴ,ş.ƴ,ş.œ.CubeGrid.
EntityId);var ԟ=new MyTuple<MyTuple<string,byte,int,long>,Vector3D,Vector3D,Vector3D,string>(м,ԝ.ľ.GetPosition(),ԝ.Ő,ԝ.ŀ,
$"{Ԟ}V': {ԝ.Ķ.Length():f1}\n{Ԟ}{ѽ()?.Ț}({ԙ.Count})");ş.ذ.SendBroadcastMessage("apck.report",ԟ);}if(Ԇ){ԛ.Clear();if(ԙ.Count>0){ԛ.Add(new MyTuple<int,string,Vector3D>(-1,
"agent",ԝ.ľ.GetPosition()));ş.վ?.ӥ("Task queue:");var ԡ=ԝ.ľ.GetPosition();foreach(var Æ in ԙ){ş.վ?.ӥ($"{Æ.ſ}-{Æ.ʍ}");if(Æ.ʆ.
HasValue)ԡ=Æ.ʆ.Value;ԛ.Add(new MyTuple<int,string,Vector3D>(Æ.ʍ,Æ.ſ,Æ.ʆ??ԡ));}ş.ذ.SendBroadcastMessage(
"captain-commander.task-data",ԛ.ToImmutableArray());}}Җ();if(Ѱ!=ResponseState.None)ԃ.ѵ(қ,Ѱ==ResponseState.FreeFiring?"FreeFiring":"Attacking");if(Ԛ!=
null){var Æ=Ԛ.Value;ԃ.ѵ(Æ.ڔ.ا??Æ.ڔ.ؾ,Æ.ſ);if(Æ.ڍ(ʓ.Ų,ş.ų)){ş.տ?.ӥ($"TFin {Æ.ſ}-{Æ.ʍ}");Ҁ();}}ԓ(Ҍ());}ԍ Ԣ=new ԍ();ԍ ԣ=new ԍ()
;ԍ Ԥ=new ԍ();Vector3D ԥ(ӊ A,ӎ?ê,ԍ Ĵ){foreach(var Ƹ in Ĵ.ԁ)A.ʎ=Ƹ.D(A,Ƹ);return A.ʎ;}Vector3D Ԑ(ӊ A,ӎ?ê){return ԥ(A,ê,Ԣ);}
Vector3D ԑ(ӊ A,ӎ?ê){return ԥ(A,ê,ԣ);}Vector3D Ԓ(ӊ A,ӎ?ê){return ԥ(A,ê,Ԥ);}void ԓ(ע Ą){var ӑ=new ӊ{ӌ=ş,Ӎ=Ą};var π=Ą.ק?.Invoke()??
ş.ʆ;var Ԕ=Ą.ا;var ԕ=Ą.פ;var Ԗ=Ą.צ;if(Ѱ==ResponseState.FreeFiring){Ą.ا=қ;Ą.פ=ҙ;}if(Ą.ؾ?.Η.HasValue==true){ӑ.ʎ=Ą.ؾ.Η.Value;
ӑ.Ӌ=Ą.ؾ.Ő??Vector3D.Zero;if(ԣ.ԁ.Count>0){if(!ԣ.Ԏ&&Ą.ף!=null)ӑ.ʎ=Ą.ף(ӑ,null);ş.ų.ŀ=ԑ(ӑ,null);}else ş.ų.ŀ=Ą.ף?.Invoke(ӑ,
null)??ӑ.ʎ;if(Ѱ==ResponseState.FreeFiring)ş.ų.ŀ=Қ(ӑ,null);Ą.ظ=ş.ų.ŀ;double ԗ=(ş.ų.ŀ-π).Length();if(Ą.ױ){if(ş.Ƅ()&&(ԗ>(MAX_SP
>200?8000:3000))){if(ҳ(π,Ą)){if(!Ӣ.HasValue){Ӣ=Ą.ש;ş.տ?.ӥ("Circumnav starts");}Ą.ש=false;}else{if(Ӣ.HasValue){Ą.ש=Ӣ.Value
;ş.տ?.ӥ("Circumnav ends");}Ӣ=null;}}else{if(Ӣ.HasValue){Ą.ש=Ӣ.Value;ş.տ?.ӥ("Circumnav ends");}Ӣ=null;}}if(Ą.ؤ)Ą.ظ=Ӓ.Ӏ(Ą.ظ
.Value,ş,Ą);}else if(Ą.ת)Ą.ظ=Ą.ף?.Invoke(ӑ,null);bool Ԙ=false;var Ԃ=Vector3D.Zero;if((ş.ų.Ģ==1)&&(ş.ų.ġ==ThrustDelegation
.None)&&Ą.ظ.HasValue){Ԙ=true;Ԃ=Ӓ.Ҏ(Ą.ظ.Value,ş,Ą);}if(Ԙ&&(Ԣ.ԁ.Count==0)){Ą.Ŀ=Ԃ;}else{if(Ą.ا?.Η.HasValue==true){ӑ.ʎ=Ą.ا.Η.
Value;ӑ.Ӌ=Ą.ا.Ő??Vector3D.Zero;if(Ԣ.ԁ.Count>0){if(!Ԣ.Ԏ&&Ą.פ!=null)ӑ.ʎ=Ą.פ(ӑ,null);ӑ.ʎ=Ԑ(ӑ,null);}else ӑ.ʎ=Ą.פ?.Invoke(ӑ,null)
??ӑ.ʎ;Ą.Ŀ=ӑ.ʎ;}else if(Ą.ת)Ą.Ŀ=Ą.פ?.Invoke(ӑ,null);}ӑ.ʎ=Ą.ظ??ş.ʆ;if(Ԥ.ԁ.Count>0){if(!Ԥ.Ԏ&&Ą.ץ!=null)ӑ.ʎ=ş.ʆ+Ą.ץ(ӑ,null);Ą.
ع=Ԓ(ӑ,null)-ş.ʆ;}else{Ą.ع=Ą.ץ?.Invoke(ӑ,null)??Vector3D.Zero;}Ą.غ=Ą.צ?.Invoke();Ą.ا=Ԕ;Ą.פ=ԕ;Ą.צ=Ԗ;}bool?Ӣ;bool ҳ(Vector3D
π,ע Ą){var ҏ=ş.ƃ.Value;var Ґ=ҏ-π;if(Vector3D.Dot(Ґ.Normalized(),ş.ų.Ń)>0.99){var Ë=ş.ų.ŀ;var ґ=Ë-ҏ;var ш=Ґ.Length();var ғ
=(Ë-π).Normalized();var Ҕ=Vector3D.Dot((ҏ-Ë).Normalized(),ғ);ş.վ?.ӥ(
$"Circumnav.alt: {ш}\nCircumnav.d: {(Ë-π).Length()}\nCircumnav.dot: {Ҕ}");if(Ҕ<0.6){var ь=new BoundingSphereD(ҏ,ш);if(ь.Contains(Ë)==ContainmentType.Contains){var Ȅ=Ӓ.Ҿ(Ë,ş,Ą);ş.վ?.ӥ(
"Circumnavigate above");Ą.ظ=Ȅ;return true;}else{ь.Radius-=100;var Ϻ=new RayD(π,ғ);var ќ=ь.Intersects(Ϻ);if(ќ.HasValue){var ѓ=Vector3D.Cross(-Ϻ
.Direction,ґ);var ѝ=Vector3D.Cross(ґ,ѓ);var Ř=ґ.Length();var ѐ=ш*ш/Ř;var у=Math.Asin(ш/Ř);var ю=Math.Cos(у)*ш;var ҍ=ҏ+ѐ*ґ
/Ř+ю*ѝ.Normalized();var Ȅ=Ӓ.Ҿ(ҍ,ş,Ą);ş.վ?.ӥ($"Outer: Circumnavigate tangent");Ą.ظ=Ȅ;return true;}}}}return false;}public
void Ѽ(ڑ Æ){ş.տ?.ӥ("CreateWP "+Æ.Ț);ѿ(Æ);}public ڑ ѽ(){return Ԛ?.Value;}public void Ѿ(ڑ Æ){ԋ(Æ);ԙ.AddLast(Æ);ş.տ?.ӥ(
$"Added {Æ.Ț}, total: {ԙ.Count}");if(Ԛ==null){Ԛ=ԙ.First;Ҋ(Æ);}else if(Ԛ.Value.ڒ&&!Ԛ.Value.ړ.HasValue)Ҁ();}public void ѿ(ڑ Æ){ԋ(Æ);ԙ.AddFirst(Æ);ş.տ?.ӥ(
$"Added and activated {Æ.Ț}, total: {ԙ.Count}");Ԛ=ԙ.First;Ҋ(Æ);}public void Ҁ(){if(Ԛ==null)return;var ҁ=Ԛ;var A=ҁ.Value;A.ڕ?.Invoke();Ԣ.ԁ.Clear();ԣ.ԁ.Clear();Ԥ.ԁ.
Clear();ş.ش.Ă(A.ſ+".OnComplete");if(Ҟ)ş.ز.ˊ();if(ҁ.Next!=null){if(ҁ==Ԛ){A=ҁ.Next.Value;Ԛ=ҁ.Next;Ҋ(A);}ԙ.Remove(ҁ);}else{ԙ.
Clear();Ԛ=null;if((ԅ!=null)&&(A!=ԅ)){ԅ=Ԅ();Ѿ(ԅ);}}}void Ҋ(ڑ Æ){Æ.ڏ(ş,ʓ.Ų);if(Ӷ.ContainsKey(Æ.ſ)&&Ӷ[Æ.ſ].ԁ.Count>0){ş.տ?.ӥ(
$"Bh aim override: {Æ.ſ}");Ԣ.Ӵ(Ӷ[Æ.ſ]);}if(ӵ.ContainsKey(Æ.ſ)&&ӵ[Æ.ſ].ԁ.Count>0){ş.տ?.ӥ($"Bh move override: {Æ.ſ}");ԣ.Ӵ(ӵ[Æ.ſ]);}if(ӷ.ContainsKey
(Æ.ſ)&&ӷ[Æ.ſ].ԁ.Count>0){ş.տ?.ӥ($"Bh up norm override: {Æ.ſ}");Ԥ.Ӵ(ӷ[Æ.ſ]);}}ע ҋ=new ע{Ț="Standby"};public ע Ҍ(){var Ғ=Ԛ?
.Value;if(Ғ!=null)return Ғ.ڔ;else return ҋ;}public void ҕ(){var Æ=ѽ();if(Æ as ڛ!=null)Ҁ();}public void Ң(int J){var ң=ԙ.
FirstOrDefault(Ë=>Ë.ʍ==J);if(ң!=null){if(ѽ()==ң)Ҁ();else ԙ.Remove(ң);}}public void Ҥ(){if(ş.ط!=null&&ş.ɷ.ɩ!=0)Ѿ(new ۊ(ş,ş.ɷ.ɩ,ş.ر.ճ(
"docking")));}public void ҥ(long Ҧ){ҧ=false;ş.Ѥ.C("apck.dpath.complete","",Ҧ);}bool ҧ;public void Ҩ(Τ Ҭ=null){if(ş.ط?.Status==
MyShipConnectorStatus.Connected){ş.ت.ForEach(Ą=>Ą.ChargeMode=ChargeMode.Auto);ş.ث.ForEach(Ą=>Ą.Stockpile=false);ş.ų.ģ(PState.WP);var ҩ=ş.ط.
OtherConnector;var Ҫ=new ڛ(ş,60);Ҫ.ڕ=()=>{ş.տ?.ӥ("OtherConnector undick wait complete");var і=Ҭ?.Η??ş.ط.GetPosition()-ş.ط.WorldMatrix.
Forward*100;if(ҩ.CustomName.Contains("dock-host")){if(ҧ==false){ҩ.CustomData=ş.ذ.Me.ToString();ҧ=true;ş.ذ.SendBroadcastMessage(
"apck.depart.request",new MyTuple<long,Vector3D>(ҩ.EntityId,і));ş.տ?.ӥ($"Waiting for depart dpath");ş.ų.ģ(PState.Inert);ş.Ɖ.ѿ(new ڛ(ş,()=>ҧ==
false));}}else{ş.տ?.ӥ($"Undock");ş.ط.Disconnect();}};ş.Ɖ.ѿ(Ҫ);}}public void ҫ(Τ Ⱥ){қ=Ⱥ;Ҝ=Ⱥ.Ζ;}public void ҭ(long J,long Ý,
Action ҡ){қ=ҝ.ɺ;Ҝ=J;ҝ.ڰ(ş,J,Ý,ҡ);}void Җ(){if(қ?.Σ()==true){if(ѯ==ResponseKind.FreeFire){if(Ѱ==ResponseState.None){ş.տ?.ӥ(
"Response: FreeFiring");Ѱ=ResponseState.FreeFiring;ҙ=Ӓ.Ӕ;Қ=Ӓ.Ӆ;}}else if(ѯ==ResponseKind.Attack){if(Ѱ==ResponseState.None){Ѱ=ResponseState.
Attacking;ş.տ?.ӥ("Response: Attacking");if(ş.ן!=null)ş.ذ.SendUnicastMessage(ş.ן.EntityId,"apck-encounter",new MyTuple<long,
Vector3D>(қ.Ζ,қ.Η.Value));if(ѱ!=null){ş.Ѥ.Ã(ѱ.Replace("{id}",қ.Ζ.ToString()),ş);}else{var Ғ=new چ(ş,қ);if(ş.D.Ӱ(
"patrol-after-response")&&!ԙ.Any()){var җ=ş.ʆ;Ғ.ڕ=()=>{var Ҙ=new ڝ(җ,ş,null,true);Ҙ.ړ=60*40;Ҙ.Ț="Search";var Ɩ=new ӎ{ӏ=500};Ҙ.ڔ.ף=(A,ê)=>Ӓ.Ӑ(A,
Ɩ);Ѿ(new ڝ(җ+Ӓ.Ҹ(new ӊ{ӌ=ş})*200,ş,20,false));Ѿ(Ҙ);};}ѿ(Ғ);}}}}else{if(Ѱ!=ResponseState.None){Ѱ=ResponseState.None;ҙ=null
;Қ=null;қ=null;Ҝ=null;ş.տ?.ӥ("Combat Response Ends");ş.ƈ.Ɨ();ҝ.ڱ();if(ѯ!=ResponseKind.Ignore)ş.ز.ˊ();}}}Func<ӊ,ӎ?,
Vector3D>ҙ;Func<ӊ,ӎ?,Vector3D>Қ;Τ қ;long?Ҝ;ڷ ҝ;public bool Ҟ=>!Ҝ.HasValue&&(ѯ!=ResponseKind.Ignore)&&(Ԛ==null||Ԛ.Value.ژ);List<ʋ
>ҟ=new List<ʋ>();public void Ҡ(ʋ Ë){var Ǧ=(Ë.ʎ-ş.ʆ).Length();ş.վ?.ӥ($"consider tg: {Ǧ:f2}");if(((ѯ==ResponseKind.Attack)
&&(Ǧ<ş.ـ.Get<float>("awareness-range")))||((ѯ==ResponseKind.FreeFire)&&(Ǧ<ş.ƈ.ƥ))){ҟ.Add(Ë);}}Random Ѝ=new Random();public
void Ѻ(int ѫ,TargetSelection Ŋ){if(ҟ.Count>0){if(Ŋ==TargetSelection.Closest)ş.ز.ʺ(ҟ.OrderBy(Ë=>(ş.ʆ-Ë.ʎ).LengthSquared()).
First(),ş);else{int È=0;if(Ŋ==TargetSelection.Random)È=Ѝ.Next(ҟ.Count);if(Ŋ==TargetSelection.Loop)È=ѫ%ҟ.Count;ş.ز.ʺ(ҟ[È],ş);}
}ҟ.Clear();}public Vector3D?Ѭ(Vector3D M){var ѭ=ş.ح.FirstOrDefault(A=>A.CanScan(M));double Ѯ=0;foreach(var A in ş.ح)Ѯ+=A.
AvailableScanRange;if(ѭ!=null){var Ǧ=ѭ.Raycast(M);if(Ǧ.HitPosition.HasValue&&Ǧ.EntityId!=ѭ.CubeGrid.EntityId)return Ǧ.HitPosition;}return
null;}public ResponseKind ѯ=ResponseKind.FreeFire;public ResponseState Ѱ=ResponseState.None;public string ѱ=null;public void
Ѳ(string Ѵ){var ѳ=ѯ;Enum.TryParse(Ѵ,out ѯ);if(ѯ!=ѳ&&ѯ!=ResponseKind.Ignore){Ѱ=ResponseState.None;Ҝ=null;ş.ز.ˊ();}}public
void Ƈ(){ş.տ?.ӥ("~Cap Finalizer");ş.ر.ՙ(ş);}}class ѩ{Dictionary<string,List<Ѣ>>Ѡ=new Dictionary<string,List<Ѣ>>();Random ѡ=
new Random();ؿ ş;public ѩ(ؿ ª){ş=ª;}class Ѣ{public List<Func<Τ,bool>>ѣ=new List<Func<Τ,bool>>();public List<string>Ѥ=new
List<string>();public int ѥ;public int Ѧ;public int ʢ;public bool ѧ(Τ Ⱥ)=>ѣ.All(Ë=>Ë(Ⱥ));}char[]Ѩ=new char[]{'<','=','>'};
public void џ(string[]Ѫ){int ˠ;int Ѷ=0;int È=3;var Ç=Ѫ[2];if(int.TryParse(Ѫ[3],out ˠ)){È++;if(int.TryParse(Ѫ[4],out Ѷ))È++;}
else ˠ=300;var ѷ=string.Join(":",Ѫ.Skip(È+1));var Ѹ=new Ѣ();Ѹ.Ѥ.AddRange(ѷ.Split('|'));foreach(var ѹ in Ѫ[È].Split(',','&'))
{var ɜ=ѹ.IndexOfAny(Ѩ);var ɡ=ѹ.Substring(0,ɜ);var Ƞ=ѹ.Substring(ɜ,1);var ɒ=ѹ.Substring(ɜ+1,ѹ.Length-ɜ-1);Func<double,
double,bool>ѻ=(Ř,Ą)=>Ř==Ą;if(Ƞ=="<")ѻ=(Ř,Ą)=>Ř<Ą;if(Ƞ==">")ѻ=(Ř,Ą)=>Ř>Ą;float Ƹ=0;float.TryParse(ɒ,out Ƹ);if(ɡ=="targetType")Ѹ
.ѣ.Add(Ⱥ=>Ⱥ.Μ==(MyDetectedEntityType)Enum.Parse(typeof(MyDetectedEntityType),ɒ));if(ɡ=="rnd")Ѹ.ѣ.Add(Ⱥ=>ѻ(ѡ.NextDouble(),
Ƹ));if(ɡ=="targetSize")Ѹ.ѣ.Add(Ⱥ=>ѻ(Ⱥ.Κ.Value.Extents.Length(),Ƹ));if(ɡ=="distance")Ѹ.ѣ.Add(Ⱥ=>Ⱥ.Η.HasValue&&ѻ((ş.ʆ-Ⱥ.Η.
Value).Length(),Ƹ));if(ɡ=="rc-distance")Ѹ.ѣ.Add(Ⱥ=>{var Ǧ=ş.Ɖ.Ѭ(ş.ʆ+(Ⱥ.Η.Value-ş.ʆ).Normalized()*(Ƹ+1));return Ǧ.HasValue&&ѻ(
(ş.ʆ-Ǧ.Value).Length(),Ƹ);});if(ɡ=="dot")Ѹ.ѣ.Add(Ⱥ=>Ⱥ.Η.HasValue&&ѻ(ş.ų.ŏ,Ƹ));if(ɡ=="targetVelocity")Ѹ.ѣ.Add(Ⱥ=>Ⱥ.Ő.
HasValue&&ѻ(Ⱥ.Ő.Value.Length(),Ƹ));if(ɡ=="blockEnabled"){var Ą=ş.ش.Ā.FirstOrDefault(Ë=>Ë.CustomName.Contains(ɒ));if(Ą!=null)Ѹ.ѣ.
Add(Ⱥ=>Ą.Enabled);}if(ɡ=="taskElapsedTicks")Ѹ.ѣ.Add(Ⱥ=>ş.Ɖ.ѽ()!=null&&ʓ.Ų-ş.Ɖ.ѽ().ڗ>Ƹ);if(ɡ=="custom-val")Ѹ.ѣ.Add(Ⱥ=>ѻ(ş.ـ.
Get<float>(ɡ),Ƹ));if(ɡ=="toggle")Ѹ.ѣ.Add(Ⱥ=>ş.D.Ӱ(ɒ));if(ɡ=="alt")Ѹ.ѣ.Add(Ⱥ=>ş.ų.ĳ.HasValue&&ѻ(ş.ų.ĳ.Value,Ƹ));if(ɡ=="ng")Ѹ
.ѣ.Add(Ⱥ=>ѻ(ş.ų.Ĳ?.Length()??0,Ƹ));if(ɡ=="climb-rate")Ѹ.ѣ.Add(Ⱥ=>ş.ų.Ĳ.HasValue&&ѻ(Vector3D.Dot(ş.ų.Ő,-ş.ų.Ń),Ƹ));}Ѹ.ѥ=ˠ;
Ѹ.Ѧ=Ѷ;if(!Ѡ.ContainsKey(Ç))Ѡ.Add(Ç,new List<Ѣ>());Ѡ[Ç].Add(Ѹ);}public void ѵ(Τ Ⱥ,string Ɓ){if(Ѡ.ContainsKey(Ɓ))foreach(
var Ѹ in Ѡ[Ɓ]){if(ʓ.Ų-Ѹ.ʢ>Ѹ.ѥ){Ѹ.ʢ=ʓ.Ų;if(Ѹ.ѧ(Ⱥ)){Ѹ.ʢ+=Ѹ.Ѧ;ş.տ?.ӥ($"Predicate {Ѹ.ѥ} passed, adding cooldown ({Ѹ.Ѧ})");
foreach(var Ä in Ѹ.Ѥ){ş.տ?.ӥ($"Running command '{Ä}'");ş.Ѥ.Ã(Ä.Replace("{id}",Ⱥ.Ζ.ToString()),ş);}}}}}}struct ӊ{public Vector3D
ʎ;public Vector3D Ӌ;public ؿ ӌ;public ע Ӎ;}struct ӎ{public float?ӏ;public Func<ӊ,ӎ?,Vector3D>D;}class Ӓ{public static
Vector3D Ӑ(ӊ ӑ,ӎ?ê){var ª=ӑ.ӌ;var Ë=ӑ.ʎ;var Ǧ=ê?.ӏ??500d;var ҽ=Ҹ(ӑ);var ӓ=ª.ʆ-Ë;var H=ӓ.Length();ӓ/=H;var A=ª.ʆ;var Ҕ=Vector3D.
Dot(ҽ,ӓ);if(Ҕ>0.7||Ҕ<-0.4){var ъ=Math.Sign(Ҕ)*Vector3D.Cross(Vector3D.Cross(ҽ,ӓ),ӓ).Normalized();A+=ъ*CC_GAIN*0.5;}var Ǌ=ё.
ђ(ӓ,ҽ).Normalized();A+=Ǌ*CC_GAIN;var Υ=((H<Ǧ)?1:-1)*ӓ*Math.Min(CC_GAIN*1.5,Math.Abs(H-Ǧ)*2);A+=Υ;return A;}public static
Vector3D Ӈ(ӊ A,ӎ?ê){var ª=A.ӌ;var Ѷ=ª.ƈ.Ǝ;var Ɩ=new ӎ{ӏ=Ѷ};var Ë=A.ʎ;var Ⱥ=Ë-ª.ʆ;var H=Ⱥ.Length();if(H>Ѷ*1.3){ё.ѕ(ª.ʆ+Ⱥ/H*3,ª.ʆ,
new BoundingSphereD(Ë,Ѷ),ref Ë);A.ʎ=Ë;}else{A.ʎ=Ӑ(A,Ɩ);A.ʎ=Ҽ(A.ʎ,Ҹ(A)*CC_GAIN*0.5);}A.ʎ=ӈ(A,Ɩ);return A.ʎ;}public static
Vector3D ӈ(ӊ A,ӎ?ê){var Ĵ=A.ӌ.ų.ĳ;if(Ĵ<100){return A.ʎ-A.ӌ.ų.Ń*(2-Ĵ.Value/100)*CC_GAIN;}return A.ʎ;}public static Vector3D Ӕ(ӊ A
,ӎ?ê){var ª=A.ӌ;var Ӛ=ª.ų.Ő;var ӛ=ª.ʆ;ª.ƈ.Ȑ(A.ʎ);var ǐ=ª.ƈ.ȡ(A.ʎ,A.Ӌ,Ӛ,ª.ų.Ĳ??Vector3D.Zero);var Ӝ=ª.ƈ.ȟ();if(Ӝ.Direction
!=Vector3D.Zero){MatrixD ӝ;var τ=ª.ų.ľ.WorldMatrix.Up;if(Vector3D.ArePerpendicular(ref Ӝ.Direction,ref τ))ӝ=MatrixD.
CreateFromDir(Ӝ.Direction,ª.ų.ľ.WorldMatrix.Up);else ӝ=MatrixD.CreateFromDir(Ӝ.Direction,ª.ų.ľ.WorldMatrix.Forward);ӝ.Translation=Ӝ.
Position;A.Ӎ.צ=()=>ӝ;}else A.Ӎ.צ=null;if(ª.ɷ.ɯ.ɺ.Σ()){var Ӟ=new RayD(ӛ,(ǐ-ӛ).Normalized());var Ӂ=ª.ɷ.ɯ.ɺ.Κ.Value;var ь=new
BoundingSphereD(Ӂ.Min,Ӂ.Max.X);if(!Ӟ.Intersects(ь).HasValue)ª.ƈ.ç(ǐ);}else ª.ƈ.ç(ǐ);return ǐ;}public static Vector3D ӟ(ӊ A,ӎ?ê){var ª=A
.ӌ;if(!ª.Ƅ())return A.ʎ;var Ӡ=ª.ʆ;var ȼ=ª.ų.Ń;var ӡ=A.ʎ-Ӡ;var ә=Vector3D.ProjectOnPlane(ref ӡ,ref ȼ);var É=ª.ų.ľ.
WorldMatrix.Forward;var Ҕ=Vector3D.Dot(ӡ.Normalized(),É);if(Ҕ<0)ә=Vector3D.ProjectOnPlane(ref ә,ref É);return Ӡ+ә;}public static
Vector3D Ĳ(ӊ A,ӎ?ê){if(A.ӌ.Ƅ())return-A.ӌ.ų.Ń;else return Vector3D.Zero;}public static Vector3D ӕ(ӊ A,ӎ?ê){return A.ӌ.ʆ+Ĳ(A,ê)*(
ê?.ӏ??1f);}public static Vector3D Ӗ(ӊ A,ӎ?ê){return A.ӌ.ʆ+ӗ(A,ê);}public static Vector3D ӗ(ӊ A,ӎ?ê){var ª=A.ӌ;var Ә=ê?.ӏ
??1f;if(ª.Ƅ()){var ҿ=ª.ų.Ĳ.Value;if(ª.ų.Ő.LengthSquared()>0&&A.ʎ!=ª.ʆ){var ˡ=Vector3D.Reject(ª.ų.Ő,(A.ʎ-ª.ʆ).Normalized())
/5f*Ә;return-(ҿ+ˡ).Normalized();}return-ҿ;}else return Vector3D.Zero;}public static Vector3D Ӊ(ӊ A,ӎ?ê){return A.ʎ-(A.ʎ-A
.ӌ.ʆ).Normalized()*(ê?.ӏ??1f);}public static Vector3D Ҵ(ӊ A,ӎ?ê){return A.ӌ.Ƅ()?A.ʎ-A.ӌ.ų.Ń*(ê?.ӏ??1f):A.ʎ;}public static
Vector3D ҵ(Vector3D Ҷ,float ъ,ؿ ª){return ª.Ƅ()?Ҷ-ª.ų.Ń*ъ:Ҷ;}public static Vector3D Ҹ(ӊ A){return A.ӌ.Ƅ()?-A.ӌ.ų.Ń:(A.Ӌ.
LengthSquared()>20*20?A.Ӌ.Normalized():Vector3D.UnitY);}public static Vector3D ҹ(ӊ A,ӎ?ê){var Һ=A.Ӎ.ا;if(Һ?.Ι.HasValue==true){var Ⱥ=(
A.ʎ-A.ӌ.ʆ).Normalized();var Ę=Һ.Ι.Value.Left;if(Math.Abs(Vector3D.Dot(Ⱥ,Һ.Ι.Value.Up))<Math.Abs(Vector3D.Dot(Ⱥ,Ę)))Ę=Һ.Ι.
Value.Up;if(Math.Abs(Vector3D.Dot(Ⱥ,Һ.Ι.Value.Forward))<Math.Abs(Vector3D.Dot(Ⱥ,Ę)))Ę=Һ.Ι.Value.Forward;if(ê?.ӏ==null&&Һ.Κ.
HasValue)ê=new ӎ{ӏ=(float)Һ.Κ.Value.HalfExtents.Length()/2f};return A.ʎ+һ(Ę,ê);}return A.ʎ;}static Vector3D һ(Vector3D Ę,ӎ?ê){
return Ę*Math.Sin(ʓ.D/3)*(ê?.ӏ??30);}public static Vector3D Ү(ӊ A,ӎ?ê){return A.ʎ+һ(A.ӌ.ų.ľ.WorldMatrix.Left,ê);}public static
Vector3D ү(ӊ A,ӎ?ê){return A.ʎ+һ(A.ӌ.ų.ľ.WorldMatrix.Up,ê);}static Vector3D Ұ(ؿ ª,Vector3D M,float Ř,Vector3D Ę){return M+Ę*(M-ª
.ʆ).Length()*Math.Tan(Ř);}public static Vector3D ұ(ӊ A,ӎ?ê){return Ұ(A.ӌ,A.ʎ,ê?.ӏ??0,A.ӌ.ų.ľ.WorldMatrix.Up);}public
static Vector3D Ҳ(ӊ A,ӎ?ê){return Ұ(A.ӌ,A.ʎ,ê?.ӏ??0,A.ӌ.ų.ľ.WorldMatrix.Left);}public static Vector3D ҷ(ӊ A,ӎ?ê){return A.Ӌ==
Vector3D.Zero?A.ʎ:Ұ(A.ӌ,A.ʎ,ê?.ӏ??0,A.Ӌ.Normalized());}public static Vector3D Ӄ(ӊ A,ӎ?ê){return!A.ӌ.Ƅ()?A.ʎ:Ұ(A.ӌ,A.ʎ,ê?.ӏ??0,A.
ӌ.ų.Ń);}static Vector3D ӄ(Vector3D M,Vector3D ȓ,float ӂ){var Ї=Vector3D.CalculatePerpendicularVector(ȓ);var Ë=Vector3D.
Cross(ȓ,Ї);return M+Ë*ӂ*Math.Cos(Math.PI*(ʓ.D/2f))+Ї*ӂ*Math.Sin(Math.PI*(ʓ.D/2f));}public static Vector3D ӆ(ӊ A,ӎ?ê){return ӄ
(A.ʎ,(A.ʎ-A.ӌ.ʆ).Normalized(),ê?.ӏ??30);}public static Vector3D Ӆ(ӊ A,ӎ?ê){var Ⱥ=A.ʎ-A.ӌ.ʆ;var H=Ⱥ.Length();var ȓ=Ⱥ/H;var
ӂ=ê?.ӏ??30;if(H>50)return ӄ(A.ӌ.ʆ+ȓ*100,ȓ,ӂ);return A.ʎ;}public static Vector3D Ҽ(Vector3D ȑ,Vector3D ҽ){return ȑ+ҽ*Math.
Sin(ʓ.D/2);}public static Vector3D Ҿ(Vector3D Ë,ؿ ª,ע Ą){var ҿ=ª.ų.Ń;var Ⱥ=Ë-ª.ʆ;var щ=Vector3D.ProjectOnPlane(ref Ⱥ,ref ҿ)
;ª.վ?.ӥ($"bearing: {щ.Length()}");double ъ;ª.س.TryGetPlanetElevation(MyPlanetElevation.Sealevel,out ъ);if(ъ<2000)return ª
.ʆ-ҿ*1000+щ.Normalized()*500;return ª.ʆ+щ;}public static Vector3D Ӏ(Vector3D Ë,ؿ ª,ע Ą){if(ª.ɷ.ɯ.ɺ.Σ()&&ª.D.Ӱ(
"avoid-carrier")){var Ӂ=ª.ɷ.ɯ.ɺ.Κ.Value;var ь=new BoundingSphereD(Ӂ.Min,Ӂ.Max.X);return ё.ы(Ë,ª.ʆ,ь);}return Ë;}public static Vector3D
Ҏ(Vector3D Ë,ؿ ª,ע Ą){if(!ª.D.Ӱ("allow-1t-up-shifter"))Ą.ץ=null;var π=(Ą.ק!=null)?Ą.ק():ª.ʆ;return π-ª.ų.ϖ(Ë-π,Ą.ר??
MAX_SP,Ą.ש,Ą.ؾ?.Ő??Vector3D.Zero);}public static Vector3D ڐ(ӊ A,ӎ?ê){return A.ʎ;}}abstract class ڑ{public string Ț;public
string ſ{get;protected set;}public int ʍ{get;private set;}public bool ژ;public bool ڒ;public Vector3D?ʆ;public int?ړ;public ע
ڔ;public Action ڕ;public Action ږ;public int ڗ;bool ڙ;protected ؿ ş;protected ڑ(string Ɓ,ؿ ª,bool ڈ=true){ſ=Ɓ;ş=ª;Ț=Ɓ;ڔ=
new ע();ڙ=ڈ;}public void ډ(int J){ʍ=J;}public virtual void ڊ(Τ ڋ,Τ ڌ){ڔ.ا=ڋ;ڔ.ؾ=ڌ;}public abstract bool ʃ(int Ų,ğ ų);public
bool ڍ(int Ų,ğ ų){if(ړ.HasValue&&(Ų-ڗ>ړ))return true;return ʃ(Ų,ų);}bool ڎ;public void ڏ(ؿ ª,int Ų){if((ª.ط?.Status==
MyShipConnectorStatus.Connected)&&ڙ){ª.տ?.ӥ($"{Ț}: forcing depart");ª.Ɖ.Ҩ(ڔ.ؾ);return;}if(ª.ة?.IsLocked==true){ª.ة.Unlock();ª.ų.ģ(PState.WP);
}if(ڗ==0)ڗ=Ų;ª.ش.Ă(ſ+".OnStart");if(!ڎ){ڎ=true;ª.տ?.ӥ($"Starting task {ſ}-{ʍ}");ږ?.Invoke();}else ª.տ?.ӥ(
$"Resuming task {ſ}-{ʍ}");}}class ڛ:ڑ{Func<bool>ҁ;public ڛ(ؿ ª,int ʚ=0):base(ʚ==0?"wait-for-signal":$"wait-{ʚ}t",ª,false){if(ʚ!=0)ړ=ʚ;ڔ.Ț="Wait"
;}public ڛ(ؿ ª,Func<bool>ǉ,int ʚ=0):base("wait-for-condition",ª,false){if(ʚ!=0)ړ=ʚ;ڔ.Ț="Wait";ҁ=ǉ;}public override bool ʃ
(int Ų,ğ ų){return ړ.HasValue&&(ړ.Value<(Ų-ڗ))||(ҁ?.Invoke()==true);}}class ڜ:ڑ{public ڜ(Action Ř):base("exec",null,false
){ڕ=Ř;}public override bool ʃ(int Ų,ğ ų){return true;}}class ڝ:ڑ{double?ڞ;public ڝ(Vector3D M,ؿ ª,double?յ,bool ε):base(
"move",ª){if(յ!=null)ڞ=յ*յ;ڔ.ؤ=true;ژ=true;var Ⱥ=new Τ(Ț,null);ږ=()=>Ⱥ.Ρ(M);ʆ=M;ڔ.ױ=true;ڔ.ؾ=Ⱥ;ڔ.ا=Ⱥ;ڔ.ש=ε;if(ε&&յ.HasValue)ڞ=
400;ڔ.ץ=Ӓ.Ĳ;}public override bool ʃ(int Ų,ğ ų){if(!ڞ.HasValue)return false;var ʨ=(ڔ.ק?.Invoke()??ş.ʆ-ڔ.ؾ.Η.Value).
LengthSquared();if(ʨ<100)ڔ.ا=null;return ʨ<ڞ;}}class ښ:ڝ{Vector3D ȼ;Vector3D ҁ;public ښ(Vector3D?ι,ؿ ª,Vector3D?Ę):base(Vector3D.Zero
,ª,0,false){ſ="land";ژ=true;ږ=()=>{if(!ι.HasValue){if(ª.Ƅ()){var ъ=ª.ų.ĳ.Value;ҁ=ª.ʆ+ª.ų.Ń*ъ;}else ª.Ɖ.Ҁ();}else ҁ=ι.
Value;ʆ=ҁ;if(!Ę.HasValue){if(ª.Ƅ())ȼ=ª.ų.Ń;else ª.Ɖ.Ҁ();}else ȼ=Ę.Value;var Ϭ=ҁ-ª.ʆ;var H=Ϭ.Length();if(H>500){if(ª.ـ.Get<
bool>("capital-behavior"))ª.Ɖ.ѿ(new ڝ(ҁ-Ϭ/H*400-ȼ*Ϭ.Length()/4f,ª,50,false));else ª.Ɖ.ѿ(new ڝ(ҁ-Ϭ/H*400-ȼ*Ϭ.Length()/4f,ª,5,
true));}};ڔ.ת=true;ڔ.פ=(A,ê)=>ª.ة.GetPosition()+ȼ*1000;ڔ.ף=(A,ê)=>ҁ;ڔ.ק=()=>ª.ة.GetPosition()+ª.ة.WorldMatrix.Down*(
IsLargeGrid?3.2:0.55);ڔ.צ=()=>{var Ü=MatrixD.CreateFromDir(ª.ة.WorldMatrix.Down);Ü.Translation=ª.ة.WorldMatrix.Translation;return Ü
;};}public override bool ʃ(int Ų,ğ ų){if(ş.ة.IsLocked){ş.ų.ģ(PState.Inert);return true;}return false;}}class ٳ:ڑ{Vector3D
?ٴ;Vector3D ٵ;double ٶ;float ٷ;public ٳ(ؿ ª,float ٸ=0,Vector3D?ٹ=null):base("cruise-fw",ª){ş=ª;ڒ=true;ڔ.װ=true;ژ=true;ٷ=ٸ
;ٴ=ٹ;ڔ.ا=new Τ("",null);ڔ.ا.Ρ(ª.ʆ);ڔ.ؾ=new Τ("",null);ڔ.ؾ.Ρ(ª.ʆ);ږ=()=>{if(ª.Ƅ())ٶ=(ş.ƃ.Value-ş.ʆ).Length();ٵ=ª.ʆ;};ڔ.ץ=Ӓ
.Ĳ;ڔ.ש=true;}public override bool ʃ(int Ų,ğ ų){var ٺ=ş.ʆ;var ٻ=CC_GAIN*10;if(ş.Ƅ())ٺ+=ё.ђ(ş.ų.Ń,ş.ų.ľ.WorldMatrix.Left)*ٻ
;else ٺ+=(ٴ??ş.ų.ľ.WorldMatrix.Forward)*ٻ;var ټ=ٺ;if(ş.ų.ĳ.HasValue){if(ş.ų.ĳ<ٷ){var ٱ=(ٷ-ş.ų.ĳ.Value)/ٷ;ټ+=-ş.ų.Ń*ٻ*ٱ;ş.
վ?.ӥ($"diff: {ٱ}");}else{var ъ=(ş.ƃ.Value-ş.ʆ).Length();if(ъ>ٶ){var ٱ=Math.Min(ъ-ٶ,ٻ);ټ+=ş.ų.Ń*ٻ*Math.Sqrt(ٱ)/150;}}ş.վ?.
ӥ($"elevCap: {ٶ:f1}\nelev: {(ş.ƃ.Value-ş.ʆ).Length():f1}");}ڔ.ؾ.Ρ(ټ);ڔ.ا.Ρ(ٺ);return false;}}class و:ڑ{float ى;protected
ڷ Ќ;IMyShipMergeBlock ي;public و(ؿ ª,ڷ ƨ,Τ Ⱥ,IMyShipMergeBlock Ą):base("jab",ª){if(ƨ!=null){Ќ=ƨ;ږ=()=>ƨ.ڰ(ª,null,0);ڕ+=()
=>ƨ.ڱ();}var ٮ=ƨ?.ɺ??Ⱥ??new Τ("dumb jab",null);ږ+=()=>{Vector3D M=ª.ų.ύ(ى);if(Ą!=null){ڔ.צ=()=>{var Ü=IsLargeGrid?MatrixD.
CreateFromDir(Ą.WorldMatrix.Right,Ą.WorldMatrix.Up):MatrixD.CreateFromDir(Ą.WorldMatrix.Up,Ą.WorldMatrix.Right);Ü.Translation=Ą.
WorldMatrix.Translation;return Ü;};M=Ą.WorldMatrix.Translation+ڔ.צ().Forward*ى;}if(!ٮ.Η.HasValue){ٮ.Ρ(M);}};ي=Ą;ڔ.ؾ=ٮ;ڔ.ا=ٮ;ى=1000;
ڔ.ש=true;}int ٯ;public override bool ʃ(int Ų,ğ ų){if(ڔ.ا.Σ()){if(ų.ŏ>0.9){var Ü=Vector3D.Dot((ڔ.צ?.Invoke()??ų.ľ.
WorldMatrix).Forward,ų.Ő);var H=(ڔ.ا.Η.Value-ş.ʆ).Length();if((ٯ==0)&&(Ü>MAX_SP-0.2)&&ş.ƈ.Ȁ((float)(H/Ü)+ş.ـ.Get<float>(
"torpedo-fuse-offset"),ڔ.ا.Ζ,ي)){ٯ=1;ڗ=Ų;ڔ.ף=(A,ê)=>A.ӌ.ʆ+(ڔ.צ?.Invoke()??ų.ľ.WorldMatrix).Forward*(-150);ş.տ?.ӥ($"jabe release at tick {ڗ}")
;}}}if((ٯ==0)&&(Ų-ڗ>120))return true;if((ٯ==1)&&(Ų-ڗ>60))return true;return false;}}class چ:ڑ{ڷ ڇ;void Ɣ(Τ Ⱥ,ؿ ª){ڔ.ؤ=
true;ş=ª;ڔ.ا=Ⱥ;ڔ.ؾ=Ⱥ;ڔ.ש=false;ڔ.ץ=Ӓ.ӗ;ڔ.פ=Ӓ.Ӕ;ڕ+=()=>ª.ƈ.Ɨ();}public چ(ؿ ª,Vector3D M):base("attack",ª){var Ⱥ=new Τ(
"dumb attack",null);Ɣ(Ⱥ,ª);ږ=()=>{Ⱥ.Ρ(M);ڔ.ף=(A,ê)=>{var Ѷ=ª.ƈ.Ǝ;var Ë=A.ʎ;A.ʎ=Ӓ.ҵ(A.ʎ,Ѷ/2,ª);var Ǧ=Ë==A.ʎ?Ѷ:Ѷ*0.86f;var Ɩ=new ӎ{ӏ=Ǧ}
;A.ʎ=Ӓ.Ӑ(A,Ɩ);A.ʎ=Ӓ.ӈ(A,Ɩ);return Ӓ.Ҽ(A.ʎ,Ӓ.Ҹ(A)*0.5*CC_GAIN);};};}public چ(ؿ ª,Τ Ⱥ):base("attack",ª){Ɣ(Ⱥ,ª);ڔ.ף=Ӓ.Ӈ;}
public چ(ؿ ª,ڷ ٽ):base("attack",ª){ڇ=ٽ;ږ=()=>ٽ.ڰ(ª,null,0);ڕ=()=>ٽ.ڱ();Ɣ(ٽ.ɺ,ª);ڔ.ף=Ӓ.Ӈ;}public override bool ʃ(int Ų,ğ ų){if(
ڔ.ا.Σ())return false;if(ڔ.ا.ϫ())return true;return false;}}class پ:ڑ{List<Vector3D>ٿ;int ڀ;Vector3D ҁ;float ځ;public پ(ؿ
ª,List<Vector3D>ß,bool Þ,long ڂ):base("dpath",ª,false){ٿ=ß;ځ=ª.ـ.Get<float>("dpath-speed-limit");var ڃ=ª.ر.ճ("docking");
var ڄ=ª.ر.է(ڂ);ږ=()=>{ڄ.ڰ(ª,ڂ,ڂ);ڃ.ډ(ª);if(Þ){ª.ط.Disconnect();ª.ų.ģ(PState.WP);}};ڕ=()=>{ڄ.ڱ();ڃ.ڱ();if(Þ)ª.Ɖ.ҥ(ڂ);};var ٲ
=ڃ.ɺ;ڔ.ا=ٲ;ڔ.פ=(A,ê)=>ª.ط.GetPosition()-ٲ.Ι.Value.Forward*10000;ڔ.צ=()=>ª.ط.WorldMatrix;ڔ.ק=()=>ª.ط.GetPosition();ڔ.ץ=(A,
ê)=>ٲ.Ι?.Up??Vector3D.Zero;ڔ.ؾ=ڄ.ɺ;ڔ.ף=(A,ê)=>ҁ;}public override bool ʃ(int Ų,ğ ų){var Ȯ=ڔ.ؾ.Ι;if(Ȯ.HasValue){ҁ=Vector3D.
Transform(ٿ[ڀ],Ȯ.Value);if((ş.ط.GetPosition()-ҁ).LengthSquared()<3){if((ځ>0)&&(ڀ!=ٿ.Count-1))ڔ.ר=ځ;if(++ڀ==ٿ.Count)return true;}}
else ҁ=ş.ʆ;return false;}}class ۄ:ڑ{Vector3D?ҁ;Vector3D?ۈ;MatrixD?ۅ;Vector3D?ۆ;IMySensorBlock ۇ;public ۄ(ؿ ª,IMySensorBlock
Ì,Vector3D?Ę):base("follow-playa",ª){ۇ=Ì;ş=ª;ۆ=Ę;Ɣ(new Τ("sensor",null));}public ۄ(ؿ ª,long J,Vector3D?M=null):base(
"follow",ª){ş=ª;ҁ=M;var ƨ=ª.ر.է(J);Ɣ(ƨ.ɺ);ږ=()=>ƨ.ڰ(ª,J,J);ڕ=()=>ƨ.ڱ();}void Ɣ(Τ Ⱥ){ژ=true;ڒ=true;ڔ.Ț="follow";ڔ.ף=(A,ê)=>ۅ.
HasValue?Vector3D.Transform(ۈ.Value,ۅ.Value):A.ӌ.ʆ;ڔ.פ=(A,ê)=>ۅ.HasValue?A.ӌ.ʆ+(ۆ.HasValue?Vector3D.Rotate(ۆ.Value,ۅ.Value):ۅ.
Value.Forward):A.ӌ.ų.ύ(100);ڔ.ץ=(A,ê)=>ۅ.HasValue?ۅ.Value.Up:A.ӌ.ų.ľ.WorldMatrix.Up;ڔ.ؾ=Ⱥ;ڔ.ا=Ⱥ;}public override bool ʃ(int Ų
,ğ ų){if(ۇ!=null){var ۂ=ۇ.LastDetectedEntity;if(!ۂ.IsEmpty()){ڔ.ؾ.Ρ(ۂ.Position);ڔ.ؾ.Ő=ۂ.Velocity;var Ȯ=ۂ.Orientation;Ȯ.
Translation=ۂ.Position;ڔ.ؾ.Ι=Ȯ;ڔ.ا=ڔ.ؾ;}}var π=ڔ.ק?.Invoke()??ş.œ.GetPosition();if(ڔ.ؾ.Ι.HasValue){ۅ=ڔ.ؾ.Ι;if(!ۈ.HasValue){ۈ=ҁ??
Vector3D.Rotate(π-ۅ.Value.Translation,MatrixD.Transpose(ۅ.Value));}}else ۅ=null;return false;}}class ۃ:ڑ{public ۃ(ؿ ª,Τ ۋ):base(
"wingman",ª){ڒ=true;ڔ.ؤ=true;ژ=true;var Ⱥ=ۋ;ڔ.ا=ۋ;ڔ.ؾ=ۋ;ڔ.פ=(A,ê)=>A.ʎ+Ⱥ.Ι.Value.Forward*5000;ڔ.ץ=(A,ê)=>Ⱥ.Ι?.Up??Vector3D.Zero;}
public override bool ʃ(int Ų,ğ ų){return false;}}class ۊ:ڑ{public ۊ(ؿ ª,Vector3D M,Vector3D τ):base("docking",ª){var Ⱥ=new Τ(
"dumb dock",null);Ⱥ.Ρ(M);var Ü=MatrixD.CreateFromDir(τ);Ü.Translation=M;Ⱥ.Ι=Ü;Ɣ(Ⱥ);}public ۊ(ؿ ª,long Ȃ,ڷ ٽ):base("docking",ª){Ɣ(ٽ.
ɺ);ږ=()=>{ٽ.ډ(ª);ş.տ?.ӥ($"Sending docking request to {Ȃ}");ª.Ѥ.C("apck.docking.request",ª.ط.GetPosition(),Ȃ);};ڕ=()=>{ª.Ѥ
.C("apck.dpath.complete",ª.ط.GetPosition(),Ȃ);ٽ.ڱ();};}void Ɣ(Τ Ⱥ){ڔ.ؾ=Ⱥ;ڔ.ا=Ⱥ;ڔ.פ=(A,ê)=>ş.ط.GetPosition()-Ⱥ.Ι.Value.
Forward*10000;ڔ.ף=(A,ê)=>A.ʎ+Ⱥ.Ι.Value.Forward*(IsLargeGrid?1.25f:0.5f);ڔ.צ=()=>ş.ط.WorldMatrix;ڔ.ק=()=>ş.ط.GetPosition();ڔ.ץ=(
A,ê)=>Ⱥ.Ι?.Up??Vector3D.Zero;}public override bool ʃ(int Ų,ğ ų){if(ڔ.ؾ.Σ()){var H=(ڔ.ק()-ڔ.ؾ.Η.Value).LengthSquared();if(
(H<20)&&(ų.Ŏ.Length()<0.8)&&(ş.ط!=null)){ş.ط.Connect();if(ş.ط.Status==MyShipConnectorStatus.Connected){ş.ų.ģ(PState.Inert
);ş.ų.ĸ.DampenersOverride=false;return true;}}}return false;}}class ۉ:ڑ{public ۉ(ؿ ª):base("maintenance",ª,false){ڒ=true;
ږ=()=>{ª.ت.ForEach(Ą=>Ą.ChargeMode=ChargeMode.Recharge);ª.ث.ForEach(Ą=>Ą.Stockpile=true);ª.ų.ģ(PState.Inert);};ڕ=()=>{ª.ت
.ForEach(Ą=>Ą.ChargeMode=ChargeMode.Auto);ª.ث.ForEach(Ą=>Ą.Stockpile=false);ª.ų.ģ(PState.WP);};}public override bool ʃ(
int Ų,ğ ų){return false;}}class ہ:ڑ{Dictionary<string,long>ڤ;Dictionary<string,long>ڥ=new Dictionary<string,long>();List<
IMyInventory>ڦ;List<IMyInventory>ڧ;public ہ(ؿ ª,Dictionary<string,string>Ɇ):base("cargo",ª,false){ږ=()=>{if(ª.ط.OtherConnector!=null
){ک(true);ک(false);ڤ=new Dictionary<string,long>();foreach(var ڨ in Ɇ){int Ł;if(int.TryParse(ڨ.Value,out Ł))ڤ.Add(ڨ.Key,Ł
);}foreach(var H in ڤ)ş.տ?.ӥ($"Desired: {H.Key}: {H.Value}");}};}void ک(bool ڪ){var ګ=ş.ط.GetInventory(0);var O=new List<
IMyCargoContainer>();ş.د.GetBlocksOfType(O,Ą=>Ą.IsSameConstructAs(ڪ?ş.ط:ş.ط.OtherConnector)&&Ą.HasInventory&&(Ą is IMyCargoContainer));
var ڬ=O.Select(Ë=>Ë.GetInventory(0)).Where(Ë=>Ë.IsConnectedTo(ګ)).OrderByDescending(Ë=>(int)Ë.MaxVolume).ToList();if(ڪ)ڦ=ڬ;
else ڧ=ڬ;}List<MyInventoryItem>ڟ=new List<MyInventoryItem>();void ڣ(bool ڠ,string F,int ڡ){var Ý=ڠ?ڦ:ڧ;var і=ڠ?ڧ:ڦ;foreach(
var H in Ý){ڟ.Clear();H.GetItems(ڟ);foreach(var Ë in ڟ){if(Ë.Type.ToString().Contains(F)){if(ڠ)ڡ*=-1;if(ڡ>0){if(H.
TransferItemTo(і.OrderByDescending(Ĵ=>(int)Ĵ.MaxVolume).First(),Ë,ڡ)){ş.տ?.ӥ($"{(ڠ?"Pushed":"Pulled")} {Ë.Type}: {ڡ}");return;}}}}}}
public override bool ʃ(int Ų,ğ ų){ڥ.Clear();foreach(var H in ڤ)ڥ.Add(H.Key,0);foreach(var H in ڦ){List<MyInventoryItem>ڢ=new
List<MyInventoryItem>();H.GetItems(ڢ);foreach(var Ë in ڢ){var F=ڥ.Keys.FirstOrDefault(Ç=>Ë.Type.ToString().Contains(Ç));if(F
!=null)ڥ[F]+=Ë.Amount.RawValue/1000000;}}bool Ǧ=false;foreach(var H in ڤ){var ٱ=H.Value-ڥ[H.Key];if(ٱ!=0){Ǧ=true;ڣ(ٱ<0,H.
Key,(int)ٱ);}}if(Ǧ)ş.վ?.ӥ("Working with cargo");return!Ǧ;}}class ڷ{public string ڸ;public Τ ɺ;public int ڹ;public Action<
MyIGCMessage,Τ>ں;public long ڻ;public Func<Τ,bool>ڼ;public bool ڽ;ڲ ۀ;public ؿ ӌ;public ڷ(string ɡ,bool ھ,ڲ ڿ,Func<Τ,bool>ǉ=null){ڽ=
ھ;ڸ=ɡ;ڼ=ǉ;ɺ=new Τ(ɡ+"--listener",60);ں=(Ý,Ⱥ)=>{var ʊ=(MyTuple<MyTuple<string,long,byte,byte>,Vector3D,Vector3D,MatrixD,
BoundingBoxD>)Ý.Data;Ⱥ.и(Ý.Source,ʊ,ʓ.Ų);};ۀ=ڿ;}public ڷ ڭ(Action<MyIGCMessage,Τ>ڵ){ں=ڵ;return this;}int ڮ;Action گ;public void ډ(ؿ
ª,Action ҡ=null){ۀ.Ֆ(this);ڮ++;گ=ҡ;ӌ=ª;ۀ.բ(this);}public void ڰ(ؿ ª,long?Թ,long к,Action ҡ=null){ڻ=Թ??ڻ;ډ(ª,ҡ);if(к!=0)ª.
ذ.SendUnicastMessage(к,"apck.unicast.t+",new MyTuple<string,long>(ڸ,ڻ));else{if(!ۀ.ա(ڸ,ڻ))ۀ.Ծ(this);else ʓ.ʗ(
$"Already subscribed to unicast for DataId {ڻ}");}}public void Ƈ(){ӌ?.տ?.ӥ($"IgcL '{ɺ.Ț}' Finalizer");while(ڮ>0)ڱ();}public void ڱ(){if(ڮ>0){ۀ.գ(ڸ);if(--ڮ==0){ۀ.Օ(this
);ӌ?.տ?.ӥ($"IgcL '{ɺ.Ț}' kill");if(!ۀ.ա(ڸ,ڻ))گ?.Invoke();}}}}class ڲ{List<ڷ>ڳ=new List<ڷ>();class ڴ{public string ڶ;
public int ه;public List<MyIGCMessage>ե=new List<MyIGCMessage>();public IMyBroadcastListener ջ;}Dictionary<string,ڴ>զ=new
Dictionary<string,ڴ>();IMyIntergridCommunicationSystem Ĺ;public ڲ(IMyIntergridCommunicationSystem ć){Ĺ=ć;}public void Ԝ(List<
MyIGCMessage>Ù,ؿ ª){foreach(var ƨ in զ.Values){ƨ.ե.Clear();if(ƨ.ջ!=null){while(ƨ.ջ.HasPendingMessage)ƨ.ե.Add(ƨ.ջ.AcceptMessage());}
else{foreach(var Ü in Ù){if(Ü.Tag==ƨ.ڶ)ƨ.ե.Add(Ü);}}ª.վ?.ӥ($"CHNL:{ƨ.ڶ}({ƨ.ե.Count})");}foreach(var ƨ in ڳ){if(!զ.
ContainsKey(ƨ.ڸ))ª.վ?.ӥ($"{ƨ.ڸ}(CHANNEL ERROR!)");else{if(ƨ.ں==null)continue;var ը=զ[ƨ.ڸ];foreach(var À in ը.ե){ƨ.ں(À,ƨ.ɺ);ʓ.ʕ?.
Invoke(!(ƨ.ڼ==null&&!ƨ.ڽ),"No predicate in uni que");if((ƨ.ڼ==null)||ƨ.ڼ(ƨ.ɺ)){ƨ.ڹ=ʓ.Ų;break;}}}}ձ.Clear();if(դ.Count>0){ڳ.
AddRange(դ);դ.Clear();}if(ի.Count>0){ڳ.RemoveAll(Ë=>ի.Contains(Ë));ի.Clear();}}public void թ(ڷ ƨ,ref List<MyIGCMessage>ժ){ժ.
Clear();if(զ.ContainsKey(ƨ.ڸ))ժ.AddRange(զ[ƨ.ڸ].ե);}List<ڷ>ի=new List<ڷ>();List<ڷ>դ=new List<ڷ>();public void Օ(ڷ ƨ){ի.Add(ƨ)
;}public void Ֆ(ڷ ƨ){if(!ڳ.Contains(ƨ))դ.Add(ƨ);}public void ՙ(ؿ ª){foreach(var ƨ in ڳ.Where(Ë=>Ë.ӌ==ª))ƨ.Ƈ();}public
bool ա(string ɡ,long Թ){return ڳ.Any(Ë=>Ë.ڸ==ɡ&&Ë.ڻ==Թ);}public void բ(ڷ ƨ){var ɡ=ƨ.ڸ;if(!զ.ContainsKey(ɡ)){զ.Add(ɡ,new ڴ(){
ڶ=ɡ,ջ=ƨ.ڽ?Ĺ.RegisterBroadcastListener(ɡ):null,ه=1});}else զ[ɡ].ه++;}public void գ(string ɡ){if(զ.ContainsKey(ɡ)){if(--զ[ɡ
].ه<=0){if(զ[ɡ].ջ!=null)Ĺ.DisableBroadcastListener(զ[ɡ].ջ);else Ĺ.SendBroadcastMessage("apck.unicast.closed",ɡ);զ.Remove(
ɡ);}}}public ڷ է(long к){Action<MyIGCMessage,Τ>A=(Ë,Ⱥ)=>{var ʊ=(MyTuple<MatrixD,Vector3D>)Ë.Data;Ⱥ.Ρ(ʊ.Item1.Translation)
;Ⱥ.Ι=ʊ.Item1;Ⱥ.Ő=ʊ.Item2;Ⱥ.Ο=Ë.Source;};var ƨ=new ڷ("apck-position",false,this,Ë=>true).ڭ(A);ƨ.ڻ=к;return ƨ;}public ڷ կ(
long Ȃ){var ƨ=new ڷ("tgp.local.gridsense.update",false,this,Ë=>Ë.Ζ==Ȃ);ƨ.ڻ=Ȃ;return ƨ;}public ڷ ճ(string հ){return new ڷ(
$"apck.{հ}.update",false,this,Ë=>true);}HashSet<string>ձ=new HashSet<string>();public void Ծ(ڷ ƨ){if(ձ.Add(ƨ.ڸ+ƨ.ڻ))Ĺ.SendBroadcastMessage
($"apck.unicast.whohas",new MyTuple<string,long>(ƨ.ڸ,ƨ.ڻ));}public void ղ(string ɡ,byte մ,string ŉ,Vector3D M,Vector3D Ǌ,
float Ǧ,string լ){Ĺ.SendBroadcastMessage($"apck.unicast.whohas+predicate",new MyTuple<string,string,MyTuple<string,byte,
Vector3D,Vector3D,float>>(ɡ,լ,new MyTuple<string,byte,Vector3D,Vector3D,float>(ŉ,մ,M,Ǌ,Ǧ)));}HashSet<long>խ=new HashSet<long>();
public void ȴ(Ղ ȵ,ؿ ծ,bool ŝ){var Ȫ=new Լ();Ȫ.Հ=(Ý,J)=>խ.Add(Ý);Ȫ.Խ=(Ý,J)=>խ.Remove(Ý);Ȫ.Ծ=(J)=>Ĺ.Me==J;Ȫ.Կ=(ʈ,Ì,M,Ǌ,Ǧ)=>{bool
ʉ=Ղ.Ժ(M,Ǌ,Ǧ,ծ.ʆ);if(ʉ&&(ʈ==null||(ʈ=="Dockable"&&ŝ)||(ծ.Ƶ==ʈ)))return Ĺ.Me;return-1;};Ȫ.Ձ=()=>{foreach(var Ì in խ){var H=
new MyTuple<MatrixD,Vector3D>(ծ.œ.WorldMatrix,ծ.ų.Ő);Ĺ.SendUnicastMessage(Ì,"apck-position",H);}};ȵ.Յ.Add("apck-position",Ȫ
);}}Ղ Ի;class Լ{public Action<long,long>Խ;public Func<long,bool>Ծ;public Func<string,byte,Vector3D,Vector3D,float,long>Կ;
public Action<long,long>Հ;public Action Ձ;}class Ղ{IMyBroadcastListener Ф;IMyBroadcastListener Ճ;IMyBroadcastListener Մ;
IMyIntergridCommunicationSystem Ĺ;public Ղ(IMyIntergridCommunicationSystem È){Ĺ=È;Ф=È.RegisterBroadcastListener("apck.unicast.closed");Ճ=È.
RegisterBroadcastListener("apck.unicast.whohas");Մ=È.RegisterBroadcastListener("apck.unicast.whohas+predicate");}public Dictionary<string,Լ>Յ=new
Dictionary<string,Լ>();public static bool Ժ(Vector3D M,Vector3D Ǌ,float Ǧ,Vector3D Է){bool Ą=false;if(Ǌ!=Vector3D.Zero){if(
Vector3D.Dot(Ǌ,(Է-M).Normalized())>Ǧ)Ą=true;}else{if(M!=Vector3D.Zero){if(Vector3D.DistanceSquared(Է,M)<Ǧ*Ǧ)Ą=true;}else Ą=true;
}return Ą;}public void Ը(List<MyIGCMessage>ˍ){while(Ф.HasPendingMessage){var Ü=Ф.AcceptMessage();var Զ=(string)Ü.Data;if(
Յ.ContainsKey(Զ))Յ[Զ].Խ.Invoke(Ü.Source,0);}while(Ճ.HasPendingMessage){var Ü=Ճ.AcceptMessage();var H=(MyTuple<string,long
>)Ü.Data;if(Յ.ContainsKey(H.Item1)&&Յ[H.Item1].Ծ.Invoke(H.Item2))Ĺ.SendUnicastMessage(Ü.Source,"apck.unicast.ihave",H);}
while(Մ.HasPendingMessage){var Ü=Մ.AcceptMessage();var H=(MyTuple<string,string,MyTuple<string,byte,Vector3D,Vector3D,float>>
)Ü.Data;if(Յ.ContainsKey(H.Item1)&&Յ[H.Item1].Կ!=null){var J=Յ[H.Item1].Կ.Invoke(H.Item3.Item1,H.Item3.Item2,H.Item3.
Item3,H.Item3.Item4,H.Item3.Item5);if(J>0)Ĺ.SendUnicastMessage(Ü.Source,"apck.unicast.ihave+callback",new MyTuple<long,string
>(J,H.Item2));}}foreach(var Ü in ˍ){if(Ü.Tag.Contains("apck.unicast.t")){var H=(MyTuple<string,long>)Ü.Data;var Զ=H.Item1
;var Թ=H.Item2;if(Յ.ContainsKey(Զ)){var Ì=Յ[Զ];if(Ü.Tag=="apck.unicast.t+")Ì.Հ.Invoke(Ü.Source,Թ);else if(Ü.Tag==
"apck.unicast.t-")Ì.Խ.Invoke(Ü.Source,Թ);}}}foreach(var Ë in Յ)Ë.Value.Ձ();}}void Ռ(List<string>ɜ,bool Ս){foreach(var Æ in ɜ){if(Ս&&Æ.
Contains("signal"))continue;Ր(Æ.Split(new[]{':'},StringSplitOptions.RemoveEmptyEntries),ӌ);Ս=false;}}List<string>Վ=new List<
string>();ڑ Տ=new ڛ(null);void Ր(string[]Ǥ,ؿ ª,bool Ց=false,bool Ւ=false){var Ⱥ=Ւ?ª.Ɖ.ѽ()?.ڔ.ؾ:null;var Æ=Ն(Ǥ,ª,Ⱥ);if(Ց)ª.Ɖ.ѿ(
Æ);else{ª.Ɖ.Ѿ(Æ);Վ.Add(string.Join(":",Ǥ));}}void Փ(string[]Ǥ){var Ք=Ǥ[2];if(Ք=="dock"&&(ӌ.ط?.OtherConnector!=null)){if(ӌ
.Ɖ.ԇ.HasValue)Ր($"command:create-task:dock:TargetId={ӌ.Ɖ.ԇ.Value}".Split(':'),ӌ);else if(ɷ.ɩ!=0)Ր(
$"command:create-task:dock:TargetId={ɷ.ɩ}".Split(':'),ӌ);else{var ҩ=ӌ.ط.OtherConnector;var Ü=ҩ.WorldMatrix;var Ę=Ü.Forward;var M=Ü.Translation+Ü.Forward*(ҩ.
CubeGrid.GridSizeEnum==MyCubeSize.Large?1.25:0.5);Ր($"command:create-task:dock:AimNormal={Ę.X};{Ę.Y};{Ę.Z}:{Ъ(M)}".Split(':'),ӌ)
;}}if(Ք=="land"&&(ӌ.ة!=null)){var M=ӌ.ة.GetPosition()+ӌ.ة.WorldMatrix.Down*(IsLargeGrid?3.2:0.55);var Ę=ӌ.ة.WorldMatrix.
Down;var Ä=$"command:create-task:land:AimNormal={Ę.X};{Ę.Y};{Ę.Z}:{Ъ(M)}";Ր(Ä.Split(':'),ӌ);}if(Ք=="move"){var Ä=
$"command:create-task:move:{Ъ(ӌ.ʆ)}";Ր(Ä.Split(':'),ӌ);}}ڑ Ն(string[]Ǥ,ؿ ª,Τ Ⱥ){try{ª.տ?.ӥ($"Parsing task: {string.Join(":",Ǥ)}");ڑ Ғ;var Շ=Ǥ[2];Dictionary<
string,string>Ɉ;Vector3D?Ȫ=null;int Ո=3;if(Ʉ(Ո,Ǥ,out Ɉ))Ո=4;if(Ǥ.Length>4){var Չ=Ǥ.Take(Ո+3).Skip(Ո).ToArray();Ȫ=new Vector3D(
double.Parse(Չ[0]),double.Parse(Չ[1]),double.Parse(Չ[2]));}var Պ=ɇ<string>(Ɉ,"Name");var J=ɇ<long?>(Ɉ,"TargetId")??ª.Ɖ.ԇ??-1;
var Ջ=ɇ<int?>(Ɉ,"Ticks");var յ=ɇ<long?>(Ɉ,"Proximity");var β=ɇ<string>(Ɉ,"FlyThrough");var ء=ɇ<string>(Ɉ,"Circumnavigate");
Vector3D?Ę=null;var آ=ɇ<string>(Ɉ,"AimNormal");if(آ!=null){var Ł=آ.Split(';');Ę=new Vector3D(double.Parse(Ł[0]),double.Parse(Ł[1
]),double.Parse(Ł[2]));}float?ϗ=ɇ<float?>(Ɉ,"SpeedLimit");switch(Շ){case"orbit":var أ=ɇ<int?>(Ɉ,"R")??500;var A=Ȫ??ª.ʆ+ª.
ų.ľ.WorldMatrix.Forward*أ;Ғ=new ڝ(A,ª,null,true);var Ɩ=new ӎ{ӏ=أ};Ғ.ڔ.ף=(Ѹ,ê)=>Ӓ.Ӑ(Ѹ,Ɩ);break;case"wait":Ғ=new ڛ(ª,Ջ??0);
break;case"wait-for-signal":Ғ=new ڛ(ª);Վ.Clear();break;case"exec":Ғ=new ڜ(()=>ª.Ѥ.Ã(ɇ<string>(Ɉ,"FollowUp").Replace(';',':'),
ª));break;case"cruise-fw":Ғ=new ٳ(ª,ɇ<float?>(Ɉ,"R")??0,Ę);break;case"move":Ғ=new ڝ(Ȫ.Value,ª,յ??1,β=="true");break;case
"move-rel":Ғ=new ڝ(Vector3D.Zero,ª,յ??1,β=="true");Ғ.ږ=()=>{var إ=ɇ<string>(Ɉ,"RemapNG")=="true"&&ª.ų.Ĳ.HasValue;var ئ=ª.œ.
WorldMatrix;if(إ){var É=Math.Abs(Vector3D.Dot(ª.ų.Ń,ئ.Left))>.99?ئ.Forward:Vector3D.Cross(ª.ų.Ń,ئ.Left);ئ=MatrixD.CreateFromDir(É,-
ª.ų.Ń);ئ.Translation=ª.ʆ;}var M=Vector3D.Transform(Ȫ.Value,ئ);Ғ.ڔ.ؾ.Ρ(M);Ғ.ʆ=M;var Ř=ª.œ.WorldMatrix.Forward;Ғ.ڔ.פ=(Ѹ,ê)
=>ª.ʆ+Ř*100;};Ғ.ʆ=null;break;case"jab":Ғ=new و(ª,J!=-1?ӌ.ر.կ(J):null,Ⱥ,null);break;case"attack":if(Ȫ.HasValue)Ғ=new چ(ª,Ȫ.
Value);else if(ʿ.ˁ.ʑ&&ʿ.ˁ.ɺ.Ζ==J)Ғ=new چ(ª,ʿ.ˁ.ɺ);else Ғ=new چ(ª,ӌ.ر.կ(J));break;case"follow":if(J!=-1)Ғ=new ۄ(ª,J,Ȫ);else Ғ=
new ۄ(ª,(IMySensorBlock)ª.د.GetBlockWithName("sensor-apck"),Ę);break;case"wingman":if(ª.ł==null)Ғ=new ۃ(ª,ɷ.ɯ.ɺ);else Ғ=new
ۃ(ª,new Τ("su-w",null));break;case"dock":if(ª.ط!=null){if(J!=-1)Ғ=new ۊ(ª,J,ӌ.ر.ճ("docking"));else Ғ=new ۊ(ª,Ȫ.Value,Ę.
Value);}else Ғ=Տ;break;case"maintenance":Ғ=new ۉ(ª);break;case"cargo":if(ª.ط!=null)Ғ=new ہ(ª,Ɉ);else Ғ=Տ;break;case"land":if(
ª.ة!=null)Ғ=new ښ(Ȫ,ª,Ę);else Ғ=Տ;break;default:Ғ=Տ;break;}Ғ.Ț=Պ??Շ;Ғ.ړ=Ջ??Ғ.ړ;Ғ.ڔ.ר=ϗ??Ғ.ڔ.ר;if(β!=null)Ғ.ڔ.ש=β=="true";
if(ء!=null)Ғ.ڔ.ױ=ء=="true";return Ғ;}catch(Exception ex){ʓ.ʗ(ex.ToString());}return null;}static Vector3I[]ؠ={new Vector3I
(1,0,0),new Vector3I(-1,0,0),new Vector3I(0,1,0),new Vector3I(0,-1,0),new Vector3I(0,0,1),new Vector3I(0,0,-1)};static D
נ<D>(IMyCubeBlock Ą)where D:class,IMyTerminalBlock{foreach(var ס in ؠ){var A=Ą.CubeGrid.GetCubeBlock(Ą.Position+ס);if((A
!=null)&&(A.FatBlock is D)&&(A.FatBlock!=Ą))return A.FatBlock as D;}return null;}class ע{public string Ț="Default";public
Func<ӊ,ӎ?,Vector3D>ף;public Func<ӊ,ӎ?,Vector3D>פ;public Func<ӊ,ӎ?,Vector3D>ץ;public Func<MatrixD>צ;public Func<Vector3D>ק;
public float?ר;public bool ש=false;public bool ת;public bool װ;public bool ױ;public bool ؤ;public Τ ا;public Τ ؾ;public
Vector3D?ظ;public Vector3D?Ŀ;public Vector3D ع;public MatrixD?غ;}ؼ ػ;class ؼ{public List<ؿ>ؽ=new List<ؿ>();}ؿ ӌ;class ؿ{public
bool م;public Ć Ѥ;public Variables ـ=new Variables();public ӫ D=new ӫ();public ɐ ف;public List<IMyShipMergeBlock>ق;public
List<IMyShipMergeBlock>ك;public IMyShipMergeBlock ل;public IMyShipMergeBlock ن;public IMyShipConnector ط;public List<
IMyShipConnector>ب;public List<IMyWarhead>ص;public IMyLandingGear ة;public List<IMyBatteryBlock>ت;public List<IMyGasTank>ث;public List<
IMyLargeTurretBase>ج;public List<IMyCameraBlock>ح;public IMyRadioAntenna خ;public IMyGyro œ;public IMyGridTerminalSystem د;public
IMyIntergridCommunicationSystem ذ;public ڲ ر;public ɰ ɷ;public ˀ ز;public IMyRemoteControl س;public ğ ų;public ĕ ش;public IMyProgrammableBlock ض;public
IMyProgrammableBlock ײ;public IMyProgrammableBlock ן;public IMyProgrammableBlock պ;public List<IMyProgrammableBlock>ռ;ؼ ս;public Ӫ վ;public
Ӫ տ;HashSet<IMyTerminalBlock>ր=new HashSet<IMyTerminalBlock>();public Vector3D ʆ{get{return ų.ľ.GetPosition();}}D ց<D>(
string ă,List<IMyTerminalBlock>ւ,bool փ=false)where D:class,IMyTerminalBlock{D Ǧ;վ?.ӥ(
$"Looking for {(փ?"single":"")} '{ă}' ({typeof(D).Name})");var Ƹ=ւ.Where(Ą=>Ą is D&&Ą.CustomName.Contains(ă)).Cast<D>().ToList();Ǧ=փ?Ƹ.Single():Ƹ.FirstOrDefault();if(Ǧ!=null)ր.
Add(Ǧ);return Ǧ;}List<D>ք<D>(List<IMyTerminalBlock>ւ,string Ę=null)where D:class,IMyTerminalBlock{վ?.ӥ(
$"Looking for blocks ({typeof(D).Name})");var Ƹ=ւ.Where(Ą=>Ą is D&&((Ę==null)||Ą.CustomName.Contains(Ę))).Cast<D>().ToList();foreach(var Ą in Ƹ)ր.Add(Ą);return
Ƹ;}void ն<D>(List<D>շ,List<IMyCubeGrid>ƹ,string Ę=null)where D:class,IMyTerminalBlock{var Ì=new List<D>();د.
GetBlocksOfType(Ì,Ë=>ƹ.Contains(Ë.CubeGrid)&&((Ę==null)||Ë.CustomName.Contains(Ę)));շ.AddRange(Ì);}public ؿ(IMyProgrammableBlock х,ʯ ţ,
IMyGridTerminalSystem đ,IMyIntergridCommunicationSystem ć,ڲ ո,ˀ չ,Ć Ä,ؼ օ){foreach(var M in Ч(х)){if(M.Contains("rank"))ƴ=int.Parse(M.Split(
'=')[1]);if(M.Contains("tag"))Ƶ=M.Split('=')[1];}د=đ;ذ=ć;ر=ո;ز=չ;Ѥ=Ä;ս=օ;ɷ=new ɰ(120,150,ƴ,this);Func<IMyTerminalBlock,bool
>Ƹ=Ą=>Ą.CubeGrid==х.CubeGrid;var ה=new HashSet<Vector3I>(ţ.ʰ);bool ו=!ה.Any()||DesignMode;ƽ=new ƾ(х,ה);if(!ו){Ƹ=ƽ.Ƃ;}else
տ=new Ӫ(ʓ.ʗ,this);var ǣ=new List<IMyTerminalBlock>();đ.GetBlocks(ǣ);ǣ=ǣ.Where(Ą=>Ƹ(Ą)).ToList();ƶ(ǣ);if(ו){ƽ.Ƒ.Clear();if
(ن!=null){var ז=new ƾ(ن,new HashSet<Vector3I>());ז.ǃ(ր.ToArray());ţ.ʰ=ז.Ƒ;ن.CustomData=ţ.ToString();}else ƽ.ǃ(ր.ToArray()
);ţ.ʰ=ƽ.Ƒ;ʓ.ʗ($"Subset: {ǣ.Count}");ʓ.ʗ($"Saving defs: {ţ.ʰ.Count}");ţ.ʪ();}տ?.Ӧ();տ=null;}int?ח;public void ט(){if(!ח.
HasValue&&ق.Any())ח=0;}void ם(){if(ח.HasValue){י(ق[ח.Value]);ח++;if(ח==ق.Count)ח=null;}}void י(IMyShipMergeBlock ך){var Ƞ=נ<
IMyShipMergeBlock>(ך);if(Ƞ!=null&&Ƞ.CustomName.Contains("sub-base")&&(Ƞ!=ن)&&!ג.Any(Ë=>Ë.ن==Ƞ)&&!ў.Any(Ë=>Ë.ن==Ƞ)){var כ=new ʯ(null);כ.ʵ(
Ƞ.CustomData);var ל=new HashSet<Vector3I>();var מ=new ƾ(Ƞ,כ.ʰ);տ?.ӥ("sub defs: "+כ.ʰ.Count);Func<IMyTerminalBlock,bool>ד=
מ.Ƃ;var ǣ=new List<IMyTerminalBlock>();د.GetBlocks(ǣ);ǣ=ǣ.Where(Ą=>ד(Ą)).ToList();տ?.ӥ("sub blocks: "+ǣ.Count);try{var B=
new ؿ(this);B.ƶ(ǣ);B.ų.ģ(PState.Inert);ў.Add(B);B.ل=ך;foreach(var M in Ч(ך)){if(M.Contains("tag"))B.Ƶ=M.Split('=')[1];}}
catch(Exception ex){ʓ.ʗ("Sub init failure: "+ex);}}}public void ֆ(ؿ Ë,long?Ȃ,bool և=false){Ë.م=true;ڑ Æ=null;Ë.Ɖ.ԇ=Ë.Ɖ.ԇ??Ȃ;
if(!և){var ι=Ë.ل;foreach(var F in Ч(ι)){if(F=="jab")Æ=new و(this,null,null,ι);if(F=="spin")Æ=new و(this,null,null,ι);}}
Action א=()=>{տ?.ӥ($"sub-unit[{ג.IndexOf(Ë)}] starts");Ë.ن.Enabled=false;Ë.ų.ģ(PState.WP);var Ö=Ë.س.CustomData.Trim('\n').
Split(new[]{'\n'},StringSplitOptions.RemoveEmptyEntries).Where(Ì=>!Ì.StartsWith("//"));foreach(var Ä in Ö)Ѥ.Ã(Ä,Ë);};if(Æ!=
null){Ɖ.ѿ(new ڜ(א));Ɖ.ѿ(new ڜ(()=>Ɖ.ѿ(Æ)));}else א();}public void ב(ؿ Ë){Ë.Ƈ();Ʋ.Add(Ë);}public List<ؿ>ג=new List<ؿ>();
public HashSet<ؿ>ў=new HashSet<ؿ>();public HashSet<ؿ>Ʋ=new HashSet<ؿ>();public ؿ ł;public ɋ Ƴ=ɋ.Ɍ;public int ƴ=int.MaxValue;
public string Ƶ="";public ؿ(ؿ M){ł=M;د=ł.د;ذ=ł.ذ;ز=ł.ز;Ѥ=ł.Ѥ;ɷ=ł.ɷ;ر=ł.ر;ս=ł.ս;}public void ƶ(List<IMyTerminalBlock>Ʒ){var Ƹ=Ʒ
;var Ƽ=ք<IMyMotorStator>(Ƹ);var ƹ=Ƽ.Where(Ë=>Ë.Top!=null).Select(Ë=>Ë.Top.CubeGrid).ToList();տ?.ӥ(
$"Friend subgrids: {ƹ.Count}");ռ=ք<IMyProgrammableBlock>(Ƹ);ն(ռ,ƹ);տ?.ӥ($"PBs: {ռ.Count}");if(ł==null){foreach(var Ą in ռ)Ѥ.C("apck-handshake","",Ą.
EntityId);}œ=ց<IMyGyro>("forward-gyro",Ƹ,true);var ƺ=ք<IMyShipController>(Ƹ);ف=new ɐ(ƺ,D);var ƻ=new List<IMyRadioAntenna>();ƻ=ք<
IMyRadioAntenna>(Ƹ);ն(ƻ,ƹ);خ=ƻ.FirstOrDefault();ة=ք<IMyLandingGear>(Ƹ).FirstOrDefault();ب=new List<IMyShipConnector>();د.
GetBlocksOfType(ب,Ë=>Ë.IsSameConstructAs(œ)&&Ë.CustomName.Contains("dock-host"));ب.ForEach(H=>H.CustomData="");ط=ց<IMyShipConnector>(
"docka",Ƹ);ت=ք<IMyBatteryBlock>(Ƹ);ث=ք<IMyGasTank>(Ƹ);ج=new List<IMyLargeTurretBase>();د.GetBlocksOfType(ج,Ë=>Ë.
IsSameConstructAs(œ)&&Ë.CustomName.Contains("x-designator"));ح=ք<IMyCameraBlock>(Ƹ);ն(ح,ƹ);var Ʃ=new List<IMyCameraBlock>();د.
GetBlocksOfType(Ʃ,Ë=>Ë.IsSameConstructAs(œ));Ʃ.ForEach(Ë=>Ë.EnableRaycast=true);ق=ք<IMyShipMergeBlock>(Ƹ,"sub-unit");ն(ق,ƹ,"sub-unit");
ن=ց<IMyShipMergeBlock>("sub-base",Ƹ);ك=ք<IMyShipMergeBlock>(Ƹ,"dumb-unit");ص=ք<IMyWarhead>(Ƹ);ն(ص,ƹ);س=ք<IMyRemoteControl
>(Ƹ).First();var Ŋ=ք<IMyTimerBlock>(Ƹ);ն(Ŋ,ƹ);ش=new ĕ(Ŋ);Ǎ=new List<IMyTerminalBlock>();Ǎ.AddRange(ք<IMyThrust>(Ƹ));Ǎ.
AddRange(ք<IMyArtificialMassBlock>(Ƹ));if(!string.IsNullOrEmpty(GGEN_GR_TAG)){var ð=new List<IMyGravityGenerator>();var é=د.
GetBlockGroupWithName(GGEN_GR_TAG);if(é!=null)é.GetBlocksOfType(ð,Ą=>Ƹ.Contains(Ą));foreach(var Ą in ð)ր.Add(Ą);Ǎ.AddRange(ð);}else Ǎ.
AddRange(ք<IMyGravityGenerator>(Ƹ));ų=new ğ(س,ذ,œ,Ǆ,this);var Ɛ=ք<IMyUserControllableGun>(Ƹ);ն(Ɛ,ƹ);List<Vector3D>ƪ=new List<
Vector3D>();var ƫ=new List<IMyBlockGroup>();د.GetBlockGroups(ƫ,Ë=>Ë.Name.Contains("[apck-custom]"));var Ƭ=new List<ș>();var ƭ=
new List<IMyTimerBlock>();var Ʈ=new List<IMyUserControllableGun>();foreach(var Ư in ƫ){var ư=new List<IMyTerminalBlock>();Ư
.GetBlocksOfType(ƭ);ư.AddRange(Ŋ.Where(Ë=>ƭ.Contains(Ë)));Ư.GetBlocksOfType(Ʈ);ư.AddRange(Ɛ.Where(Ë=>Ʈ.Contains(Ë)));if(ư
.Any()){Ƭ.Add(new ș(Ư,ŵ.ŷ,ư));ƪ.Add(ư.First().WorldMatrix.Forward);Ɛ.RemoveAll(Ë=>ư.Contains(Ë));}}ƫ.Clear();var ǅ=new
List<IMyUserControllableGun>();د.GetBlockGroups(ƫ,Ë=>Ë.Name.Contains("[apck-fixed]"));var ǆ=ƫ.FirstOrDefault();if(ǆ==null)ǅ.
AddRange(Ɛ.Where(Ë=>!(Ë is IMyLargeTurretBase)));else ǆ.GetBlocksOfType(ǅ,Ë=>Ɛ.Contains(Ë));ƈ=new Ʀ(this);var Ǉ=MatrixD.Identity
;var ǈ=new[]{Ǉ.Forward,Ǉ.Backward,Ǉ.Up,Ǉ.Down,Ǉ.Left,Ǉ.Right};foreach(var H in ǈ){Func<Vector3D,Vector3D,bool>ǉ=(Ë,Ǌ)=>
Vector3D.Dot(Ë,Vector3D.TransformNormal(Ǌ,œ.WorldMatrix))>0.75;var ǋ=ǅ.Where(ê=>ǉ(ê.WorldMatrix.Forward,H)).ToList();var ǌ=ƪ.
Where(Ë=>ǉ(Ë,H));if(ǋ.Any()||ǌ.Any()){ƈ.Ə(ǋ,Ƭ,œ,H);}}ƈ.Ɣ();ز.ˁ.ʁ(this);Ɖ=new Ԁ(this);œ.GyroOverride=false;if(ط?.Status==
MyShipConnectorStatus.Connected)Ɖ.ѿ(new ۉ(this));else ų.ģ(PState.WP);}List<IMyTerminalBlock>Ǎ;С ǎ;int Ǐ;public С Ǆ(){if(ǎ==null){ǎ=new С(œ,Ǎ)
;return ǎ;}else if(ʓ.Ų-Ǐ>120){Ǐ=ʓ.Ų;if(Ǎ.Any(Ë=>!Ë.IsFunctional)){Ǎ.RemoveAll(Ë=>!Ë.IsFunctional);ǎ=new С(œ,Ǎ);}}if(Ǎ.Any
(Ë=>Ë is IMyThrust&&(Ë as IMyThrust)?.MaxEffectiveThrust!=(Ë as IMyThrust)?.MaxThrust))ǎ.ϼ();return ǎ;}ƾ ƽ;class ƾ{public
HashSet<Vector3I>Ƒ;IMyTerminalBlock Ɔ;MatrixD ƿ;public ƾ(IMyTerminalBlock ǀ,HashSet<Vector3I>H){Ɔ=ǀ;ƿ=MatrixD.Transpose(Ɔ.
WorldMatrix);Ƒ=H;}public Vector3I ǁ(IMyCubeBlock Ë){var ǂ=Vector3D.Rotate(Ë.Position-Ɔ.Position,Ɔ.CubeGrid.WorldMatrix);var Ĵ=
Vector3D.Rotate(ǂ,ƿ);return Vector3I.Round(Ĵ);}public void ǃ(params IMyTerminalBlock[]Ą){foreach(var Ë in Ą){var ƨ=ǁ(Ë);Ƒ.Add(ƨ)
;}}public bool Ƃ(IMyTerminalBlock Ą){return Ą.CubeGrid==Ɔ.CubeGrid&&Ƒ.Contains(ǁ(Ą));}}public Vector3D?ƃ;public bool Ƅ(){
if(ų.Ĳ!=null){if(ƃ==null){Vector3D ƅ;if(س.TryGetPlanetPosition(out ƅ)){ƃ=ƅ;return true;}}return ƃ.HasValue;}return false;}
public void ŕ(){try{Ɖ.Ԝ();}catch(Exception ex){ʓ.ʗ($"Cap.HandleTick failure, task: {Ɖ.ѽ()?.Ț}");ʓ.ʗ(ex.ToString());}ע ƀ=Ɖ.Ҍ();
ų.ɢ(ƀ);ƈ.Ď();ם();var A=ג.Count;if(ў.Count>0){ג.AddRange(ў);ս.ؽ.AddRange(ў);տ?.ӥ($"SU add: {A}->{ג.Count}");ў.Clear();
foreach(var Ɔ in ג)Ɔ.ט();}A=ג.Count;if(Ʋ.Count>0){ג.RemoveAll(Ë=>Ʋ.Contains(Ë));ս.ؽ.RemoveAll(Ë=>Ʋ.Contains(Ë));տ?.ӥ(
$"SU remove: {A}->{ג.Count}");Ʋ.Clear();}տ?.Ӧ();վ?.Ӧ();}public void Ƈ(){Ɖ.Ƈ();ł?.ט();foreach(var ª in ג)ª.Ƈ();}public Ʀ ƈ;public Ԁ Ɖ;}class Ɗ:ڑ{
public Func<Vector3D>Ƌ;int ƌ;public Ɗ(string ă,ע ƀ,int?ű=null):base("deprecated",null){ڔ=ƀ;Ț=ă;ړ=ű;}public override bool ʃ(int
Ų,ğ ų){return ړ.HasValue&&(Ų-ƌ>ړ);}public Ɗ Ŵ(){return(Ɗ)this.MemberwiseClone();}}public enum ŵ{Ŷ=0,ŷ=1,Ÿ=2,Ź=3,ź}class Ż
{public string ż;public float Ž=800f;public float ž;public ŵ ſ;public Ż(ŵ Ɓ,float ƍ,float ƙ,string ă){ſ=Ɓ;Ž=ƍ;ž=ƙ;ż=ă;}}
class ƚ{public ǩ ƛ;public int Ɯ;public int Ɲ;int ƞ;public bool Ɵ(int Ų){if(!ƛ.ǒ)return false;if(Ɯ>ƛ.Ǒ.Count){ƞ=Ų+Ɲ;Ɯ=0;return
true;}if(ƞ>0){if(ƞ>Ų)return true;else{ƞ=0;}}return false;}}class Ʀ{public List<Ǚ>Ơ=new List<Ǚ>();LinkedList<Ż>ơ=new
LinkedList<Ż>();Ǚ Ƣ;ؿ ş;public bool ƣ;public float?Ƥ;public float ƥ;public float Ƨ;float Ƙ=float.MaxValue;public float Ǝ=500;
public Ʀ(ؿ ª){ş=ª;}public void Ə(List<IMyUserControllableGun>Ɛ,List<ș>Ƒ,IMyTerminalBlock É,Vector3D ƒ){Ơ.Add(new Ǚ(Ɛ,Ƒ,ơ,É,ƒ,ş
));}List<ƚ>Ɠ=new List<ƚ>();public void Ɣ(){foreach(var ƕ in Ơ){ƕ.ċ=ð=>Ɠ.First(Ë=>Ë.ƛ==ð).Ɯ++;ƕ.ď(ƕ.ǜ.FirstOrDefault()?.Ż)
;foreach(var ð in ƕ.ǜ){Ƨ=Math.Max(Ƨ,ð.Ż.Ž);Ƙ=Math.Min(Ƙ,ð.Ż.Ž);var Ɩ=new ƚ(){ƛ=ð};Ɠ.Add(Ɩ);if(ð.Ż.ż=="Heavy ballistic S")
Ɩ.Ɲ=6*60;if(ð.Ż.ż=="Heavy ballistic L")Ɩ.Ɲ=12*60;if(ð.Ż.ż=="Rail S")Ɩ.Ɲ=20*60;if(ð.Ż.ż=="Rail L")Ɩ.Ɲ=60*60;if(ð.Ż.ż==
"Rocket")Ɩ.Ɲ=10*60;}if(ş.ج.Any())Ƙ=Math.Min(Ƙ,IsLargeGrid?700:550);Ǝ=Ƙ;ƥ=Ƨ;if(ş.ג.Any()||ş.ك.Any())ƥ=PMW_FF_REACTION_R;}Ƣ=Ơ.
FirstOrDefault();}public void Ɨ(){Ơ.ForEach(Ë=>Ë.ñ());}public Ǚ Ʊ(){return Ƣ;}public void ç(Vector3D ǐ){var Ȉ=Ʊ();if(Ȉ!=null){Ȉ.ç(ǐ);ş
.վ?.ӥ($"CurrentMisalign: {Ȉ.ú:f2}");}}Vector3D ȉ;public void Ȋ(Vector3D Ę){if(ȉ!=Ę){var ȋ=Ơ.FirstOrDefault(Ë=>Ë.ǝ==Ę);
foreach(var Ȍ in Ơ){ş.տ?.ӥ($"considering face normal {Ȍ.ǝ} vs {Ę}");}Ƣ=ȋ??Ƣ;}ȉ=Ę;}public void ȍ(int Ȏ){if(Ȏ!=-1)Ƣ=Ơ.ElementAt(Ȏ
);else{var È=Ơ.IndexOf(Ƣ);if(++È>Ơ.Count-1)Ƣ=Ơ.FirstOrDefault();else Ƣ=Ơ.ElementAt(È);}ş.տ?.ӥ($"cycled face to {Ƣ.ǝ}");ȉ=
Ƣ.ǝ;}int ȏ;public void Ȑ(Vector3D ȑ){var Ȍ=Ƣ;if(Ȍ!=null){var ƍ=(ȑ-ş.ʆ).Length();var Ȓ=ş.ـ.Get<float>("wb-range-override")
;if(Ȓ>0)Ǝ=Ȓ;else Ǝ=(ş.D.Ӱ("wb-snipe-range")?Ƨ:Ƙ)-50;var ð=Ȍ.ô();var Ǧ=Ɠ.First(Ë=>Ë.ƛ==ð);if(Ǧ.Ɵ(ʓ.Ų)||((ð.Ż.Ž<ƍ)&&(Ƨ>ƍ)))
Ǿ(Ȍ);else{if(!ð.ǒ&&(ʓ.Ų>ȏ+ş.ـ.Get<int>("wb-model-cycle-timeout"))){ȏ=ʓ.Ų;Ǿ(Ȍ);}}}Ƣ=Ȍ;}void Ǿ(Ǚ Ƹ){var ð=Ƹ.ô();if(ð!=null)
{var Į=Ƹ.ǜ.IndexOf(ð);for(int È=Į+1;;È++){if(È==Ƹ.ǜ.Count)È=0;var ǿ=Ƹ.ǜ[È];var Ǧ=Ɠ.First(Ë=>Ë.ƛ==ǿ);if((È==Į)||!ǿ.ǒ||!Ǧ.Ɵ
(ʓ.Ų)){Ƹ.ď(ǿ.Ż);break;}}}}public bool Ȁ(float ȁ,long Ȃ,IMyShipMergeBlock ȃ=null){bool Ȅ=false;foreach(var Ü in ş.ك.Where(
Ë=>(ȃ==null)||(Ë==ȃ))){var ȅ=נ<IMyShipMergeBlock>(Ü);if(ȅ!=null){Ȅ=true;var Ȇ=נ<IMyTimerBlock>(ȅ);if(Ȇ!=null){Ȇ.
TriggerDelay=ȁ;Ȇ.StartCountdown();}ȅ.ApplyAction("OnOff_Off");ş.տ?.ӥ($"torp away");var B=ş.ג.FirstOrDefault(Ë=>Ë.ن==ȅ&&!Ë.م);if(B!=
null){ş.ֆ(B,Ȃ,true);}}}return Ȅ;}public void Ď(){foreach(var Ȍ in Ơ){Ȍ.Ď();}}public RayD ȟ(){var Ƞ=Ƣ?.ý();if(Ƞ.HasValue){ş.վ
?.ӥ($"{Ƣ.ì().ż} FwOverride {(Ƞ.Value-ş.ʆ).Length():f3}m");return new RayD(Ƞ.Value,Ƣ.ö());}return new RayD();}public
Vector3D ȡ(Vector3D ȑ,Vector3D Ȣ,Vector3D ȣ,Vector3D Ȥ){if(Ƣ!=null){var ȥ=Ƣ.ì();if(ȥ!=null){var Ƞ=Ƣ.ý();if(Ƞ!=null){var ǐ=Ȧ(ȥ,Ƞ.
Value,ȑ,Ȣ,ȣ,Ȥ);return ǐ;}}}return ȑ;}Vector3D Ȧ(Ż ȧ,Vector3D Ȩ,Vector3D ȑ,Vector3D Ȣ,Vector3D ȣ,Vector3D Ȥ){Vector3D Ȟ=ȑ-Ȩ;
double ȁ=0;if((ȧ==null)||(Ȣ.LengthSquared()<double.Epsilon)&&(ȣ.Length()<double.Epsilon))return ȑ;switch(ȧ.ſ){case ŵ.ź:case ŵ.
ŷ:var ȝ=ё.Ͻ(Ȩ,ȣ,ȑ,Ȣ,ȧ.ž,ref ȁ);if(!Ȥ.IsZero())ȝ-=Ȥ*ȁ*ȁ/2f;return ȝ;case ŵ.Ÿ:var H=Ȟ.Length();var ȓ=Ȟ/H;Vector3D Ȕ=ȓ*100;
Vector3D ȕ=Ȕ+ȣ;double Ȗ=Vector3D.Dot(ȓ,ȕ);Ȗ=Math.Min(Ȗ,200);var ȗ=(200-Ȗ)*(200-Ȗ)/(2*600);var Ș=(ȗ*(200+Ȗ)/2+(H-ȗ)*200)/H;return
ё.Ͻ(Ȩ,ȣ,ȑ,Ȣ,Ș,ref ȁ);case ŵ.Ź:return ȑ;default:return ȑ;}}}class ș{public float ž{get;}public float Ž{get;}public float Ǔ
{get;}public string Ț{get;}public bool ǒ{get;}public IMyBlockGroup ț{get;}public float Ȝ{get;}public ŵ ŵ{get;}public int
ȇ{get;}public List<IMyTerminalBlock>ǽ{get;}public ș(IMyBlockGroup ǡ,ŵ Ǣ,List<IMyTerminalBlock>ǣ){ț=ǡ;var Ǥ=ǡ.Name.Split(
'[').Select(Ë=>Ë.Trim(']')).ToArray();var ǥ=Ǥ.Skip(1).FirstOrDefault(Ë=>Ë.Contains("v="));var Ǧ=Ǥ.Skip(1).FirstOrDefault(Ë
=>Ë.Contains("r="));var ǧ=Ǥ.Skip(1).FirstOrDefault(Ë=>Ë.Contains("d="));var Ǩ=Ǥ.Skip(1).FirstOrDefault(Ë=>Ë.Contains(
"fwO="));if(ǥ==null||Ǧ==null){var å=$"Custom group '{ǡ.Name}' definition failure. Expected v and r tags, e.g. 'Heavy Dakka [apck-custom][ripple][v=500][r=1000][d=5]'"
;ʓ.ʗ(å);throw new Exception(å);}if(ǧ!=null)Ǔ=float.Parse(ǧ.Split('=')[1]);else Ǔ=5;if(Ǩ!=null)Ȝ=float.Parse(Ǩ.Split('=')[
1]);Ž=float.Parse(Ǧ.Split('=')[1]);ž=float.Parse(ǥ.Split('=')[1]);ȇ=120;ŵ=Ǣ;ǒ=Ǥ.Any(Ë=>Ë=="ripple");Ț=Ǥ[0];ǽ=ǣ;}}class ǩ{
public IMyTerminalBlock Ǫ;public Vector3D ǟ;public List<IMyUserControllableGun>Ǒ=new List<IMyUserControllableGun>();public
string Ǟ;public Ż Ż;public bool ǒ;public float Ǔ;public int ǔ;public int Ǖ;public int ǖ=240;public Action Ǘ;public Action ǘ;}
class Ǚ{IMyTerminalBlock ǚ;ؿ ş;public bool Ǜ{get;private set;}public List<ǩ>ǜ;public Vector3D ǝ{get;private set;}public Ǚ(
List<IMyUserControllableGun>Ɛ,List<ș>ǫ,LinkedList<Ż>Ǡ,IMyTerminalBlock ǲ,Vector3D ƒ,ؿ ª){ş=ª;ǜ=new List<ǩ>();var ǳ=ǲ.
WorldMatrix.Translation;var Ǵ=MatrixD.Transpose(ǲ.WorldMatrix);foreach(var ǡ in ǫ){var ǵ=ǭ(ǡ,Ǡ);var Ƕ=new List<
IMyUserControllableGun>();ǡ.ț.GetBlocksOfType(Ƕ,Ë=>ǡ.ǽ.Contains(Ë));ǵ.Ǒ.AddRange(Ƕ);ǵ.ǒ=ǡ.ǒ;ª.տ?.ӥ(
$"Added custom weapon group '{ǡ.Ț}', gun count: {Ƕ.Count}, ripple: {ǵ.ǒ}");var Ǽ=new List<IMyTimerBlock>();ǡ.ț.GetBlocksOfType(Ǽ,Ë=>ǡ.ǽ.Contains(Ë));IMyTimerBlock Ƿ=null;IMyTimerBlock Ǹ=null;
IMyTimerBlock ǹ=null;foreach(var Æ in Ǽ){var Ǻ=Ч(Æ);if(Ǻ.Contains("ref"))Ƿ=Æ;if(Ǻ.Contains("fire"))Ǹ=Æ;if(Ǻ.Contains("cease"))ǹ=Æ;}Ƿ=
Ƿ??Ǽ.FirstOrDefault();if(Ƿ!=null){ǵ.Ǘ=()=>{if(Ǹ!=null)Ц(Ǹ);else Ƿ.Enabled=true;};ǵ.ǘ=()=>{if(ǹ!=null)Ц(ǹ);else Ƿ.Enabled=
false;};ǵ.Ǫ=Ƿ;var ǻ=Ƿ.GetPosition()+Ƿ.WorldMatrix.Forward*ǡ.Ȝ-ǳ;ǵ.ǟ=Vector3D.TransformNormal(ǻ,Ǵ);ª.տ?.ӥ(
"Added custom trigger");}}foreach(var þ in Ɛ){var ǯ=þ.BlockDefinition.TypeId.ToString()+"/"+þ.BlockDefinition.SubtypeName;if(!ǜ.Any(Ë=>Ë.Ǟ==ǯ)
){if(ǯ.Contains("MyObjectBuilder_SmallGatlingGun"))ǭ(ǯ,"Light ballistic",400f,800f,5,60,ŵ.ŷ,Ǡ);else if(ǯ.Contains(
"SmallBlockMediumCalibreGun")){ǭ(ǯ,"Heavy ballistic S",500f,1400f,2,400,ŵ.ŷ,Ǡ).ǒ=true;}else if(ǯ.Contains("LargeBlockLargeCalibreGun")){ǭ(ǯ,
"Heavy ballistic L",500f,2000f,2,720,ŵ.ŷ,Ǡ).ǒ=true;}else if(ǯ.Contains("SmallRailgun")){ǭ(ǯ,"Rail S",1000f,1400f,2,260,ŵ.ŷ,Ǡ).ǒ=true;}else
if(ǯ.Contains("LargeRailgun")){ǭ(ǯ,"Rail L",2000f,2000f,2,260,ŵ.ŷ,Ǡ).ǒ=true;}else if(ǯ.Contains("SmallMissileLauncher")){ǭ
(ǯ,"Rocket",200f,800f,10,180,ŵ.Ÿ,Ǡ).ǒ=true;}}var Ǳ=ǜ.FirstOrDefault(Ë=>Ë.Ǟ==ǯ);if(Ǳ!=null)Ǳ.Ǒ.Add((IMyUserControllableGun
)þ);else ʓ.ʗ($"Failed to parse weapon subtype: {ǯ}, custom name: {þ.CustomName}");}Vector3D Ǭ;foreach(var ó in ǜ){Ǭ=
Vector3D.Zero;if(ó.Ǒ.Count>0){Ǭ=Vector3D.Zero;ó.Ǒ.ForEach(Ë=>Ǭ+=Ë.GetPosition());Ǭ/=ó.Ǒ.Count;ó.Ǫ=ó.Ǒ.OrderBy(Ë=>(Ǭ-Ë.
GetPosition()).Length()).First();ó.ǟ=Vector3D.TransformNormal(Ǭ-ǳ,Ǵ);}ª.տ?.ӥ($"Group {ó.Ż.ż}: {ó.Ǒ.Count}");}ǚ=ǲ;ǝ=ƒ;Ǜ=true;}ǩ ǭ(ș
Ǯ,LinkedList<Ż>Ǡ){return ǭ(Ǯ.ț.Name,Ǯ.Ț,Ǯ.ž,Ǯ.Ž,Ǯ.Ǔ,Ǯ.ȇ,Ǯ.ŵ,Ǡ);}ǩ ǭ(string ǯ,string ă,float ƙ,float ƍ,float á,int ǰ,ŵ Ɓ,
LinkedList<Ż>Ǡ){var Ű=Ǡ.FirstOrDefault(Ë=>Ë.ż==ă);if(Ű==null){Ű=new Ż(Ɓ,ƍ,ƙ,ă);Ǡ.AddLast(Ű);}var ð=new ǩ(){Ǟ=ǯ,Ż=Ű,ǖ=ǰ,Ǔ=á};ǜ.Add(
ð);return ð;}public void ñ(){Ǜ=true;ò();}public void ò(){if(Ǜ){foreach(var ó in ǜ){ó.Ǒ.ForEach(Ë=>Ë.Shoot=false);ó.ǘ?.
Invoke();}Ǜ=false;}}public ǩ ô(){if(ë!=null)return ǜ.Where(Ë=>Ë.Ż==ë).FirstOrDefault();return null;}public Vector3D ö(){return
ô().Ǫ?.WorldMatrix.Forward??Vector3D.Zero;}public Vector3D?ý(){if(ë!=null)foreach(var é in ǜ.Where(Ë=>Ë.Ż==ë)){if(!ş.D.Ӱ(
"coax-ripple")||!é.ǒ)return Vector3D.TransformNormal(é.ǟ,ǚ.WorldMatrix)+ǚ.WorldMatrix.Translation;else{var ø=é.Ǒ[é.ǔ];if(ø.IsWorking)
return ø.WorldMatrix.Translation;else{Č(é);return null;}}}return null;}public float ù;public double ú;double û;bool ü(
IMyTerminalBlock þ,Vector3D è,float á,bool î){var â=ş.ـ.Get<float>("wb-precision-override");if(â>0)á=â;var ã=Vector3D.Dot((è-þ.
GetPosition()).Normalized(),þ.WorldMatrix.Forward);ã=Math.Min(ã,1f);var ä=Math.Acos(ã);var H=(þ.GetPosition()-è).Length();var å=
Math.Tan(ä)*Math.Max(100,H);var æ=Math.Abs(å-û);û=å;ù=á;ú=Math.Tan(ä)*H;return(H<ë.Ž)&&(ä<Math.PI/4f)&&(ú<á)&&(!î||æ<0.5f);}
public void ç(Vector3D è){if(ë!=null)foreach(var é in ǜ.Where(Ë=>Ë.Ż==ë)){if(ş.D.Ӱ("coax-ripple")&&é.ǒ){var ê=é.Ǒ[é.ǔ];if(ü(ê,
è,é.Ǔ,true)){if(!ê.IsWorking)Č(é);else{if(!Ǜ){ê.Shoot=true;Ǜ=true;if(é.Ǖ==0)é.Ǖ=ʓ.Ų;}č=true;}}}else{if(ü(é.Ǫ,è,é.Ǔ,false)
){if(!Ǜ){é.Ǒ.ForEach(Ë=>Ë.Shoot=true);é.Ǘ?.Invoke();Ǜ=true;}č=true;}}}}Ż ë;public Ż ì(){return ë;}Ż í;int õ;int ÿ;public
void ď(Ż Ċ){í=Ċ;õ=ʓ.Ų;}public Action<ǩ>ċ;void Č(ǩ é){é.ǔ++;é.Ǖ=0;Ǜ=false;if(é.ǔ>é.Ǒ.Count-1)é.ǔ=0;ċ?.Invoke(é);}bool č;
public void Ď(){if((í!=null)&&(í!=ë)){if(ǜ.Any(Ë=>Ë.Ż==í)){ë=í;ÿ=ǜ.First(Ë=>Ë.Ż==í).ǖ;ÿ=300;Ǜ=true;ò();}else ë=null;í=null;}
foreach(var é in ǜ.Where(Ë=>Ë.Ǖ>0)){if(ʓ.Ų-é.Ǖ>(é.Ż.ż.Contains("Rail")?ş.ـ.Get<int>("ripple-increment-interval-rail"):ş.ـ.Get<
int>("ripple-increment-interval"))){Č(é);}}if(!č)ò();č=false;}}void Đ(){ɸ.ʰ.Clear();Save();Ē();}IMyGridTerminalSystem đ;
void Ē(){ʓ.Ų=0;ɜ=new Queue<ʡ>();ɸ=new ʯ((Ì)=>Storage=Ì);ɸ.ʵ(Storage);ʿ=new ˀ();ػ=new ؼ();ӌ=new ؿ(Me,ɸ,đ,IGC,new ڲ(IGC),ʿ,ą,ػ
);ӌ.տ?.ӥ($"IGC id: {IGC.Me}");ɷ=ӌ.ɷ;ӌ.ט();ӌ.م=true;ʿ.Ɣ(ӌ);if(ӌ.ب.Any())ŝ=new Ş(ӌ,ɸ,đ);Ի=new Ղ(IGC);ʿ.ȴ(Ի);ӌ.ر.ȴ(Ի,ӌ,ŝ!=
null);ӌ.ɷ.ȴ(Ի);IsLargeGrid=ӌ.œ.CubeGrid.GridSizeEnum==MyCubeSize.Large;if(!string.IsNullOrEmpty(Me.CustomData)||!string.
IsNullOrEmpty(Storage))Ĕ=true;}void Save(){ɸ?.ʪ();}bool ē;bool Ĕ;class ĕ{Dictionary<string,IMyTimerBlock>ā=new Dictionary<string,
IMyTimerBlock>();public List<IMyTimerBlock>Ā{get;}public ĕ(List<IMyTimerBlock>ā){Ā=ā;}public bool Ă(string ă){IMyTimerBlock Ą;if(!ā.
TryGetValue(ă,out Ą)){Ą=Ā.FirstOrDefault(A=>A.CustomName.Contains(ă));if(Ą!=null)ā.Add(ă,Ą);else return false;}Ц(Ą);return true;}}Ć
ą;class Ć{Dictionary<string,Action<string[],ؿ>>Ö;IMyIntergridCommunicationSystem ć;public Ć(
IMyIntergridCommunicationSystem È,Dictionary<string,Action<string[],ؿ>>Ĉ){Ö=Ĉ;ć=È;}public void ĉ(string J,string[]º,ؿ ª){Ö[J].Invoke(º,ª);}public void
à(string À,long J=0){if(J!=0)Á(À,J);else Á(À);}void Á(string Â,params long[]I){C("apck.command",Â,I);}public void Ã(
string Ä,ؿ ª){ª.տ?.ӥ($"Got apck cmd: {Ä}");Ä=Ä.Replace("{me}",ª.ذ.Me.ToString());var º=Ä.Split(':');var Å=º[0];if(Å.StartsWith
("<")){var Æ=Å.Trim('<','>');if(!ª.Ƶ.Contains(Æ))return;else º=º.Skip(1).ToArray();}Å=º[0];if(Å=="toggle"){var Ç=º[1];if(
º.Length>2)ª.D.ӭ(Ç,bool.Parse(º[2]));else ª.D.ӯ(Ç);ª.D.ӳ(Ç,ª);return;}if(Å=="command"){ĉ(º[1],º,ª);return;}int?È=null;
string F=null;if(Å.Contains("]")){È=int.Parse(Å.Split('[').Select(M=>M.Trim(']')).Skip(1).First());Å=Å.Split('[').First();}if(
Å.Contains(">")){F=Å.Split('<').Select(M=>M.Trim('>')).Skip(1).First();Å=Å.Split('<').First();}var É=string.Join(":",º.
Skip(1).ToArray());if(F!=null)É=$"<{F}>:{É}";if(Å=="bc")Á(É);if(Å=="w"){if(È.HasValue&&(ª.ɷ.ɪ.Count>È))Á(É,ª.ɷ.ɪ.ElementAt(È
.Value));else Á(É,ª.ɷ.ɪ.ToArray());}if(Å=="su"){if(È.HasValue&&(ª.ג.Count>È))ĉ(º[2],º.Skip(1).ToArray(),ª.ג[È.Value]);
else foreach(var B in ª.ג)ĉ(º[2],º.Skip(1).ToArray(),B);}if(Å=="recursive"){Ê(º,ª);}if(Å=="p"&&(ª.ɷ.ɩ!=0))Á(É,ª.ɷ.ɩ);}void Ê
(string[]º,ؿ ª){ĉ(º[2],º.Skip(1).ToArray(),ª);foreach(var B in ª.ג){Ê(º,B);}}public void C<D>(string F,D H,params long[]I
){if(!I.Any()){Ь.ї++;ć.SendBroadcastMessage(F,H);}else{foreach(var J in I){Ь.ї++;ć.SendUnicastMessage(J,F,H);}}}public
void K(Vector3D M,string N,string O,int Q,string S,bool X=false){var Y=O.Trim('#');var A=new Color(µ(Y,0),µ(Y,2),µ(Y,4));ć.
SendBroadcastMessage(X?"cmdr.persist-targetable":"cmdr.persist-projection",new MyTuple<string,Vector2,Vector3D,Vector4,string>(N,Vector2.One
*Q,M,A.ToVector4(),S));}int µ(string Ë,int È){return int.Parse(Ë.Substring(È,2),System.Globalization.NumberStyles.
HexNumber);}}List<string>Ø=new List<string>();List<MyIGCMessage>Ù=new List<MyIGCMessage>();void Main(string Ú){if(!ē){try{ʓ.Ɣ(
this);ʓ.ʕ=(Ë,Ì)=>{if(!Ë)ʓ.ʗ($"ASSERT FAILURE: {Ì}");};Ē();ē=true;Runtime.UpdateFrequency=UpdateFrequency.Update1;}catch(
Exception ex){Runtime.UpdateFrequency=UpdateFrequency.None;Echo(ex.ToString());if(Ú=="command:clear-state")Storage="";else if(Ú==
"command:clear-defs"){Đ();Runtime.UpdateFrequency=UpdateFrequency.Update1;}return;}}ʓ.ʙ?.Invoke();ʓ.څ?.Invoke(
$"Subordinates: {ӌ?.ɷ.ɪ.Count}");ʓ.Ų++;ʓ.ʔ=Math.Max(0.001,Runtime.TimeSinceLastRun.TotalSeconds);ʓ.D+=ʓ.ʔ;if(DIAG_IGC.HasValue)Echo("WARNING! DbgIgc");
Echo($"AutoPillock Core v.{Ver}");ӌ.վ?.ӥ($"Dockable: {ӌ.ط!=null} Carrier: {ŝ!=null}\nDesignators: {ӌ.ج.Count}");ӌ.վ?.ӥ(
"Services found:");ӌ.վ?.ӥ($"TGP: {ӌ.ן!=null} TP: {ӌ.ض!=null}");ӌ.վ?.ӥ($"TMC: {ӌ.պ!=null} Osvc: {ӌ.ײ!=null}");Ø.Clear();Ù.Clear();while(
IGC.UnicastListener.HasPendingMessage){Ù.Add(IGC.UnicastListener.AcceptMessage());}try{ӌ.ر.Ԝ(Ù,ӌ);}catch(Exception ex){ʓ.ʗ(
ex.ToString());throw ex;}var Û=IGC.RegisterBroadcastListener("apck.command");while(Û.HasPendingMessage){var Ü=Û.
AcceptMessage();Ø.AddRange(ɑ(Ü.Data.ToString()));}foreach(var Ü in Ù){var Ý=Ü.Source;Ь.й++;if(Ü.Tag=="apck.command")Ø.AddRange(ɑ(Ü.
Data.ToString()));else{if(Ü.Tag=="apck-handshake-reply"){if((string)Ü.Data=="TGP")ӌ.ן=ӌ.ռ.First(Ë=>Ë.EntityId==Ü.Source);if(
(string)Ü.Data=="TP"){ӌ.ض=ӌ.ռ.First(Ë=>Ë.EntityId==Ü.Source);ӌ.ض.TryRun(
$"[command:set-mode:{ӌ.ų.ġ}],[toggle:vtol-hybrid:{ӌ.ų.Ģ==3}]");}if((string)Ü.Data=="OutputSvc")ӌ.ײ=ӌ.ռ.First(Ë=>Ë.EntityId==Ü.Source);if((string)Ü.Data=="TMC")ӌ.պ=ӌ.ռ.First(Ë=>Ë.
EntityId==Ü.Source);}if(Ü.Tag=="apck.tac.addchild"){ɷ.ɪ.Add(Ý);}else if(Ü.Tag=="tp.force-report"){ӌ.ų.ȩ=(Vector3D)Ü.Data;}else
if(Ü.Tag=="apck.dpath.complete"){ŝ.ŭ(Ü.Source);}else if(Ü.Tag=="apck.dpath.add:node"){ŝ?.Ħ((MatrixD)Ü.Data);}else if(Ü.Tag
=="apck.dpath.add:entry"){ŝ?.Ħ((MatrixD)Ü.Data,true);}else if(Ü.Tag=="diag.set-storage"){ɸ.ʵ(Ü.Data as string);ɸ.ʪ();}else
if(Ü.Tag=="apck.docking.request"){ŝ?.Ů(Ý,(Vector3D)Ü.Data);}else if(Ü.Tag.Contains("dpath.exec")){var Þ=Ü.Tag.Contains(
"depart");var ß=(ImmutableArray<Vector3D>)Ü.Data;ӌ.Ɖ.ѿ(new پ(ӌ,ß.Reverse().ToList(),Þ,Ü.Source));}else if(Ü.Tag==
"apck.unicast.ihave"){IGC.SendUnicastMessage(Ý,"apck.unicast.t+",(MyTuple<string,long>)Ü.Data);}else if(Ü.Tag=="apck.unicast.ihave+callback"
){var H=(MyTuple<long,string>)Ü.Data;ӌ.Ѥ.Ã(H.Item2.Replace("{id}",H.Item1.ToString()),ӌ);}}}ɷ.ŕ();ŝ?.ŕ();if(Ĕ&&string.
IsNullOrEmpty(Ú)){Ĕ=false;var Ö=Me.CustomData.Trim('\n').Split(new[]{'\n'},StringSplitOptions.RemoveEmptyEntries).Where(Ì=>!Ì.
StartsWith("//")).Select(Ì=>"["+Ì+"]").ToList();Ú=string.Join(",",Ö);}if(!string.IsNullOrEmpty(Ú)&&Ú.Contains(":")){Ь.й++;Ø.
AddRange(ɑ(Ú));}foreach(var Ä in Ø){ӌ.Ѥ.Ã(Ä,ӌ);}ɷ.Ⱦ(ػ.ؽ);ʿ.ʃ(ػ.ؽ,Ù);Ի.Ը(Ù);ӌ.ŕ();var Í=ػ.ؽ.Count;if(ػ.ؽ.Count>0)Ò.AppendLine(
$"Units: {Í}");var Æ=ʓ.Ų%10;for(int È=0;È<Í;È++){if((Í<5)||(È%10==Æ)){var ª=ػ.ؽ[È];try{if(!ª.œ.IsFunctional||!ª.س.IsFunctional)ª.ł.ב(
ª);else ª.ŕ();}catch(Exception ex){ʓ.ʗ("SubUnit failure, removing");ª.ł.ב(ª);ʓ.ʗ(ex.ToString());}}}var Î=ӌ.ײ;if((Î!=null)
&&IGC.IsEndpointReachable(Î.EntityId)&&(Î.Enabled)){var Ï=Î.EntityId;ӌ.վ?.ӥ("Delegating output to "+Î.EntityId);Ò.
AppendLine(ӌ.D.Ӱ("damp-when-idle")?"DMP":"INR");Ò.AppendLine((ӌ.ų.ġ!=0)&&(ӌ.ض!=null)?"THR":"HC");Ŝ(Ï,Ò.ToString(),new Vector2(0f,
3.8f/4),1f);Ò.Clear();Ò.AppendLine(ӌ.Ɖ.Ѱ==ResponseState.Attacking?"TURR REACT":"");Ŝ(Ï,Ò.ToString(),new Vector2(0f,1.15f),
0.6f);Ò.Clear();Õ(Ï);}else if(DIAG_IGC.HasValue)Õ(DIAG_IGC.Value);if(ӌ.վ!=null){Ò.AppendFormat("Processed in {0:f3} ms\n",
Runtime.LastRunTimeMs).AppendFormat("sentMsgCount: {0}\n",Ь.ї).AppendFormat("receivedCmdCount: {0}\n",Ь.й).AppendFormat(
"ParseVectorsCount: {0}\n",Ь.Ю).AppendFormat("TVextrapolationsCount: {0}\n",Ь.Я).AppendFormat("TVInvalidationsCount: {0}\n",Ь.т).AppendFormat(
"TacNode.Parent: {0}\n",ɷ.ɩ).AppendFormat("TacNode.Children: {0}\n",ɷ.ɪ.Count());ӌ.վ?.ӥ(Ò.ToString());}Ò.Clear();ʧ();Ñ.Enqueue(Runtime.
LastRunTimeMs);if(Ñ.Count==100){double Ð=0;foreach(var Ë in Ñ)Ð+=Ë;Echo($"100 runs avg {(Ð/100f).ToString("f3")} ms, instr.: {(float)Runtime.CurrentInstructionCount/Runtime.MaxInstructionCount*100:f1}%"
);Ñ.Dequeue();}}Queue<double>Ñ=new Queue<double>();StringBuilder Ò=new StringBuilder();List<MyTuple<string,Vector3D,
ImmutableArray<string>>>Ó=new List<MyTuple<string,Vector3D,ImmutableArray<string>>>();void Ô(string F,Vector3D M,params string[]Ì){Ó.
Add(new MyTuple<string,Vector3D,ImmutableArray<string>>(F,M,Ì.ToImmutableArray()));}void Õ(long Ė){IGC.SendUnicastMessage(Ė
,"hud.apck.proj",Ó.ToImmutableArray());Ó.Clear();}void Ŝ(long Ř,string Æ,Vector2 M,float Q){IGC.SendUnicastMessage(Ř,
"draw-text",new MyTuple<string,Vector2,float>(Æ,M,Q));}Ş ŝ;class Ş{ؿ ş;Dictionary<IMyShipConnector,Vector3D>Š=new Dictionary<
IMyShipConnector,Vector3D>();List<ě>Ť;long?š;IMyBroadcastListener Ţ;public Ş(ؿ ª,ʯ ţ,IMyGridTerminalSystem đ){ş=ª;Ť=ţ.ʱ;ª.ب.ForEach(Ë=>Š
.Add(Ë,Ë.GetPosition()));Ţ=ª.ذ.RegisterBroadcastListener("apck.depart.request");foreach(var ť in Ť){ª.տ?.ӥ($"node: {ť.ĝ}"
);}}Τ ś=new Τ("docking",null);public void ŕ(){ş.վ?.ӥ("Navmesh: "+Ť.Count);while(Ţ.HasPendingMessage){var Ü=Ţ.
AcceptMessage();var H=(MyTuple<long,Vector3D>)Ü.Data;if(ş.ب.Any(Ë=>Ë.EntityId==H.Item1))Ů(Ü.Source,H.Item2,true);}ş.վ?.ӥ(
$"Dpath lock: {š}");foreach(var Ì in Ŧ)ş.վ?.ӥ($"{Ì} awaits docking");foreach(var Ì in Ū)ş.վ?.ӥ($"{Ì} awaits departure");if(Ŧ.Count>0&&!š.
HasValue){var Ŗ=ş.ب.FirstOrDefault(H=>(string.IsNullOrEmpty(H.CustomData)||(H.CustomData==Ŧ.Peek().ToString()))&&(H.Status==
MyShipConnectorStatus.Unconnected));if(Ŗ!=null){var J=Ŧ.Dequeue();Ŗ.CustomData=J.ToString();ş.տ?.ӥ($"{J} assigned to connector");var ŗ=
MatrixD.Transpose(Ŗ.WorldMatrix);try{if(Ť.Any(Ë=>Ë.Ĝ)){var Ř=Ũ(ū[J],Ŗ).Select(Ë=>Ë.ĝ).ToImmutableArray();foreach(var Ë in Ř)ş.տ
?.ӥ($"dpath.exec: {Ë}");ş.տ?.ӥ($"Sent {Ř.Length}-node approach path");ş.Ѥ.C("apck.dpath.exec.docking",Ř,J);š=J;}else ş.Ѥ.
C("apck.dpath.exec.docking",new List<Vector3D>(){ů(Ŗ,20)}.ToImmutableArray(),J);}catch(Exception ex){ʓ.ʗ(ex.ToString());
throw ex;}}}if(Ū.Count>0&&!š.HasValue){var J=Ū.Peek();var ř=ş.ب.FirstOrDefault(H=>H.CustomData==J.ToString());if(ř!=null){Ū.
Dequeue();if(Ť.Any(Ë=>Ë.Ĝ)){var Ř=ũ(ř,ū[J]).Select(Ë=>Ë.ĝ).ToImmutableArray();ş.տ?.ӥ($"Sent {Ř.Length}-node departure path");ş.
Ѥ.C("apck.dpath.exec.depart",Ř,J);š=J;}else ş.Ѥ.C("apck.dpath.exec.depart",new List<Vector3D>(){ů(ř,20)}.ToImmutableArray
(),J);}}foreach(var H in ş.ب.Where(H=>!string.IsNullOrEmpty(H.CustomData))){long J;if(long.TryParse(H.CustomData,out J)){
ş.վ?.ӥ($"Channeling DV to {J}");var Ü=H.WorldMatrix;var M=Ü.Translation+Ü.Forward*(IsLargeGrid?1.25:0.5);if(Š[H]!=
Vector3D.Zero){var Ë=(H.GetPosition()-Š[H]);ś.Ő=Ë/ʓ.ʔ;M+=Ë;}Š[H]=H.GetPosition();ś.Ρ(M);ś.Ι=Ü;var Ç=ś.п();ş.ذ.SendUnicastMessage
(J,"apck.docking.update",Ç);}}}public void Ŭ(){if(š.HasValue){ŭ(š.Value);}}public void ŭ(long J){ş.տ?.ӥ(
$"{J} RouteComplete");if(š.HasValue&&(š.Value==J)){ş.ب.First(Ë=>Ë.CustomData==J.ToString()).CustomData="";š=null;}}public void Ů(long J,
Vector3D H,bool Þ=false){ş.տ?.ӥ($"{J} requests {(Þ?"depart":"docking")}");if(Þ){if(!Ū.Contains(J))Ū.Enqueue(J);}else{if(!Ŧ.
Contains(J))Ŧ.Enqueue(J);}ū[J]=H;}Vector3D ů(IMyShipConnector H,float Ü){return Vector3D.Rotate(H.WorldMatrix.Translation+H.
WorldMatrix.Forward*Ü-ş.œ.WorldMatrix.Translation,MatrixD.Transpose(ş.œ.WorldMatrix));}Dictionary<long,Vector3D>ū=new Dictionary<
long,Vector3D>();Queue<long>Ŧ=new Queue<long>();Queue<long>Ū=new Queue<long>();IEnumerable<ě>ŧ(Dictionary<ě,ě>ī,ě Ę){yield
return Ę;var A=ī[Ę];while(A!=null){yield return A;A=ī[A];}}List<ě>Ũ(Vector3D Ś,IMyShipConnector H){var Ŕ=Vector3D.Rotate(Ś-ş.œ
.WorldMatrix.Translation,MatrixD.Transpose(ş.œ.WorldMatrix));var ĥ=Vector3D.Rotate(H.GetPosition()-ş.œ.WorldMatrix.
Translation,MatrixD.Transpose(ş.œ.WorldMatrix));var ħ=Ť.Where(Ë=>Ë.Ĝ).OrderBy(Ë=>(Ë.ĝ-Ŕ).LengthSquared()).FirstOrDefault();var Ĩ=Ť.
OrderBy(Ë=>(Ë.ĝ-ĥ).LengthSquared()).FirstOrDefault();return ĩ(ħ,Ĩ);}List<ě>ũ(IMyShipConnector H,Vector3D Ś){var Ŕ=Vector3D.
Rotate(H.GetPosition()-ş.œ.WorldMatrix.Translation,MatrixD.Transpose(ş.œ.WorldMatrix));var ĥ=Vector3D.Rotate(Ś-ş.œ.WorldMatrix
.Translation,MatrixD.Transpose(ş.œ.WorldMatrix));var ħ=Ť.OrderBy(Ë=>(Ë.ĝ-Ŕ).LengthSquared()).FirstOrDefault();var Ĩ=Ť.
Where(Ë=>Ë.Ĝ).OrderBy(Ë=>(Ë.ĝ-ĥ).LengthSquared()).FirstOrDefault();return ĩ(ħ,Ĩ);}List<ě>ĩ(ě ħ,ě Ĩ){var į=new HashSet<ě>();į.
Add(ħ);var ī=new Dictionary<ě,ě>();var Ĭ=new Dictionary<ě,double>();var ĭ=new Dictionary<ě,double>();foreach(var Ę in Ť){ĭ.
Add(Ę,double.MaxValue);Ĭ.Add(Ę,double.MaxValue);ī.Add(Ę,null);}Ĭ[ħ]=0;ĭ[ħ]=0;while(į.Count>0){var Į=į.OrderBy(Ë=>ĭ[Ë]).
FirstOrDefault();if(Į==Ĩ){return ŧ(ī,Į).ToList();}į.Remove(Į);foreach(var Ę in Į.Ğ){var İ=Ĭ[Į]+Ę.Item2;if(İ<Ĭ[Ę.Item1]){ī[Ę.Item1]=Į;Ĭ
[Ę.Item1]=İ;ĭ[Ę.Item1]=İ;if(!į.Contains(Ę.Item1)){į.Add(Ę.Item1);}}}}ʓ.ʗ($"aStar Failure");return null;}public void Ħ(
MatrixD ė,bool Ĥ=false){try{var Ę=new ě(){ĝ=Vector3D.Rotate(ė.Translation-ş.œ.GetPosition(),MatrixD.Transpose(ş.œ.WorldMatrix))
,Ĝ=Ĥ};var ę=Ť.Where(Ë=>Ë.Ĝ).OrderBy(Ë=>(Ë.ĝ-Ę.ĝ).LengthSquared()).FirstOrDefault();if(ę==null)ę=Ť.OrderBy(Ë=>(Ë.ĝ-Ę.ĝ).
LengthSquared()).FirstOrDefault();if(ę!=null){ę.Ğ.Add(new MyTuple<ě,double>(Ę,(Ę.ĝ-ę.ĝ).Length()));Ę.Ğ.Add(new MyTuple<ě,double>(ę,(Ę
.ĝ-ę.ĝ).Length()));}Ť.Add(Ę);}catch(Exception ex){ʓ.ʗ(ex.ToString());}}public void Ě(){Ť.Clear();}}class ě{public bool Ĝ;
public Vector3D ĝ;public HashSet<MyTuple<ě,double>>Ğ=new HashSet<MyTuple<ě,double>>();}class ğ{PState Ġ;public
ThrustDelegation ġ;public int Ģ;public void ģ(PState Ī){if(Ī==PState.WP)ń();else if(Ī==PState.Inert)Ņ(false);else if(Ī==PState.Disabled)
Ņ();Ġ=Ī;}public void ı(string ŉ){ThrustDelegation H;if(Enum.TryParse(ŉ,out H)){ġ=H;if(ª.ض!=null)ª.ض.TryRun(
$"command:set-mode:{H}");}}void ń(){ĸ.DampenersOverride=false;}void Ņ(bool ņ=true){Ľ.GyroOverride=false;ĺ().Б();ĸ.DampenersOverride=ņ;}public ğ
(IMyRemoteControl Ň,IMyIntergridCommunicationSystem ć,IMyGyro ň,Func<С>Ŋ,ؿ Œ){ĸ=Ň;Ĺ=ć;Ľ=ň;ª=Œ;ĺ=Ŋ;ķ=Ŋ().В(1);ª.տ?.ӥ(
$"Total thrust force BB: {ķ}, vol: {ķ.Volume}");if(ķ.Volume>0)Ģ=3;else if(ķ.Max.Z>0)Ģ=1;ª.տ?.ӥ($"ThrustDim: {Ģ}");}Vector3D ŋ;public string Ō;public Vector3D ō{get;
private set;}public Vector3D Ŏ{get;private set;}public double ŏ{get;private set;}public Vector3D Ő{get{return ĸ.
GetShipVelocities().LinearVelocity;}}Vector3D?ő;public double œ;public Vector3D Ń;public Vector3D?Ĳ{get{if(ő!=null)return ő;var Ł=ĸ.
GetNaturalGravity();if(Ł!=Vector3D.Zero){œ=Ł.Length();Ń=Ł/œ;ő=Ł;}else ª.ƃ=null;return ő;}}public double?ĳ{get{double Ĵ;if(ĸ.
TryGetPlanetElevation(MyPlanetElevation.Surface,out Ĵ))return Ĵ;return null;}}Vector3D ĵ{get;set;}public Vector3D Ķ{get;set;}public
BoundingBoxD ķ;public IMyRemoteControl ĸ;IMyIntergridCommunicationSystem Ĺ;Func<С>ĺ;ؿ ª;int Ļ;double ļ;IMyGyro Ľ;public
IMyTerminalBlock ľ{get{return Ľ;}}public Vector3D Ŀ;public Vector3D ŀ;bool ï;public Vector3D ȩ;public void ɢ(ע ƀ){var ξ=ʓ.Ų-Ļ;Ļ=ʓ.Ų;if(ξ
>0)ļ=ξ/60f;else return;Ķ=(Ő-ĵ)/ļ;ĵ=Ő;ŀ=Vector3D.Zero;ï=false;switch(Ġ){case PState.Disabled:return;case PState.WP:try{var
ο=ƀ;if(ο==null){ģ(PState.Disabled);return;}var Ⱥ=Vector3D.Zero;if(ο.ظ.HasValue){var π=(ο.ק!=null)?ο.ק():ľ.GetPosition();
bool ρ=!ª.D.Ӱ("suppress-gyro-control")&&ª.ـ.Get<bool>("hold-thrust-on-rotation");if((ρ&&(ŋ.Length()>1))||ª.D.Ӱ(
"suppress-transition-control")){ν(π,π,null,null,false);}else{ν(π,ο.ظ.Value,ο.ؾ?.Ő,ο.ר,ο.ש);}}else{var M=ľ.GetPosition();float?ˠ=null;if(ª.D.Ӱ(
"damp-when-idle"))ˠ=0;if((Ģ==1)&&(ġ==ThrustDelegation.None))Ⱥ=-ϖ(Vector3D.Zero,ˠ,false,Vector3D.Zero);else ν(M,M,null,ˠ,false);}if(ġ==
ThrustDelegation.Rover)ο.Ŀ=null;if(ο.Ŀ.HasValue){Ŀ=ο.Ŀ.Value;Ⱥ=Ŀ-(ο.غ??ľ.WorldMatrix).Translation;}if(Ⱥ!=Vector3D.Zero){σ(Ⱥ,ο.ع,ο.غ??ľ.
WorldMatrix,(ο.ا?.Η.HasValue==true)&&!ο.װ);if((Ģ==1)&&(ġ==ThrustDelegation.None))ĺ().ɓ().ɛ(Ϗ);}if(ï!=Ľ.GyroOverride){Ľ.GyroOverride
=ï;}}catch(Exception ex){if(ª.خ!=null)ª.خ.CustomName+="HC Exception! See PB screen log";var Ǧ=ĸ;var Ĵ=
$"HC EPIC FAIL\nBehavior:{ƀ.Ț}\n{ex}";ʓ.ʗ(Ĵ);ģ(PState.Disabled);throw ex;}break;}Ϗ=0;ő=null;}void σ(Vector3D Ⱥ,Vector3D τ,MatrixD υ,bool φ){var χ=Vector3D.
Zero;var Ȱ=Vector3D.TransformNormal(ĸ.GetShipVelocities().AngularVelocity,MatrixD.Transpose(ľ.WorldMatrix));var ψ=Vector3D.
Zero;if(Ⱥ!=Vector3D.Zero){if(φ)ψ=Vector3D.TransformNormal(ό(Ⱥ),MatrixD.Transpose(ľ.WorldMatrix));Ⱥ=Ⱥ.Normalized();χ=ё.ϻ(Ⱥ,υ,
ľ.WorldMatrix,τ,ª.ـ.Get<float>("roll-power-factor"));Ŏ=new Vector3D(Math.Abs(χ.X),Math.Abs(χ.Y),Math.Abs(χ.Z));ō=Ŏ-ŋ;ŋ=Ŏ;
ŏ=Vector3D.Dot(Ⱥ,υ.Forward);}ï=!ª.D.Ӱ("suppress-gyro-control");if(ï)ё.ɘ(Ľ,χ,Ȱ,ψ);}void ν(Vector3D γ,Vector3D μ,Vector3D?Ȣ
,float?δ,bool ε){if(Ġ!=PState.WP)return;bool ζ=false;if(ġ!=ThrustDelegation.None){if(ª.ض!=null){if(Ĺ.IsEndpointReachable(
ª.ض.EntityId)){var H=μ;var Ƹ=γ;ª.վ?.ӥ("Thrust delegation");ζ=(ġ==ThrustDelegation.Vtol)&&(Ģ==3);var η=δ.HasValue?δ.Value:
-1;if(ζ&&(Ő.LengthSquared()<1))η=-1;ª.ذ.SendUnicastMessage(ª.ض.EntityId,"thrust",new MyTuple<Vector3D,Vector3D,Vector3D,
bool,float>(Ƹ,H,Ȣ??Vector3D.Zero,ε,η));if(!ζ)return;}else{throw new Exception("VTOL: a-thrust-provider is not reachable");}}
}if(Ģ<3)return;float ə=ĸ.CalculateShipMass().PhysicalMass;var θ=ĺ().В(ə);if(θ.Volume==0||(ə==0))return;var ι=μ;var Β=Ľ.
WorldMatrix;Β.Translation=γ;var Ȟ=μ-Β.Translation;var ȓ=Ȟ.Normalized();var κ=MatrixD.Transpose(Β);var λ=Vector3D.TransformNormal(Ő,
κ);var ς=λ;var ω=Vector3D.Zero;if(Ĳ!=null){ω=Vector3D.TransformNormal(Ĳ.Value,κ);if(!ζ)θ+=ω;}var ϡ=Vector3D.Zero;var ϙ=
Vector3D.Zero;var Ϛ=Vector3D.Zero;var ϛ=Vector3D.Zero;var Ϝ=Vector3D.Zero;var ϝ=new Vector3D();var Ϟ=ª.ف.ɂ(ref Β)*1000;var ϟ=
Vector3D.Zero;if(ζ){ϟ=Vector3D.TransformNormal(ȩ,κ)/ə;ª.վ?.ӥ($"localExtA: {ϟ.ToString("f2")}");ϝ-=ϟ;}var Ϡ=Ȟ.Length();if(Ϡ>
double.Epsilon){var Ϣ=Vector3D.TransformNormal(Ȟ,κ);var ϩ=Ϣ.Normalized();Ϛ=Vector3D.Reject(λ,ϩ);if(Ȣ.HasValue){var ϣ=Vector3D.
TransformNormal(Ȣ.Value,κ);ς=λ-ϣ;Ϛ=Vector3D.Reject(ς,ϩ);}else{ς-=Ϛ;}var Ϥ=Vector3D.Dot(ς,ϩ);bool ϥ=Ϥ>0;bool Ϧ=true;var α=new RayD(
Vector3D.Zero,ϩ);double ϧ,Ϩ;if(!θ.Intersect(ref α,out ϧ,out Ϩ))throw new InvalidOperationException(
$"Not enough thrust to compensate for gravity - zero is outide of acc BB. Mass: {ə}");Ϝ=α.Direction*ϧ;ϛ=α.Direction*Ϩ;var Ϫ=ϛ.Length();var Ϙ=Math.Pow(Math.Max(0,Ϥ),2)/(2*Ϫ*StoppingPowerQuotient);if(ϥ){if(
Ϙ>Ϡ)Ϧ=false;else Ϛ/=ļ;}if(ε||Ϧ){if(δ.HasValue&&(Vector3D.Dot(ȓ,Ő)>=δ)){ϝ=ϛ;ϝ*=(Ϥ-δ.Value)/Ϫ;}else ϝ=Ϝ;}else ϝ=ϛ;if(Ϧ){var
ϊ=Vector3D.Dot(ȓ,Ő);if(ϊ>MAX_SP-10){ϝ*=(MAX_SP-ϊ)/10;}if(SmoothPointBlankAcceleration&&(Ȟ.LengthSquared()<Ϝ.LengthSquared
()))ϝ=-Ϣ;}ϙ=ϝ;ϡ=Ϛ;if(Ϛ.IsValid())ϝ+=Ϛ;}else if(δ.HasValue&&(δ==0)){var Æ=1/ļ;if(λ.LengthSquared()<DampeningCutoffSquared)
Æ=θ.Extents.Max()*DampeningCutoffFactor;if(Ϟ!=Vector3D.Zero)ϝ+=Vector3D.Reject(λ,Ϟ.Normalized())*Æ;else ϝ+=λ*Æ;}if(Ĳ!=
null)ϝ+=ω;if(ª.ł==null)ϝ-=Ϟ;try{ϝ.Y*=-1;ĺ().ɘ(ϝ,ə);}catch{ʓ.ʗ("SetOverride failure");ʓ.ʗ($"reject{Ϛ}");ʓ.ʗ($"Dt{ļ:f5}");ʓ.ʗ(
$"reject/Dt{Ϛ/ļ}");ʓ.ʗ($"reversePoint{ϛ}");ʓ.ʗ($"point{Ϝ}");ʓ.ʗ($"overrideVector: {ϝ}, mass: {ə}, accCap: {θ}");throw;}}Vector3D?ϋ;
Vector3D ό(Vector3D Ⱥ){var ê=Vector3D.Zero;if(ϋ.HasValue){ê=Vector3D.Cross(Ⱥ,Ⱥ-ϋ.Value)/Ⱥ.LengthSquared()/ļ;}ϋ=Ⱥ;return ê;}
public Vector3D ύ(double ώ){return ľ.GetPosition()+ľ.WorldMatrix.Forward*ώ;}double Ϗ;enum ϐ{ϑ,ϒ,ϓ,ϔ,ϕ}public Vector3D ϖ(
Vector3D Ȟ,float?ϗ,bool β,Vector3D ͺ){var ά=ϐ.ϒ;var ͻ=Ő;Vector3D ͼ=Vector3D.Zero;var Ŋ=ª.Ǆ();var ͽ=Ŋ.ɓ().ɞ()/ª.س.
CalculateShipMass().PhysicalMass;var Ά=ͽ;var Έ=Vector3D.Zero;var Ή=Vector3D.Zero;var Ί=ͻ;var Ό=ϗ??MAX_SP;var Ύ=new BoundingSphereD(
Vector3D.Zero,ͽ);var Ώ=Vector3D.Zero;var ΐ=ª.ف.ɂ();if(ΐ==Vector3.Zero)ΐ=null;var Α=ª.ف.Ƀ();if(Ό==0)ά=ϐ.ϑ;if(Ĳ!=null){var Ȥ=Ĳ.
Value;Έ=Ȥ;Ύ.Center+=Ȥ;if(ͽ>ª.ų.œ)Ά=(float)Math.Sqrt(Math.Max(0,ͽ*ͽ-ª.ų.œ*ª.ų.œ));if(ΐ.HasValue){var Β=MatrixD.CreateFromDir(ё
.ђ(ľ.WorldMatrix.Left,Ȥ),-Ȥ);var ê=Vector3D.Rotate(ΐ.Value,Β).Normalized()*Ά;var Γ=Vector3D.ProjectOnPlane(ref ê,ref Ȥ);Έ
-=ê-Γ;Ή+=Γ;Ά=0;var ͷ=ΐ.Value;ͷ.Y*=-1;Ώ+=-Vector3D.Rotate(ͷ,Β).Normalized()*1000;ά=ϐ.ϒ;}}if(!ΐ.HasValue&&Ȟ!=Vector3D.Zero){
ά=ϐ.ϒ;var H=Ȟ.Length();var ȓ=Ȟ/H;Ί=ͻ-ͺ;if(Ί==Vector3D.Zero)Ώ=ȓ;else{var ˠ=Math.Max(Vector3D.Dot(Ί,ȓ),0);var ˡ=Vector3D.
Reject(Ί,ȓ);var ˢ=ˡ.Length();var ˣ=ˡ/ˢ;ά=ϐ.ϓ;if(!β){var ˤ=H;var ˬ=Ĳ.HasValue?œ*0.99:9f;var ˮ=ˬ*0.5;var Ͱ=Math.Sqrt(2*ˮ*ˤ);Ό=(
float)Math.Min(Ό,Ͱ);if(Ĳ!=null){if(H<20)Ό/=2;}else{if(H<20)Ό/=4;else if(H<80)Ό/=2;}var å=ȓ*Ό-Ί;if(ˠ<=0)å=ȓ*Ό-ˡ;Ώ=å;var ͱ=
Vector3D.Reject(å,ȓ);var Ͳ=å-ͱ;Ώ=Ͳ+ͱ/2;Vector3D.ClampToSphere(ref Ώ,ˬ);if(H<1){ά=ϐ.ϑ;Ώ=Vector3D.Zero;}}else{var ͳ=Math.Min(1,ˢ/
50)*0.5f;var ʹ=(Ό-ˠ)/Ό;Ώ=(-ˣ*ͳ+ȓ*ʹ)*ͽ;}}}if((ά==ϐ.ϑ)&&Ί!=Vector3D.Zero){if(Ĳ==null){Ώ=-Ί;}else{var ˠ=Ί.Length();var ˑ=Math
.Min(Ά,ˠ);var Ͷ=Ί/ˠ;var Δ=Vector3D.Reject(Ͷ,Ń);var Υ=Ͷ-Δ;var Φ=Vector3D.Dot(Ί,-Ń);var Χ=(Φ>0&&Φ<ª.ų.œ*3)?Math.Min(ª.ų.œ,ˠ
):ˑ;Ώ+=-Δ*(ˠ>8?ˑ:ˑ/2);Ώ+=-Υ*Χ;}}var Ψ=Vector3D.Zero;if(Ώ!=Vector3D.Zero){Ψ=-ΰ(Ώ,Ύ);}if(Ĳ.HasValue){Ψ+=Ĳ.Value;var Δ=
Vector3D.ProjectOnPlane(ref Ψ,ref Ń);Έ=Ψ-Δ;}bool Ω=true;var Ϊ=œ/ͽ;var Ϋ=Vector3D.Dot(ª.œ.WorldMatrix.Backward,Ń);if(Ϋ<Ϊ)Ω=false;
var έ=Ψ.Normalized();Ϗ=0;if(Ψ!=Vector3D.Zero){var Ǌ=Ώ.Normalized();if(Ĳ!=null&&Ω){var ή=Έ.Length();var ί=Έ/ή;if(Vector3D.
Dot(ª.œ.WorldMatrix.Backward,ί)>0)Ϗ=ή/Vector3D.Dot(ª.œ.WorldMatrix.Backward,ί)/ͽ;}else{if(β||Vector3D.Dot(ª.œ.WorldMatrix.
Backward,-Ǌ)>0.95)Ϗ=Math.Max(0,Vector3D.Dot(ª.œ.WorldMatrix.Backward,Ψ))/ͽ;}}return Ψ==Vector3D.Zero?Ψ:έ;}Vector3D ΰ(Vector3D Ώ,
BoundingSphereD Ύ){if(Ύ.Contains(Vector3D.Zero)==ContainmentType.Contains){var α=new RayD(Ώ,-Ώ.Normalized());if(Ύ.Contains(Ώ)==
ContainmentType.Disjoint){var H=α.Intersects(Ύ);ª.վ?.ӥ($"d {H}");var Ȅ=α.Position+α.Direction*H.Value;return Ȅ;}return Ώ;}return
Vector3D.Zero;}}class Τ{public long Ε;public string Ț;public long Ζ;public Vector3D?Η{get;private set;}public Vector3D?Ő;public
Vector3D?Θ;public MatrixD?Ι;public BoundingBoxD?Κ;public int?Λ;public MyDetectedEntityType?Μ{get;set;}public delegate void Ν();
public event Ν Ξ;public long Ο;public Τ(string ă,int?Π){Ț=ă;Λ=Π;}public void Ρ(Vector3D Ȫ){Η=Ȫ;Ε=ʓ.Ų;}public bool Σ(){if(Η.
HasValue&&(!Λ.HasValue||(ʓ.Ų-Ε<Λ)))return true;return false;}public bool ϫ(){return Η.HasValue&&Λ.HasValue&&(ʓ.Ų-Ε>Λ);}public
enum б:byte{в=1,г=2,д=4}bool е(б ж,б з){return(ж&з)==з;}public void и(long к,MyTuple<MyTuple<string,long,byte,byte>,Vector3D
,Vector3D,MatrixD,BoundingBoxD>р,int л){var м=р.Item1;Ț=м.Item1;Ζ=м.Item2;Μ=(MyDetectedEntityType)м.Item3;б н=(б)м.Item4;
var ξ=л-Ε;var M=р.Item2;if(е(н,б.в)){var о=р.Item3;if(!Ő.HasValue)Ő=о;if(ξ>0)Θ=(о-Ő.Value)*60/ξ;Ő=о;M+=о*ʓ.ʔ;}Ρ(M);if(е(н,б
.г))Ι=р.Item4;if(е(н,б.д))Κ=р.Item5;Ο=к;Ь.Ю++;}public MyTuple<MyTuple<string,long,byte,byte>,Vector3D,Vector3D,MatrixD,
BoundingBoxD>п(){var с=0|(Ő.HasValue?1:0)|(Ι.HasValue?2:0)|(Κ.HasValue?4:0);var Ë=new MyTuple<MyTuple<string,long,byte,byte>,
Vector3D,Vector3D,MatrixD,BoundingBoxD>(new MyTuple<string,long,byte,byte>(Ț,Ζ,(byte)MyDetectedEntityType.LargeGrid,(byte)с),Η.
Value,Ő??Vector3D.Zero,Ι??MatrixD.Identity,Κ??new BoundingBoxD());return Ë;}}static void Ц(IMyTimerBlock Ą){Ą.
GetActionWithName("TriggerNow").Apply(Ą);}static IEnumerable<string>Ч(IMyTerminalBlock Ą){return Ą.CustomName.Trim().Split('[').Select(M
=>M.Trim(']'));}static bool Ш(string Щ,ref Vector3D ð){string[]M=Щ.Split(':');if((M.Length<5)||(M[0]!="GPS"))return false;
Vector3D Ǧ;if(!double.TryParse(M[2],out Ǧ.X))return false;if(!double.TryParse(M[3],out Ǧ.Y))return false;if(!double.TryParse(M[4
],out Ǧ.Z))return false;ð=Ǧ;return true;}static string Ъ(params Vector3D[]Ы){return string.Join(":",Ы.Select(Ł=>string.
Format("{0}:{1}:{2}",Ł.X,Ł.Y,Ł.Z)));}static Э Ь;struct Э{public int Ю;public int Я;public int й;public int т;public int ї;}
static class ё{public static Vector3D ђ(Vector3D ѓ,Vector3D є){return Math.Abs(ѓ.Dot(є))>0.99?Vector3D.
CalculatePerpendicularVector(є):Vector3D.Cross(ѓ,є);}public static bool ѕ(Vector3D і,Vector3D M,BoundingSphereD ь,ref Vector3D ј){var A=ь.Center;if(
ь.Contains(M)==ContainmentType.Contains){ј=M+(M-A).Normalized()*ь.Radius*1.1;return false;}var љ=new RayD(M,(і-M).
Normalized());if(љ.Intersects(ь).HasValue){ь.Radius*=0.95;var њ=і==ь.Center?Vector3D.CalculatePerpendicularVector(љ.Direction)*ь.
Radius-ь.Center:і-ь.Center;var Ǧ=ь.Radius;var ћ=new RayD(M,љ.Direction);var ќ=ь.Intersects(ћ);if(ќ.HasValue){var Ř=њ.Length();
var ѓ=ђ(-ћ.Direction,њ/Ř);var ѝ=Vector3D.Cross(њ,ѓ);var ѐ=Ǧ*Ǧ/Ř;var у=Math.Asin(Ǧ/Ř);var ю=Math.Cos(у)*Ǧ;ј=A+ѐ*њ/Ř+ю*ѝ.
Normalized();return true;}}ј=і;return false;}public static Vector3D ф(Vector3D Ë,Vector3D х,BoundingSphereD ц,double ч){var ш=(ц.
Center-х).Length();var Ę=(ц.Center-х)/ш;var Ⱥ=Ë-х;var щ=Vector3D.ProjectOnPlane(ref Ⱥ,ref Ę);var ъ=ш-ц.Radius;if(ъ<ч)return х-
Ę*ч+щ.Normalized()*500;return х+щ;}public static Vector3D ы(Vector3D Ë,Vector3D х,BoundingSphereD ь){var э=Ë;var я=(ь.
Center-х).Normalized();if(ь.Contains(х)==ContainmentType.Contains)return ь.Center-я*ь.Radius*1.1;if(ь.Contains(Ë)==
ContainmentType.Contains)Ë=ь.Center+(Ë-ь.Center).Normalized()*ь.Radius*1.1;var а=Ë;var Х=Ë;var Ϻ=(Ë-х).Normalized();RayD Ǧ;if(Vector3D.
Dot(Ϻ,я)>0)Ǧ=new RayD(Ë,-Ϻ);else Ǧ=new RayD(х,Ϻ);if(Ǧ.Intersects(ь).HasValue){if((х-ь.Center).Length()>ь.Radius*1.2){ѕ(х,Ë,
ь,ref а);return а;}else{ѕ(Ë,х,ь,ref Х);return ф(Х,х,ь,ь.Radius+10);}}else return Ë;}public static Vector3D Ͻ(Vector3D Ͼ,
Vector3D Ͽ,Vector3D Ѐ,Vector3D Ё,double Ђ,ref double ȁ){double Љ=Vector3D.Distance(Ͼ,Ѐ);var ȓ=(Ѐ-Ͼ).Normalized();var Ѓ=Ѐ;Ё-=Ͽ;
var Є=Ё.Length();if(Є>float.Epsilon){var Ѕ=Ё/Є;var І=Math.PI-Math.Acos(Vector3D.Dot(ȓ,Ѕ));var Ї=Є*Math.Sin(І)/Ђ;if(Math.Abs
(Ї)<=1){var Ј=Math.Asin(Ї);var Ì=Љ*Math.Sin(Ј)/Math.Sin(І+Ј);ȁ=Ì/Є;Ѓ=Ѐ+Ѕ*Ì;}else{ȁ=-1;}}else{ȁ=Љ/Ђ;}return Ѓ;}public
static Vector3D ϻ(Vector3D Ϭ,MatrixD Ϲ,MatrixD ϭ,Vector3D Ϯ,float ϯ){var τ=Ϲ.Up;var ϰ=Vector3D.ProjectOnPlane(ref Ϭ,ref τ);var
ϱ=-(float)Math.Atan2(Vector3D.Dot(Vector3D.Cross(Ϲ.Forward,ϰ),τ),Vector3D.Dot(Ϲ.Forward,ϰ));τ=Ϲ.Right;ϰ=Vector3D.
ProjectOnPlane(ref Ϭ,ref τ);var ϲ=-(float)Math.Atan2(Vector3D.Dot(Vector3D.Cross(Ϲ.Forward,ϰ),τ),Vector3D.Dot(Ϲ.Forward,ϰ));float ϳ=0;
if(Ϯ!=Vector3D.Zero){τ=Ϲ.Forward;ϰ=Vector3D.ProjectOnPlane(ref Ϯ,ref τ);ϰ=ϰ.Normalized();ϳ=(float)Math.Atan2(Vector3D.Dot(
Vector3D.Cross(Ϲ.Up,ϰ),τ),Vector3D.Dot(Ϲ.Up,ϰ));}var χ=new Vector3D(ϲ,ϱ,ϳ*ϯ);var ϴ=Vector3D.TransformNormal(χ,Ϲ);var Ř=Vector3D.
TransformNormal(ϴ,MatrixD.Transpose(ϭ));return Ř;}public static void ɘ(IMyGyro ϵ,Vector3D Ϸ,Vector3D ϸ,Vector3D ψ){var Њ=Ϸ.Y;var М=Ϸ.X;
var Α=Ϸ.Z;var З=ϵ.CubeGrid.GridSizeEnum==MyCubeSize.Large?30:60;var ʠ=GRID_ANGULAR_ACCELERATIONS;double И=2*Math.PI/60f;
Func<double,double,double,double,double>Й=(Ë,К,Л,Ř)=>{var У=Ë<0?К-Л:Л-К;У=Math.Max(У,0);var Н=Math.Abs(Ë);double Ǧ;if(Н>У*У/
(2*Ř))Ǧ=З*Math.Sign(Ë)*Math.Max(Math.Min(Н,1),0.0002);else{Ǧ=-З*Math.Sign(Ë)*Math.Max(Math.Min(Н,1),0.0002);}return Ǧ-Л/И
;};var О=(float)Й(Њ,ϸ.Y,ψ.Y,ʠ.Y);var П=(float)Й(М,ϸ.X,ψ.X,ʠ.X);var Р=(float)Й(Α,ϸ.Z,ψ.Z,ʠ.Z);ϵ.SetValue("Pitch",-П);ϵ.
SetValue("Yaw",О);ϵ.SetValue("Roll",Р);}}class С{List<ɵ>Т;List<ɵ>Ф;List<ɵ>Ж;List<ɵ>Ћ;List<ɵ>Д;List<ɵ>Ќ;List<ɵ>Ѝ;double[]Ў=new
double[6];bool Џ;bool А;public void Б(){if(!А){Ф.ForEach(Ř=>Ř.Ƈ());Ж.ForEach(Ř=>Ř.Ƈ());Ћ.ForEach(Ř=>Ř.Ƈ());Д.ForEach(Ř=>Ř.Ƈ())
;Ќ.ForEach(Ř=>Ř.Ƈ());Ѝ.ForEach(Ř=>Ř.Ƈ());А=true;}}public BoundingBoxD В(float ə){Vector3D Г=new Vector3D(-Ў[5],-Ў[3],-Ў[1
])/ə;Vector3D Е=new Vector3D(Ў[4],Ў[2],Ў[0])/ə;return new BoundingBoxD(Г,Е);}public void ϼ(){Ў[0]=ɓ().ɞ();Ў[1]=ɠ().ɞ();Ў[
2]=ɔ().ɞ();Ў[3]=ɕ().ɞ();Ў[4]=ɗ().ɞ();Ў[5]=ɖ().ɞ();}public С(IMyTerminalBlock ʇ,List<IMyTerminalBlock>ɣ){MatrixD Ȯ=ʇ.
WorldMatrix;Func<Vector3D,List<ɵ>>ɤ=É=>{var Ǧ=ɣ.Where(Ą=>Ą is IMyThrust&&É==Ą.WorldMatrix.Forward).Select(Ë=>Ë as IMyThrust).ToList
();return Ǧ.Select(Æ=>new ɴ(Æ)).Cast<ɵ>().ToList();};Ф=ɤ(Ȯ.Backward);Ж=ɤ(Ȯ.Forward);Ћ=ɤ(Ȯ.Down);Д=ɤ(Ȯ.Up);Ќ=ɤ(Ȯ.Left);Ѝ=ɤ
(Ȯ.Right);var ɧ=ɣ.Where(Ą=>Ą is IMyArtificialMassBlock).Cast<IMyArtificialMassBlock>().ToList();if(ɧ.Count>0)MAX_SP=Math.
Max(MAX_SP,104.38f);var ɥ=ɣ.Where(Ą=>Ą is IMyGravityGenerator).Cast<IMyGravityGenerator>().ToList();Func<Vector3D,bool,List
<ɵ>>ɦ=(É,ŗ)=>{var ð=ɥ.Where(Ą=>É==Ą.WorldMatrix.Up);return ð.Select(M=>new ɨ(M,ɧ,ŗ)).Cast<ɵ>().ToList();};Ф.AddRange(ɦ(Ȯ.
Forward,true));Ж.AddRange(ɦ(Ȯ.Forward,false));Ф.AddRange(ɦ(Ȯ.Backward,false));Ж.AddRange(ɦ(Ȯ.Backward,true));Ћ.AddRange(ɦ(Ȯ.Up,
true));Д.AddRange(ɦ(Ȯ.Up,false));Ћ.AddRange(ɦ(Ȯ.Down,false));Д.AddRange(ɦ(Ȯ.Down,true));Ќ.AddRange(ɦ(Ȯ.Right,true));Ѝ.
AddRange(ɦ(Ȯ.Right,false));Ќ.AddRange(ɦ(Ȯ.Left,false));Ѝ.AddRange(ɦ(Ȯ.Left,true));ϼ();Џ=true;}public С ɓ(){Т=Ф;return this;}
public С ɠ(){Т=Ж;return this;}public С ɔ(){Т=Ћ;return this;}public С ɕ(){Т=Д;return this;}public С ɖ(){Т=Ќ;return this;}public
С ɗ(){Т=Ѝ;return this;}public void ɘ(Vector3D Ł,float ə){А=false;Func<ɵ,bool>ɚ=Ř=>!(Ř is ɨ);ɠ().ɛ(-Ł.Z/Ў[1]*ə);ɓ().ɛ(Ł.Z/
Ў[0]*ə);ɕ().ɛ(-Ł.Y/Ў[3]*ə);ɔ().ɛ(Ł.Y/Ў[2]*ə);ɖ().ɛ(-Ł.X/Ў[5]*ə);ɗ().ɛ(Ł.X/Ў[4]*ə);}public bool ɛ(double ɜ,Func<ɵ,bool>ɚ=
null){if(Т!=null){ɜ=Math.Min(1,Math.Abs(ɜ))*Math.Sign(ɜ);foreach(var ɝ in ɚ==null?Т:Т.Where(ɚ)){ɝ.ɛ(ɜ);}}Т=null;return true;
}public float ɞ(){float ɟ=0;if(Т!=null){foreach(var ɝ in Т){ɟ+=ɝ.ɶ();}}if(Џ&&(ɟ==0||double.IsNaN(ɟ)))ɟ=1000000;Т=null;
return ɟ;}}class ɨ:ɵ{IMyGravityGenerator ð;List<IMyArtificialMassBlock>ɱ;bool ɲ;public ɨ(IMyGravityGenerator ð,List<
IMyArtificialMassBlock>ɱ,bool ɲ){this.ð=ð;this.ɱ=ɱ;this.ɲ=ɲ;}public void ɛ(double ɳ){if(ɳ>=0)ð.GravityAcceleration=(float)(ɲ?-ɳ:ɳ)*G;}public
void Ƈ(){ð.GravityAcceleration=0;}public float ɶ(){return ɱ.Count*50000*G;}}class ɴ:ɵ{IMyThrust Æ;public ɴ(IMyThrust Æ){this
.Æ=Æ;}public void ɛ(double ɳ){if(ɳ<=0)Æ.ThrustOverride=0.00000001f;else Æ.ThrustOverride=(float)ɳ*Æ.MaxThrust;}public
void Ƈ(){Æ.ThrustOverride=0;Æ.Enabled=true;}public float ɶ(){return Æ.MaxEffectiveThrust;}}interface ɵ{void ɛ(double ɳ);
float ɶ();void Ƈ();}ɰ ɷ;class ɰ{public long ɩ;public ڷ ɯ;public HashSet<long>ɪ=new HashSet<long>();ؿ ş;int Ì;int Ĵ;int Ǧ;
Dictionary<long,MyTuple<int,Vector3D>>Ë=new Dictionary<long,MyTuple<int,Vector3D>>();bool Ƹ;Dictionary<string,int>ɫ=new Dictionary
<string,int>();public ɰ(int ƌ,int ɬ,int ɭ,ؿ ª){Ì=ƌ;Ĵ=ɬ;Ǧ=ɭ;ş=ª;ɫ["base-fw"]=100;ɫ["base-up"]=30;ɫ["interval"]=50;ɫ[
"echelon"]=20;ɫ["circle"]=350;ɯ=new ڷ("apck-wingman",false,ª.ر,Ë=>true);}public void ɮ(string ɡ,string ɒ){int Ł;if(int.TryParse(ɒ
,out Ł)&&ɫ.ContainsKey(ɡ))ɫ[ɡ]=ɒ.Contains('-')||ɒ.Contains('+')?ɫ[ɡ]+Ł:Ł;}public void ŕ(){if(!Ƹ){var ȷ=ş.ذ.
RegisterBroadcastListener("apck.report");if(ʓ.Ų>Ì){if(ʓ.Ų<Ĵ){while(ȷ.HasPendingMessage){Ь.й++;var Ü=ȷ.AcceptMessage();var H=(MyTuple<MyTuple<
string,byte,int,long>,Vector3D,Vector3D,Vector3D,string>)Ü.Data;if(!Ë.ContainsKey(Ü.Source))Ë.Add(Ü.Source,new MyTuple<int,
Vector3D>(H.Item1.Item3,H.Item2));}}else{var ȸ=Ë.Where(Æ=>Æ.Value.Item1==Ǧ-1).OrderBy(Æ=>(Æ.Value.Item2-ş.ʆ).LengthSquared());if
(ȸ.Count()>0){var ȹ=ȸ.First();if((ȹ.Value.Item2-ş.ʆ).Length()<ş.ـ.Get<float>("tg-autolink-range")){ɩ=ȸ.First().Key;ɯ.ڰ(ş,
ş.ذ.Me,ɩ);ş.Ѥ.C("apck.tac.addchild",0,ɩ);}}ş.ذ.DisableBroadcastListener(ȷ);Ƹ=true;}}}}public Τ Ⱥ=new Τ("wingman",null);
int Ȼ;int ȼ;List<ؿ>Ƚ=new List<ؿ>();public void Ⱦ(List<ؿ>ȿ){Ȼ=ȳ.Count;ȼ=0;Ⱥ.Ő=ş.ų.Ő;Ⱥ.Ι=ş.ų.ľ.WorldMatrix;Ⱥ.Κ=new
BoundingBoxD(ş.œ.CubeGrid.WorldVolume.Center,new Vector3D(ş.œ.CubeGrid.WorldVolume.Radius,0,0));Ƚ.Clear();foreach(var B in ȿ)if(B.Ɖ.
ѽ()?.ſ=="wingman")Ƚ.Add(B);Ȼ+=Ƚ.Count;foreach(var B in Ƚ){var Ȫ=ȶ();var ɀ=B.Ɖ.ѽ().ڔ.ؾ;ɀ.Ρ(Ȫ);ɀ.Ő=Ⱥ.Ő;ɀ.Ι=Ⱥ.Ι;ɀ.Κ=Ⱥ.Κ;}}
Vector3D ȶ(){var ê=ş.ų.ľ.WorldMatrix;var ȫ=ê.Translation+ê.Forward*ɫ["base-fw"]+ê.Up*ɫ["base-up"];double Ȭ=2*Math.PI/Ȼ;Vector3D
Ȫ;if(ş.D.Ӱ("wingman-circle-rotation"))Ȫ=ȭ(ȫ,ref ê,ɫ["circle"],Ȭ*ȼ++);else{ȼ=(ȼ>0)?ȼ*(-1):(Math.Abs(ȼ)+1);Ȫ=ȫ+ê.Left*ȼ*ɫ[
"interval"]+ê.Up*Math.Abs(ȼ)*ɫ["echelon"];}return Ȫ;}Vector3D ȭ(Vector3D Ë,ref MatrixD Ȯ,double Ǧ,double ȯ){var Ȱ=30/(800f/Ǧ);
return Ë+Ȯ.Right*Math.Cos(Math.PI*(ʓ.D/Ȱ)+ȯ)*Ǧ+Ȯ.Up*Math.Sin(Math.PI*(ʓ.D/Ȱ)+ȯ)*Ǧ;}void ȱ(){foreach(var Ȳ in ȳ){var Ȫ=ȶ();Ⱥ.Ρ(
Ȫ);ş.ذ.SendUnicastMessage(Ȳ,"apck-wingman",Ⱥ.п());}}HashSet<long>ȳ=new HashSet<long>();public void ȴ(Ղ ȵ){var Ȫ=new Լ();Ȫ
.Հ=(Ý,J)=>ȳ.Add(Ý);Ȫ.Խ=(Ý,J)=>ȳ.Remove(Ý);Ȫ.Ձ=ȱ;ȵ.Յ.Add("apck-wingman",Ȫ);}}bool ɉ(ɋ Ɋ){return(ӌ?.Ƴ&Ɋ)==Ɋ;}public enum ɋ:
byte{Ɍ=1,ɍ=2,Ɏ=4,ɏ=8}List<string>ɑ(string À){return À.Split(new[]{"],["},StringSplitOptions.RemoveEmptyEntries).Select(Ì=>Ì.
Trim('[',']')).ToList();}class ɐ{public List<IMyShipController>ƺ;ӫ ś;public ɐ(List<IMyShipController>A,ӫ Æ){ś=Æ;ƺ=A;}public
Vector3 ɂ(ref MatrixD Ɂ){Vector3 Ȅ=new Vector3();if(ś.Ӱ("ignore-user-thruster"))return Ȅ;var A=ƺ.Where(Ë=>Ë.IsUnderControl).
FirstOrDefault();if(A!=null&&(A.MoveIndicator!=Vector3.Zero))return Vector3.TransformNormal(A.MoveIndicator,Ɂ*MatrixD.Transpose(A.
WorldMatrix));return Ȅ;}public Vector3?ɂ(){return ƺ.Where(Ë=>Ë.IsUnderControl).FirstOrDefault()?.MoveIndicator;}public float?Ƀ(){
return ƺ.Where(Ë=>Ë.IsUnderControl).FirstOrDefault()?.RollIndicator;}}static bool Ʉ(int Ʌ,string[]Ǥ,out Dictionary<string,
string>Ɇ){if((Ǥ.Length>Ʌ)&&Ǥ[3].Contains("=")){Ɇ=Ǥ[Ʌ].Split(',').ToDictionary(Ì=>Ì.Split('=')[0],Ì=>Ì.Split('=')[1]);return
true;}Ɇ=null;return false;}static D ɇ<D>(Dictionary<string,string>Ɉ,string ɡ){string Ȅ;if((Ɉ!=null)&&Ɉ.TryGetValue(ɡ,out Ȅ)
&&!string.IsNullOrEmpty(Ȅ)){if(typeof(D)==typeof(string))return(D)(object)Ȅ;else if(typeof(D)==typeof(int?))return(D)(
object)int.Parse(Ȅ);else if(typeof(D)==typeof(long?))return(D)(object)long.Parse(Ȅ);else if(typeof(D)==typeof(float?))return(D
)(object)float.Parse(Ȅ);}return default(D);}ʯ ɸ;class ʯ{public HashSet<Vector3I>ʰ=new HashSet<Vector3I>();public List<ě>ʱ
=new List<ě>();Action<string>ħ;public string ʲ;public List<string>ʳ=new List<string>();public ʯ(Action<string>ʴ){ħ=ʴ;}
public ʯ ʵ(string ʶ){if(!string.IsNullOrEmpty(ʶ)){var Ɉ=ʶ.Split('\n').ToDictionary(Ì=>Ì.Split('=')[0],Ì=>string.Join("=",Ì.
Split('=').Skip(1)));var Ĵ=ɇ<string>(Ɉ,"defs");if(Ĵ!=null){var ʩ=Ĵ.Split(new[]{'|'},StringSplitOptions.RemoveEmptyEntries);
foreach(var H in ʩ){var Ë=H.Split(':');ʰ.Add(new Vector3I(int.Parse(Ë[0]),int.Parse(Ë[1]),int.Parse(Ë[2])));}}Ĵ=ɇ<string>(Ɉ,
"navs-nodes");if(Ĵ!=null){var ʩ=Ĵ.Split(new[]{'|'},StringSplitOptions.RemoveEmptyEntries);ʱ=new List<ě>(ʩ.Length);foreach(var H in ʩ
){var Ë=H.Split(':');var M=new Vector3D(double.Parse(Ë[0]),double.Parse(Ë[1]),double.Parse(Ë[2]));ʱ.Add(new ě(){ĝ=M});}Ĵ=
ɇ<string>(Ɉ,"navs-links");if(Ĵ!=null){foreach(var H in Ĵ.Split(new[]{'|'},StringSplitOptions.RemoveEmptyEntries)){var Ë=H
.Split(':');var Ř=ʱ.ElementAt(int.Parse(Ë[0]));var Ą=ʱ.ElementAt(int.Parse(Ë[1]));var ʨ=(Ř.ĝ-Ą.ĝ).Length();Ř.Ğ.Add(new
MyTuple<ě,double>(Ą,ʨ));Ą.Ğ.Add(new MyTuple<ě,double>(Ř,ʨ));}}Ĵ=ɇ<string>(Ɉ,"navs-entries");if(Ĵ!=null){foreach(var È in Ĵ.
Split(new[]{'|'},StringSplitOptions.RemoveEmptyEntries)){ʱ[int.Parse(È)].Ĝ=true;}}}Ĵ=ɇ<string>(Ɉ,"saved-task-q");if(Ĵ!=null){
var ʩ=Ĵ.Split(new[]{'|'},StringSplitOptions.RemoveEmptyEntries);ʳ.AddRange(ʩ);}ʲ=ɇ<string>(Ɉ,"default-task");}return this;}
public void ʪ(){ħ(ˉ());}string ʫ(){var ʩ=new List<string>();foreach(var H in ʰ){ʩ.Add(string.Join(":",string.Format(
"{0}:{1}:{2}",H.X,H.Y,H.Z)));}return string.Join("|",ʩ);}string ʬ(){var ʩ=new List<string>();var ʭ=new List<string>();var ʮ=new List<
string>();foreach(var Ë in ʱ){ʩ.Add(string.Join(":",string.Format("{0}:{1}:{2}",Ë.ĝ.X,Ë.ĝ.Y,Ë.ĝ.Z)));if(Ë.Ĝ){ʮ.Add(ʱ.IndexOf(Ë
).ToString());}foreach(var Ą in Ë.Ğ){ʭ.Add(string.Join(":",string.Format("{0}:{1}",ʱ.IndexOf(Ë),ʱ.IndexOf(Ą.Item1))));}}
return$"navs-nodes={string.Join("|",ʩ)}\nnavs-links={string.Join("|",ʭ)}\nnavs-entries={string.Join(" | ",ʮ)}";}string ˉ(){
string[]ʾ=new string[]{"defs="+ʫ(),"ts="+DateTime.Now.ToShortDateString(),"default-task="+ʲ,"saved-task-q="+string.Join("|",ʳ)
,ʬ()};return string.Join("\n",ʾ);}public override string ToString(){return ˉ();}}ˀ ʿ;class ˀ{public ʏ ˁ=new ʏ();public ڷ
ˆ;ؿ ˇ;List<MyTuple<long,Vector3D>>ˈ=new List<MyTuple<long,Vector3D>>();public void Ɣ(ؿ ª){ˇ=ª;ˆ=new ڷ(
"tgp.global.gridsense.offer",true,ª.ر).ڭ(null);}bool ˏ;public void ˊ(){if(!ˏ){ˆ.ډ(ˇ);ˏ=true;}}int ˋ;public void ʃ(List<ؿ>ˌ,List<MyIGCMessage>ˍ){ˁ.ʃ(
);ˇ.ر.թ(ˆ,ref ː);ˋ=0;ʽ(ˇ);foreach(var ª in ˌ)ʽ(ª);ˇ.վ?.ӥ($"interested: {ˋ}");if(ˋ==0){ˆ.ڱ();ˏ=false;}else ˊ();}HashSet<
long>ˎ=new HashSet<long>();List<MyIGCMessage>ː=new List<MyIGCMessage>();void ʽ(ؿ ª){ª.Ɖ.Ѻ(ʹ,ʷ);if(ª.م&&ª.Ɖ.Ҟ){ˋ++;if(ˁ.ʑ){ª.
Ɖ.ҫ(ˁ.ɺ);}else{ˎ.Clear();foreach(var Ü in ː){var ʊ=(MyTuple<long,Vector3D>)Ü.Data;var Æ=new ʋ{ʍ=ʊ.Item1,ʎ=ʊ.Item2,ʌ=Ü.
Source};if(ˎ.Add(ʊ.Item1)){ª.Ɖ.Ҡ(Æ);}}}}}TargetSelection ʷ=TargetSelection.Loop;public void ʸ(string Ɓ){Enum.TryParse(Ɓ,out ʷ)
;}int ʹ;public void ʺ(ʋ Æ,ؿ ª){ª.Ɖ.ҭ(Æ.ʍ,Æ.ʌ,()=>ª.Ѥ.C("tgp.global.gridsense.t-",Æ.ʍ,Æ.ʌ));ʹ++;}Dictionary<long,HashSet<
long>>ʻ=new Dictionary<long,HashSet<long>>();public void ȴ(Ղ ȵ){var ʼ=new Լ();ʼ.Հ=(Ý,J)=>{if(!ʻ.ContainsKey(J))ʻ.Add(J,new
HashSet<long>());ʻ[J].Add(Ý);};ʼ.Խ=(Ý,J)=>{if(J==0){foreach(var Ì in ʻ)Ì.Value.Remove(Ý);}else ʻ[J].Remove(Ý);};ʼ.Ծ=(J)=>ˁ.ʑ&&ˁ
.ɺ.Ζ==J;ʼ.Կ=(ʈ,Ì,M,Ǌ,Ǧ)=>{bool ʉ=ˁ.ʑ&&Ղ.Ժ(M,Ǌ,Ǧ,ˁ.ʆ);if(ʉ&&(ʈ==null||(ʈ==ˁ.ɺ.Μ.ToString())))return ˁ.ɺ.Ζ;return-1;};ʼ.Ձ=(
)=>{var Ⱥ=ˁ.ɺ;if(ˁ.ʑ&&Ⱥ.Η.HasValue){var ʊ=Ⱥ.п();var ʐ=new MyTuple<long,Vector3D>(Ⱥ.Ζ,Ⱥ.Η.Value);ˇ.ذ.SendBroadcastMessage(
"tgp.global.gridsense.offer",ʐ,TransmissionDistance.AntennaRelay);if(ʻ.ContainsKey(Ⱥ.Ζ)){var ǣ=ʻ[Ⱥ.Ζ];if(ǣ.Count>0){foreach(var Ì in ǣ)ˇ.ذ.
SendUnicastMessage(Ì,"tgp.local.gridsense.update",ʊ);}else ʻ.Remove(Ⱥ.Ζ);}ˇ.ذ.SendBroadcastMessage("tgp.global.gridsense.update",ʊ,
TransmissionDistance.AntennaRelay);}};ȵ.Յ.Add("tgp.local.gridsense.update",ʼ);}}struct ʋ{public long ʌ;public long ʍ;public Vector3D ʎ;}
class ʏ{public bool ʑ=>ɻ;public Vector3D ʆ=>ɼ;public Vector3D ɹ=>ɽ;public bool ʅ=true;public Τ ɺ=new Τ("tdesignator",12);bool
ɻ;Vector3D ɼ;Vector3D ɽ;MyDetectedEntityInfo ɾ;HashSet<IMyLargeTurretBase>ɿ=new HashSet<IMyLargeTurretBase>();Func<
MyDetectedEntityInfo,bool>ʀ;ؿ ş;public void ʁ(ؿ ª){ş=ª;foreach(var A in ª.ج){ɿ.Add(A);}ʀ=ʞ;}public void ʂ(Func<MyDetectedEntityInfo,bool>Ƹ){
ʀ=Ƹ;}public void ʃ(){bool ʄ=false;ɻ=false;if(!ʅ)return;foreach(var H in ɿ.Where(Ë=>Ë.HasTarget)){var È=H.
GetTargetedEntity();if(!È.IsEmpty()){if(ʄ){ɻ=true;}else{ʄ=true;if(ʀ!=null)ʄ=ʀ(È);if(ʄ){ɻ=true;ɽ=È.Velocity;ɾ=È;if(ş.D.Ӱ("aim-to-center"))
ɼ=È.BoundingBox.Center;else ɼ=È.HitPosition.Value;}else{ɻ=false;H.ResetTargetingToDefault();}}}}if(ɻ){ɺ.Ρ(ʆ);ɺ.Ő=ɹ;ɺ.Κ=ɾ.
BoundingBox;ɺ.Ι=ɾ.Orientation;ɺ.Ζ=ɾ.EntityId;ɺ.Μ=ɾ.Type;}}long ʛ;Vector3D ʜ;int ʝ;bool ʞ(MyDetectedEntityInfo Ë){bool Ȅ=true;var ʟ=
ş.ـ.Get<float>("filtering-size");if(ʟ<=0||ʟ<=Ë.BoundingBox.Extents.Length())Ȅ=true;else{Ȅ=false;}if(ş.D.Ӱ(
"freefall-target-filter")){if(ʛ==Ë.EntityId){if(Ë.Velocity.LengthSquared()>0){var ʠ=(Ë.Velocity-ʜ)*60;if((ʠ==Vector3D.Zero)||(ş.ų.Ĳ==ʠ))ʝ++;if(ʝ
>10)Ȅ=false;}}else{ʛ=Ë.EntityId;ʜ=Ë.Velocity;ʝ=0;}}return Ȅ;}}Queue<ʡ>ɜ;void ʧ(){if(ɜ.Count>0){var A=ɜ.Peek();if(A.ʢ<ʓ.Ų)
{A.ʣ.Invoke();if(A.ʤ?.Invoke()==true){A.ʢ=ʓ.Ų+A.ʥ;}else ɜ.Dequeue();}}}class ʡ{public int ʢ;public Action ʣ;public Func<
bool>ʤ;public int ʥ;public static ʡ ʦ(Action Ä,int ʚ,Func<bool>ʒ=null){return new ʡ(){ʣ=Ä,ʥ=ʚ,ʤ=ʒ};}public void ʘ(Queue<ʡ>ɜ)
{ʢ=ʓ.Ų+ʥ;ɜ.Enqueue(this);}}static class ʓ{static Action<string>Ĵ;static IMyTextSurface M;public static double D;public
static int Ų;public static double ʔ=1/60f;public static Action<bool,string>ʕ;static IMyIntergridCommunicationSystem È;public
static void Ɣ(Program â){Ĵ=â.Echo;M=â.Me.GetSurface(0);M.ContentType=ContentType.TEXT_AND_IMAGE;M.WriteText("");È=â.IGC;}
public static void ʖ(string Ì){Ĵ(Ì);}public static void ʗ(string Ì){M.WriteText($"{Ų}: {Ì}\n",true);}public static Action<
string>څ;public static Action ʙ;}