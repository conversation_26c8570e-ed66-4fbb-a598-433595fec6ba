
#region SETUP
string VERSION = "Ver_008_Updated";
string SHIP_CLASS = "FRIGATE";
DRONE_INFO MEINFO = new DRONE_INFO();
bool[] FC_CENTRAL_RUN = new bool[] { false, true, true, false, false, true, false, false, false, true };
int RUN_ASSIGNMENT;
string CONNECTOR_PLANE;
double SHIP_ANGULAR_ACCEL = 0.6;
double MAX_SPEED = 80;
double SHIP_DOCK_ANGULAR_SPEED = 0.3;
int INITIAL_HEALTH;
int PROJECTILE_VELOCITY = 300;

// Missing variable declarations
bool IGC_INITIALIZED = false;


            const string IGCTagOUT = "RFC_FLEET_IN";
            const string IGCTagIN = "RFC_FLEET_OUT";
            string PendingSend ="";

            IMyBroadcastListener Lstn;


            IMyRemoteControl RC;
            IMyShipConnector CONNECTOR;
            IMyLargeTurretBase DIRECTOR;
            List<IMyLargeTurretBase> DIRECTORS = new List<IMyLargeTurretBase>();
            IMyRadioAntenna RADIO;
            IMyGyro GYRO;
            IMyTimerBlock HW_TIMER;
            List<IMyTerminalBlock> CONTROLLERS = new List<IMyTerminalBlock>();
            List<IMyTerminalBlock> CAMERAS = new List<IMyTerminalBlock>();
            List<IMyTerminalBlock> DIRECTIONAL_FIRE = new List<IMyTerminalBlock>();


            class DRONE_INFO
            {
                public string ID;
                public string COMLOC;
                public string GLOC;
                public Vector3D LOC;
                public Vector3D VEL;
                public Vector3D TVEl;
                public string ISDOCKED;
                public double HEALTH;
                public DateTime LAST_PING;
                public string EXT_INF;
                public string OUTPUT;


                public static DRONE_INFO DRONE_DATA_RS(string IN_ARG, DRONE_INFO DRONE_INF, bool[] RUN_ID)
                {

                    string[] DRN_INFO = IN_ARG.Split('*');
                    DRONE_INF.ID = (RUN_ID[0] != true) ? DRONE_INF.ID : DRN_INFO[0];
                    DRONE_INF.COMLOC = (RUN_ID[1] != true) ? DRONE_INF.COMLOC : DRN_INFO[1];
                    DRONE_INF.GLOC = (RUN_ID[2] != true) ? DRONE_INF.GLOC : DRN_INFO[2];
                    if (RUN_ID[3] == true) { Vector3D.TryParse(DRN_INFO[3], out DRONE_INF.LOC); }
                    if (RUN_ID[4] == true) { Vector3D.TryParse(DRN_INFO[4], out DRONE_INF.VEL); }
                    if (RUN_ID[5] == true) { Vector3D.TryParse(DRN_INFO[5], out DRONE_INF.TVEl); }
                    if (RUN_ID[6] == true) { DRONE_INF.ISDOCKED = DRN_INFO[6]; }
                    if (RUN_ID[7] == true) { DRONE_INF.HEALTH = double.Parse(DRN_INFO[7]); }
                    if (RUN_ID[8] == true) { DRONE_INF.LAST_PING = DateTime.Parse(DRN_INFO[8]); }
                    if (RUN_ID[9] == true) { DRONE_INF.EXT_INF = DRN_INFO[9]; }
                    return DRONE_INF;
                }

                public static DRONE_INFO SAVE(DRONE_INFO DRONE_INF)
                {
                    DRONE_INF.OUTPUT = string.Join("*", "#" + DRONE_INF.ID, DRONE_INF.COMLOC, DRONE_INF.GLOC, DRONE_INF.LOC, DRONE_INF.VEL, DRONE_INF.TVEl, DRONE_INF.ISDOCKED, DRONE_INF.HEALTH, DRONE_INF.LAST_PING, DRONE_INF.EXT_INF, "#" + DRONE_INF.ID);
                    return DRONE_INF;
                }
            }
            class DOCKPOINT_INFO
            {
                public string ID;
                public Vector3D LOC;
                public string BASE_TAG;
                public string ISDOCKED;
                public DateTime LAST_PING;
                public string OUTPUTROLL;
                public string OUTPUT;

                public List<IMyTerminalBlock> ROUTE;


                public static DOCKPOINT_INFO DOCK_DATA_RS(string IN_ARG, DOCKPOINT_INFO DOCKPT_INF, bool[] RUN_ID)
                {

                    string[] DCK_INFO = IN_ARG.Split('*');
                    if (RUN_ID[0] == true) { DOCKPT_INF.ID = DCK_INFO[0]; }
                    if (RUN_ID[1] == true) { Vector3D.TryParse(DCK_INFO[1], out DOCKPT_INF.LOC); }
                    if (RUN_ID[2] == true) { DOCKPT_INF.BASE_TAG = DCK_INFO[2]; }
                    if (RUN_ID[3] == true) { DOCKPT_INF.ISDOCKED = DCK_INFO[3]; }
                    if (RUN_ID[4] == true) { DOCKPT_INF.LAST_PING = DateTime.Parse(DCK_INFO[4]); }
                    if (RUN_ID[5] == true) { DOCKPT_INF.OUTPUTROLL = DCK_INFO[5]; }

                    DOCKPT_INF.OUTPUT = string.Join("*", "#" + DOCKPT_INF.ID, DOCKPT_INF.LOC, DOCKPT_INF.BASE_TAG, DOCKPT_INF.ISDOCKED, DOCKPT_INF.LAST_PING, DOCKPT_INF.OUTPUTROLL, "#" + DOCKPT_INF.ID);
                    return DOCKPT_INF;
                }


                public static DOCKPOINT_INFO SAVE_ROUTE_TO_STRING(DOCKPOINT_INFO DOCKPT_INFO)
                {
                    List<string> OUTPUT = new List<string>();
                    double OFFSET_CONST = 4;
                    List<IMyTerminalBlock> DOCKPT_TRAIL = DOCKPT_INFO.ROUTE;


                    OUTPUT.Add(Vector3D.Round(DOCKPT_TRAIL[0].GetPosition() + DOCKPT_TRAIL[0].WorldMatrix.Forward * (1.5), 2) + "");
                    OUTPUT.Add(Vector3D.Round(DOCKPT_TRAIL[0].GetPosition() + DOCKPT_TRAIL[0].WorldMatrix.Forward * OFFSET_CONST, 2) + "");


                    for (int i = 1; i < DOCKPT_TRAIL.Count; i++)
                    { var IMYPLACE = DOCKPT_TRAIL[i]; OUTPUT.Add(Vector3D.Round(IMYPLACE.GetPosition() + IMYPLACE.WorldMatrix.Backward * OFFSET_CONST, 2) + ""); }


                    OUTPUT.Add(Vector3D.Round(DOCKPT_TRAIL[DOCKPT_TRAIL.Count - 1].GetPosition() +
                        DOCKPT_TRAIL[DOCKPT_TRAIL.Count - 1].WorldMatrix.Backward * OFFSET_CONST + DOCKPT_TRAIL[DOCKPT_TRAIL.Count - 1].WorldMatrix.Up * 100, 2) + "");



                    OUTPUT.Reverse();
                    DOCKPT_INFO.OUTPUTROLL = string.Join("^", OUTPUT);
                    DOCKPT_INFO.LOC = Vector3D.Round(DOCKPT_TRAIL[0].GetPosition(), 2);
                    DOCKPT_INFO.OUTPUT = string.Join("*", "#" + DOCKPT_INFO.ID, DOCKPT_INFO.LOC, DOCKPT_INFO.BASE_TAG, DOCKPT_INFO.ISDOCKED, DOCKPT_INFO.LAST_PING, DOCKPT_INFO.OUTPUTROLL, "#" + DOCKPT_INFO.ID);

                    return DOCKPT_INFO;
                }

            }
            Dictionary<string, DOCKPOINT_INFO> DOCKPOINTS = new Dictionary<string, DOCKPOINT_INFO>();


            bool FIRSTSETUP_HASRUN = false;
            List<Vector3D> COORDINATES = new List<Vector3D>();
            int COORD_ID = 0;
            bool ENEMY_DETECTED;


            bool DOCKING_INIT;

            #endregion





            #region MAIN METHOD (updated for current SE)


            void Main(string argument, UpdateType updateSource)
            {
                try
                {

                    if (!IGC_INITIALIZED)
                    {
                        Runtime.UpdateFrequency = UpdateFrequency.Update100;
                        Lstn = IGC.RegisterBroadcastListener(IGCTagIN);
                        Echo("Fleet Command Script Ver_008_Updated Initialized");
                        Echo("IGC Communication System Active");
                        Echo($"Listening on channel: {IGCTagIN}");
                        Echo($"Sending on channel: {IGCTagOUT}");
                        IGC_INITIALIZED = true;
                    }


                    if (updateSource.HasFlag(UpdateType.IGC) || Lstn.HasPendingMessage)
                    {
                        var message = Lstn.AcceptMessage();
                        argument = message.Data.ToString();
                        Echo($"[IGC] Received: {argument.Substring(0, Math.Min(50, argument.Length))}...");
                    }
                    else if (string.IsNullOrEmpty(argument))
                    {

                        if (!string.IsNullOrEmpty(PendingSend))
                        {
                            IGC.SendBroadcastMessage(IGCTagOUT, PendingSend, TransmissionDistance.TransmissionDistanceMax);
                            Echo($"[IGC] Sent: {PendingSend.Substring(0, Math.Min(50, PendingSend.Length))}...");
                            PendingSend = "";
                        }
                        return;
                    }
                    Echo($"[DEBUG] Processing argument: {argument.Substring(0, Math.Min(30, argument.Length))}...");



                    if (argument.Contains("BRDCST") == false)
                    {
                        Echo($"[DEBUG] Ignoring non-BRDCST message: {argument}");
                        return;
                    }
                    Echo("[DEBUG] Processing BRDCST message");



                    #region Error Readouts
                    OP_BAR();
                    Echo(VERSION);
                    if (RC == null || RC.CubeGrid.GetCubeBlock(RC.Position) == null)
                    { FIRSTSETUP_HASRUN = false; Echo("No RFC_RC Found"); RC = null; }
                    if (CAMERAS.Count == 0)
                    { FIRSTSETUP_HASRUN = false; Echo("No CAMERAS Found"); }
                    if (CONNECTOR == null || CONNECTOR.CubeGrid.GetCubeBlock(CONNECTOR.Position) == null)
                    { FIRSTSETUP_HASRUN = false; Echo("No RFC_CONNECTOR Found"); CONNECTOR = null; }
                    if (DIRECTOR == null || DIRECTOR.CubeGrid.GetCubeBlock(DIRECTOR.Position) == null)
                    { FIRSTSETUP_HASRUN = false; Echo("No RFC_TURRET Found"); DIRECTOR = null; }
                    if (RADIO == null || RADIO.CubeGrid.GetCubeBlock(RADIO.Position) == null)
                    { FIRSTSETUP_HASRUN = false; Echo("No RADIO  Found"); RADIO = null; }
                    if (GYRO == null || GYRO.CubeGrid.GetCubeBlock(GYRO.Position) == null)
                    { FIRSTSETUP_HASRUN = false; Echo("No RFC_GYRO  Found"); GYRO = null; }
                    #endregion



                    if (FIRSTSETUP_HASRUN == false)
                    { FIRST_TIME_SETUP(); FIRSTSETUP_HASRUN = true; Echo("System Booting"); return; }



                    if ((double)DateTime.Now.Second / RUN_ASSIGNMENT % 1 == 0 && !argument.Contains("#" + MEINFO.ID))
                    {
                        MEINFO.LAST_PING = DateTime.Now;
                        MEINFO = DRONE_INFO.SAVE(MEINFO);
                        PendingSend = MEINFO.OUTPUT;

                        IGC.SendBroadcastMessage(IGCTagOUT, PendingSend, TransmissionDistance.TransmissionDistanceMax);
                        Echo($"[IGC] Status update sent: {PendingSend.Substring(0, Math.Min(30, PendingSend.Length))}...");
                    }
                    if (!argument.Contains("#" + MEINFO.ID))
                    { Echo($"[DEBUG] No command for this drone ({MEINFO.ID})"); return; }
                    int START_POS = argument.IndexOf("#" + MEINFO.ID);
                    int ENDPOS = argument.LastIndexOf("#" + MEINFO.ID);
                    string droneData = argument.Substring(START_POS + 1, ENDPOS - START_POS);
                    Echo($"[DEBUG] Processing drone data: {droneData.Substring(0, Math.Min(50, droneData.Length))}...");
                    MEINFO = DRONE_INFO.DRONE_DATA_RS(droneData, MEINFO, FC_CENTRAL_RUN);
                    if (MEINFO.COMLOC.Contains("DOCK") == false)
                    { COORD_ID = 0; }




                    var CENTRAL_COMMAND = GridTerminalSystem.GetBlockWithName("CENTRAL_COMMAND") as IMyProgrammableBlock;
                    bool ALL_RUN = true;
                    foreach (var item in CONTROLLERS)
                    { if ((item as IMyShipController).IsUnderControl || CENTRAL_COMMAND != null) { ALL_RUN = false; } }
                    DRONE_PRI_LOGIC_ST1();
                    if (ALL_RUN)
                    { DRONE_PRI_LOGIC_ST2(); }



                    string DOCKOUTPUT = "";
                    if (SHIP_CLASS.Contains("IN") == false && SHIP_CLASS.Contains("IB") == false)
                    { DOCKOUTPUT = DOCK_SYST(); }



                    Me.CustomData = COORD_ID + "#" + SHIP_CLASS;
                    MEINFO.HEALTH = Math.Round(((int)((Me.CubeGrid.Max - Me.CubeGrid.Min).Length())) + ((Me.CubeGrid.Max - Me.CubeGrid.Min).Length() - 0.1 / INITIAL_HEALTH), 4);
                    MEINFO = DRONE_INFO.SAVE(MEINFO);
                    string DEI_OUT = "";
                    if (SCAN_HARDLOCK)
                    { DEI_OUT = "#" + "DEI" + SCAN_LOCKED_SHIP.ID + "^" + (int)SCAN_LOCKED_SHIP.SIZE + "^" + "EN" + "^" + Vector3D.Round(SCAN_LOCKED_SHIP.POSITION, 2); }
                    string fullOutput = MEINFO.OUTPUT + DOCKOUTPUT + DEI_OUT;
                    PendingSend = fullOutput;


                    IGC.SendBroadcastMessage(IGCTagOUT, fullOutput, TransmissionDistance.TransmissionDistanceMax);
                    Echo(fullOutput);


                    if (CENTRAL_COMMAND != null) { CENTRAL_COMMAND.TryRun(fullOutput); }
                }
                catch (Exception e)
                { Echo(e + ""); }
            }

            #endregion

            #region First Time Setup #RFC#                        

            void FIRST_TIME_SETUP()
            {




                try
                {
                    var TESTBOOL = GridTerminalSystem.GetBlockWithName("RFC_CONSTRUCT").CubeGrid == Me.CubeGrid;
                    Echo("Ship Still Under Construction, Detatch To Init");
                    return;
                }
                catch { }


                try
                {
                    SHIP_CLASS = Me.CustomData.Split('#')[1];
                }
                catch { }


                try
                {
                    List<IMyTerminalBlock> TEMP_CON = new List<IMyTerminalBlock>();
                    GridTerminalSystem.GetBlocksOfType<IMyShipConnector>(TEMP_CON, b => b.CubeGrid == Me.CubeGrid);
                    CONNECTOR = TEMP_CON[0] as IMyShipConnector;
                }
                catch { }


                try
                {
                    GridTerminalSystem.GetBlocksOfType<IMyCameraBlock>(CAMERAS);
                    foreach (var item in CAMERAS)
                    { (item as IMyCameraBlock).EnableRaycast = true; }
                }
                catch { }


                try
                {
                    List<IMyTerminalBlock> TEMP_RC = new List<IMyTerminalBlock>();
                    GridTerminalSystem.GetBlocksOfType<IMyRemoteControl>(TEMP_RC, b => b.CubeGrid == Me.CubeGrid && b.CustomName == "RFC_RC");
                    RC = TEMP_RC[0] as IMyRemoteControl;
                }
                catch { }


                try
                {
                    List<IMyTerminalBlock> TEMP_TUR = new List<IMyTerminalBlock>();
                    GridTerminalSystem.GetBlocksOfType<IMyLargeTurretBase>(DIRECTORS, b => b.CubeGrid == Me.CubeGrid);
                    DIRECTOR = DIRECTORS[0] as IMyLargeTurretBase;
                }
                catch { }


                try
                {
                    List<IMyTerminalBlock> TEMP = new List<IMyTerminalBlock>();
                    GridTerminalSystem.GetBlocksOfType<IMyRadioAntenna>(TEMP, b => b.CubeGrid == Me.CubeGrid && b.CustomData.Contains("RFC_ANT"));
                    if (TEMP.Count > 0)
                    {
                        RADIO = TEMP[0] as IMyRadioAntenna;
                        RADIO.Enabled = true;

                    }
                }
                catch { }


                try
                {
                    List<IMyTerminalBlock> TEMP_GYRO = new List<IMyTerminalBlock>();
                    GridTerminalSystem.GetBlocksOfType<IMyGyro>(TEMP_GYRO, b => b.CubeGrid == Me.CubeGrid && b.CustomName == "RFC_GYRO");
                    GYRO = TEMP_GYRO[0] as IMyGyro;
                }
                catch { }


                try
                {
                    GridTerminalSystem.GetBlocksOfType<IMyShipController>(CONTROLLERS, b => b.CubeGrid == Me.CubeGrid);
                }
                catch { }


                if (SHIP_CLASS == "FMISSILE" || SHIP_CLASS == "FGRAV")
                {
                    try
                    {
                        HW_TIMER = GridTerminalSystem.GetBlockWithName("HW_TIMER") as IMyTimerBlock;
                    }
                    catch { }
                }



                Random rnd = new Random();
                MEINFO.ID = SHIP_CLASS.Substring(0, 2) + Me.CubeGrid.EntityId.ToString().Substring(0, 4);
                MEINFO.ISDOCKED = MEINFO.ID;
                try
                { COORD_ID = int.Parse(Me.CustomData.Split('#')[0]); }
                catch
                { COORD_ID = 0; }


                if (RADIO != null)
                    RADIO.CustomName = MEINFO.ID;

                RUN_ASSIGNMENT = rnd.Next(3, 7);
                RETURN_CONNECTOR_DIRECTION();
                GridTerminalSystem.GetBlocksOfType<IMyUserControllableGun>(DIRECTIONAL_FIRE, (block => block.GetType().Name == "MySmallMissileLauncher" || block.GetType().Name == "MySmallGatlingGun" || block.GetType().Name == "MySmallMissileLauncherReload"));


                COLLECT_AND_FIRE(new Vector3D(), 0, 0, RC.GetPosition());
                for (int j = 0; j < CAF2_THRUST.Count; j++)
                { CAF2_THRUST[j].SetValue<float>("Override", 0.0f); CAF2_THRUST[j].ApplyAction("OnOff_On"); }
                INITIAL_HEALTH = (int)((Me.CubeGrid.Max - Me.CubeGrid.Min)).Length();


                MEINFO.COMLOC = "GOTO^" + Vector3D.Round(Me.GetPosition(), 2);
                if (CONNECTOR.Status == MyShipConnectorStatus.Connected && MEINFO.ID.Substring(0, 1) == "I")
                { MEINFO.ISDOCKED = "DK" + CONNECTOR.OtherConnector.EntityId.ToString().Substring(0, 4); MEINFO.COMLOC = "DOCK^" + "DK" + CONNECTOR.OtherConnector.EntityId.ToString().Substring(0, 4); }

            }

            #endregion

            #region RFC Primary Logic st1 #RFC#
            
            void DRONE_PRI_LOGIC_ST1()
            {







                GYRO.SetValue<float>("Roll", 0);
                GYRO.SetValue<float>("Yaw", 0);
                GYRO.SetValue<float>("Pitch", 0);
                GYRO.SetValue("Override", false);
                RC.SetAutoPilotEnabled(false);


                for (int j = 0; j < CAF2_THRUST.Count; j++)
                { CAF2_THRUST[j].SetValue<float>("Override", 0.0f); CAF2_THRUST[j].ApplyAction("OnOff_On"); }



                MEINFO.LOC = Vector3D.Round(Me.GetPosition(), 2);
                MEINFO.VEL = Vector3D.Round(RC.GetShipVelocities().LinearVelocity + (RC.WorldMatrix.Forward * 2));
                MEINFO.HEALTH = 0.9;
                MEINFO.LAST_PING = DateTime.Now;
                if (RADIO != null)
                {
                    if (MEINFO.EXT_INF == "1")
                    { RADIO.CustomName = MEINFO.ID; }
                    else
                    { RADIO.CustomName = ""; }
                }



                if (MEINFO.COMLOC.Contains("DOCK")) { CONNECTOR.Connect(); CONNECTOR.ApplyAction("OnOff_On"); }
                if (MEINFO.COMLOC.Contains("UNDOCK")) { CONNECTOR.Disconnect(); CONNECTOR.ApplyAction("OnOff_Off"); }
                if (CONNECTOR.Status == MyShipConnectorStatus.Connected && MEINFO.COMLOC.Contains("DOCK"))
                {
                    for (int j = 0; j < CAF2_THRUST.Count; j++)
                    { CAF2_THRUST[j].SetValue<float>("Override", 0.0f); CAF2_THRUST[j].ApplyAction("OnOff_Off"); }
                    GYRO.SetValue("Override", false);
                    return;
                }



                COORDINATES = new List<Vector3D>();
                if (!string.IsNullOrEmpty(MEINFO.GLOC))
                {
                    for (int i = 0; i < MEINFO.GLOC.Split('^').Length; i++)
                    {
                        Vector3D TEMP_COORD;
                        if (Vector3D.TryParse(MEINFO.GLOC.Split('^')[i], out TEMP_COORD))
                        {
                            COORDINATES.Add(TEMP_COORD);
                        }
                    }
                }

                // Ensure we have at least one coordinate (current position as fallback)
                if (COORDINATES.Count == 0)
                {
                    COORDINATES.Add(RC.GetPosition());
                    Echo("Warning: No valid coordinates found, using current position");
                }



                RC.ApplyAction("AutoPilot_Off");
                RC.DampenersOverride = true;



                SCAN_MANAGER(out ENEMY_DETECTED);



                for (int i = 0; i < DIRECTIONAL_FIRE.Count; i++)
                { DIRECTIONAL_FIRE[i].ApplyAction("Shoot_Off"); }

            }
            #endregion

            #region RFC Primary Logic st2 #RFC#
            
            void DRONE_PRI_LOGIC_ST2()
            {






                if (MEINFO.COMLOC.Contains("UNDOCK") && COORDINATES.Count > 1)
                { DOCK_ITER("UDOCK"); return; }



                if (MEINFO.COMLOC.Contains("DOCK") && COORDINATES.Count > 1)
                { DOCK_ITER("DOCK"); return; }



                if (MEINFO.COMLOC.Contains("ATTACK") || ENEMY_DETECTED && ((COORDINATES[0] - SCAN_LOCKED_SHIP.POSITION).Length() < 1600))
                {


                    Echo("System Engaging");
                    switch (SHIP_CLASS)
                    {
                        case "INTERCEPTOR":

                            Attack_Location_Interceptor(); return;
                        case "IBOMBER":

                            Attack_Location_Interceptor(); return;
                        case "FRIGATE":

                            Attack_Location_Frigate(); return;
                        case "CRUISER":

                            Attack_Location_Cruiser(); return;
                        case "FMISSILE":

                            Attack_Location_Heavy(); return;
                        case "FGRAV":

                            Attack_Location_Heavy(); return;
                    }
                }



                if (MEINFO.COMLOC.Contains("GOTO"))
                {
                    var distanceToTarget = (COORDINATES[0] - RC.GetPosition()).Length();
                    var currentSpeed = RC.GetShipSpeed();

                    Echo($"Going To Target - Distance: {distanceToTarget:F1}m, Speed: {currentSpeed:F1}m/s");
                    RC.SetValue<Single>("SpeedLimit", ((float)MAX_SPEED + 20));

                    // Parse orientation data from central command for roll normalization
                    Vector3D commandUp = Vector3D.Up; // Default to world up
                    if (!string.IsNullOrEmpty(MEINFO.EXT_INF) && MEINFO.EXT_INF.StartsWith("ORIENT|"))
                    {
                        try
                        {
                            var orientParts = MEINFO.EXT_INF.Split('|');
                            if (orientParts.Length >= 3)
                            {
                                Vector3D.TryParse(orientParts[2], out commandUp);
                                var currentUp = RC.WorldMatrix.Up;
                                var rollAlignment = Vector3D.Dot(currentUp, commandUp);
                                var rollError = Vector3D.Cross(currentUp, commandUp);
                                var rollMagnitude = rollError.Length();
                                if (rollAlignment > 0.996)
                                {
                                    Echo($"GOTO Roll ALIGNED - Alignment: {rollAlignment:F3} (within tolerance)");
                                }
                                else
                                {
                                    Echo($"GOTO Roll Control - Alignment: {rollAlignment:F3}, Error: {rollMagnitude:F2}");
                                }
                            }
                        }
                        catch { Echo("Error parsing orientation data for GOTO"); }
                    }

                    if (distanceToTarget < 100 && distanceToTarget > 5)
                    {
                        // Close range - use roll normalization to match command ship orientation
                        var gotoTarget = COORDINATES[0];
                        COLLECT_AND_FIRE(gotoTarget, 0, MAX_SPEED, RC.GetPosition());
                        GyroTurn4(gotoTarget, GYRO, 2, commandUp, "UP");
                    }
                    else if (distanceToTarget > 5)
                    {
                        // Long range - use same logic as FOLLOW command
                        if (!Me.CubeGrid.ToString().Contains("Large"))
                        {
                            GetFreeDestination(COORDINATES[0]);
                            DOGPILOT(GFD_FREE_DESTINATION, 0, false);
                        }
                        else
                        {
                            RC_MANAGER(COORDINATES[0]);
                        }
                    }
                    else
                    {
                        Echo("ARRIVED AT TARGET - Stopping");
                        // Stop all thrusters when arrived
                        for (int j = 0; j < CAF2_THRUST.Count; j++)
                        {
                            CAF2_THRUST[j].SetValue<float>("Override", 0.0f);
                        }
                        // Disable gyro override
                        GYRO.SetValue("Override", false);
                        // Enable dampeners to help stop
                        RC.DampenersOverride = true;
                    }
                    return;
                }



                if (MEINFO.COMLOC.Contains("FOLLOW"))
                {
                    var distanceToTarget = (COORDINATES[0] - RC.GetPosition()).Length();
                    Echo($"Following Target - Distance: {distanceToTarget:F1}m");
                    RC.SetValue<Single>("SpeedLimit", ((float)MAX_SPEED + 20));

                    // Parse orientation data from central command for roll normalization
                    Vector3D commandForward = Vector3D.Zero;
                    Vector3D commandUp = Vector3D.Up; // Default to world up

                    if (!string.IsNullOrEmpty(MEINFO.EXT_INF) && MEINFO.EXT_INF.StartsWith("ORIENT|"))
                    {
                        try
                        {
                            var orientParts = MEINFO.EXT_INF.Split('|');
                            if (orientParts.Length >= 3)
                            {
                                Vector3D.TryParse(orientParts[1], out commandForward);
                                Vector3D.TryParse(orientParts[2], out commandUp);
                                var currentUp = RC.WorldMatrix.Up;
                                var rollAlignment = Vector3D.Dot(currentUp, commandUp);
                                var rollError = Vector3D.Cross(currentUp, commandUp);
                                var rollMagnitude = rollError.Length();
                                if (rollAlignment > 0.996)
                                {
                                    Echo($"FOLLOW Roll ALIGNED - Alignment: {rollAlignment:F3} (within tolerance)");
                                }
                                else
                                {
                                    Echo($"FOLLOW Roll Control - Alignment: {rollAlignment:F3}, Error: {rollMagnitude:F2}");
                                }
                            }
                        }
                        catch { Echo("Error parsing orientation data for FOLLOW"); }
                    }

                    // Check if we're close enough to start matching full orientation
                    if (distanceToTarget <= 15)
                    {
                        Echo("Close to follow position - Matching full orientation");

                        // If we have valid orientation data, match it completely
                        if (commandForward != Vector3D.Zero && commandUp != Vector3D.Zero)
                        {
                            // Calculate target position slightly ahead in command ship's forward direction
                            var orientationTarget = RC.GetPosition() + commandForward * 100;
                            COLLECT_AND_FIRE(COORDINATES[0], MEINFO.TVEl.Length(), MAX_SPEED, RC.GetPosition());
                            GyroTurn4(orientationTarget, GYRO, 2, commandUp, "UP");
                        }
                        else
                        {
                            // Fallback to normal follow behavior if no orientation data
                            var followTarget = COORDINATES[0];
                            COLLECT_AND_FIRE(followTarget, MEINFO.TVEl.Length(), MAX_SPEED, RC.GetPosition());
                            GyroTurn4(followTarget, GYRO, 2, commandUp, "UP");
                        }
                    }
                    else if (distanceToTarget < 100 && distanceToTarget > 15)
                    {
                        // Medium range - approach with roll normalization to command ship up
                        var followTarget = COORDINATES[0];
                        COLLECT_AND_FIRE(followTarget, MEINFO.TVEl.Length(), MAX_SPEED, RC.GetPosition());
                        GyroTurn4(followTarget, GYRO, 2, commandUp, "UP");
                    }
                    else if (distanceToTarget > 100)
                    {
                        // Long range - use autopilot systems
                        if (!Me.CubeGrid.ToString().Contains("Large"))
                        { GetFreeDestination(COORDINATES[0]); DOGPILOT(GFD_FREE_DESTINATION, MEINFO.TVEl.Length(), false); }
                        else
                        {
                            RC_MANAGER(COORDINATES[0]);
                        }
                    }
                }

            }
            #endregion



            #region RC_MANAGER #RFC#
            
            void RC_MANAGER(Vector3D TARGETPOSITION)
            {


                RC.ClearWaypoints();
                RC.AddWaypoint(TARGETPOSITION, "1");
                RC.AddWaypoint(TARGETPOSITION, "cc1");
                RC.ApplyAction("AutoPilot_On");
                RC.ApplyAction("DockingMode_Off");
                RC.ApplyAction("Forward");
                RC.ApplyAction("CollisionAvoidance_On");
            }

            #endregion

            #region GetPredictedPosition2 #RFC#
            
            Vector3D GetPredictedTargetPosition2(IMyRemoteControl shooter, DEC_INFO target)
            {
                Vector3D predictedPosition = target.POSITION;
                Vector3D dirToTarget = Vector3D.Normalize(predictedPosition - shooter.GetPosition());


                float shotSpeed = PROJECTILE_VELOCITY;


                Vector3 targetVelocity = target.VELOCITY;
                targetVelocity -= shooter.GetShipVelocities().LinearVelocity;
                Vector3 targetVelOrth = Vector3.Dot(targetVelocity, dirToTarget) * dirToTarget;
                Vector3 targetVelTang = targetVelocity - targetVelOrth;
                Vector3 shotVelTang = targetVelTang;
                float shotVelSpeed = shotVelTang.Length();

                if (shotVelSpeed > shotSpeed)
                {

                    return Vector3.Normalize(target.VELOCITY) * shotSpeed;
                }
                else
                {

                    float shotSpeedOrth = (float)Math.Sqrt(shotSpeed * shotSpeed - shotVelSpeed * shotVelSpeed);
                    Vector3 shotVelOrth = dirToTarget * shotSpeedOrth;
                    float timeDiff = shotVelOrth.Length() - targetVelOrth.Length();
                    var timeToCollision = timeDiff != 0 ? ((shooter.GetPosition() - target.POSITION).Length()) / timeDiff : 0;
                    Vector3 shotVel = shotVelOrth + shotVelTang;
                    predictedPosition = timeToCollision > 0.01f ? shooter.GetPosition() + (Vector3D)shotVel * timeToCollision : predictedPosition;
                    return predictedPosition;
                }
            }

            #endregion

            #region DOCK_ITERATOR
            
            void DOCK_ITER(string DUD)
            {

                if (COORDINATES.Count < 3) { return; }


                int ITER_CURRENT = 0;
                int ITER_PREV = 0;
                int ITERER = 0;
                if (DUD == "DOCK")
                { ITER_CURRENT = 1; ITER_PREV = 0; ITERER = +1; }
                if (DUD == "UDOCK")
                { ITER_CURRENT = 0; ITER_PREV = 1; ITERER = -1; }


                Vector3D ROLL_ORIENTER = Vector3D.Normalize(COORDINATES[COORDINATES.Count - 1] - COORDINATES[COORDINATES.Count - 2]);


                if (COORD_ID == COORDINATES.Count - 1)
                {
                    Vector3D DOCKING_HEADING = Vector3D.Normalize(COORDINATES[COORDINATES.Count - 3] - COORDINATES[COORDINATES.Count - 2]) * 9000000;
                    GyroTurn4(DOCKING_HEADING, GYRO, 2, ROLL_ORIENTER, CONNECTOR_PLANE);
                    if ((RC.GetShipVelocities().AngularVelocity).Length() < SHIP_DOCK_ANGULAR_SPEED)
                    { VECTOR_THRUST_MANAGER(COORDINATES[COORD_ID - ITER_CURRENT], COORDINATES[COORD_ID - ITER_PREV], CONNECTOR.GetPosition(), 5, 0.7); }
                }
                else
                {
                    if (COORD_ID == 0)
                    { DOGPILOT(COORDINATES[0], 0, false); }
                    else
                    {
                        var HEADING = Vector3D.Normalize(COORDINATES[COORD_ID - ITER_PREV] - COORDINATES[COORD_ID - ITER_CURRENT]) * 9000000;
                        VECTOR_THRUST_MANAGER(COORDINATES[COORD_ID - ITER_CURRENT], COORDINATES[COORD_ID - ITER_PREV], CONNECTOR.GetPosition(), 5, 1);
                        GyroTurn4(HEADING, GYRO, 2, ROLL_ORIENTER, CONNECTOR_PLANE);
                    }
                }


                if (DUD == "UDOCK" && COORD_ID == 0) { return; };
                if ((CONNECTOR.GetPosition() - COORDINATES[COORD_ID - ITER_PREV]).Length() < 1 || ((RC.GetPosition() - COORDINATES[COORD_ID - ITER_PREV]).Length() < 10 && COORD_ID == 0))
                {
                    COORD_ID = COORD_ID + ITERER;
                    if (COORD_ID == COORDINATES.Count)
                    { COORD_ID = COORDINATES.Count - 1; }
                    if (COORD_ID < 0)
                    { COORD_ID = 0; }
                }


                MathHelper.Clamp(COORD_ID, 0, COORDINATES.Count - 1);

            }


            #endregion



            #region APCK Movement Adapter System #RFC#
            


            APCKFlightController apckController;
            bool APCK_INITIALIZED = false;

            void InitializeAPCK()
            {
                if (APCK_INITIALIZED) return;

                try
                {
                    if (RC == null || GYRO == null)
                    {
                        Echo("APCK Init Failed: Missing RC or GYRO");
                        Echo("Ensure RFC_RC and RFC_GYRO blocks are present and named correctly");
                        return;
                    }

                    // Ensure CAF2_THRUST is initialized for safety stops
                    if (CAF2_THRUST.Count == 0)
                    {
                        GridTerminalSystem.GetBlocksOfType<IMyThrust>(CAF2_THRUST, block => block.CubeGrid == Me.CubeGrid);
                        Echo($"Found {CAF2_THRUST.Count} thrusters for safety control");
                    }

                    apckController = new APCKFlightController(RC, GYRO, GridTerminalSystem, Me.CubeGrid);
                    APCK_INITIALIZED = true;
                    Echo("APCK Movement System Initialized Successfully");
                }
                catch (Exception ex)
                {
                    Echo($"APCK Init Error: {ex.Message}");
                    Echo("CRITICAL: Movement system failed to initialize");
                    apckController = null;
                    APCK_INITIALIZED = false;
                }
            }


public class APCKFlightController
{
    private IMyRemoteControl remoteControl;
    private IMyGyro gyro;
    private IMyGridTerminalSystem gridTerminalSystem;
    private IMyCubeGrid cubeGrid;
    private List<IMyThrust> thrusters;
    private Dictionary<Vector3D, List<IMyThrust>> thrusterGroups;
    private bool initialized = false;
    public string LastDebugMessage = "";


                private const double CC_GAIN = 100.0;
                private const double MAX_SP = 104.38;
                private const double SHIP_ANGULAR_ACCEL = 0.6;

    public APCKFlightController(IMyRemoteControl rc, IMyGyro gyro, IMyGridTerminalSystem gts, IMyCubeGrid grid)
    {
        this.remoteControl = rc;
        this.gyro = gyro;
        this.gridTerminalSystem = gts;
        this.cubeGrid = grid;
        Initialize();
    }

                private void Initialize()
                {
                    if (initialized) return;


                    thrusters = new List<IMyThrust>();
                    gridTerminalSystem.GetBlocksOfType<IMyThrust>(thrusters, block => block.CubeGrid == cubeGrid);


                    thrusterGroups = new Dictionary<Vector3D, List<IMyThrust>>();
                    var matrix = remoteControl.WorldMatrix;

                    thrusterGroups[matrix.Forward] = GetThrustersInDirection(matrix.Backward);
                    thrusterGroups[matrix.Backward] = GetThrustersInDirection(matrix.Forward);
                    thrusterGroups[matrix.Up] = GetThrustersInDirection(matrix.Down);
                    thrusterGroups[matrix.Down] = GetThrustersInDirection(matrix.Up);
                    thrusterGroups[matrix.Left] = GetThrustersInDirection(matrix.Right);
                    thrusterGroups[matrix.Right] = GetThrustersInDirection(matrix.Left);

                    initialized = true;
                }

                private List<IMyThrust> GetThrustersInDirection(Vector3D direction)
                {
                    var result = new List<IMyThrust>();
                    foreach (var thruster in thrusters)
                    {
                        if (Vector3D.Dot(-thruster.WorldMatrix.Forward, direction) > 0.7)
                        {
                            result.Add(thruster);
                        }
                    }
                    return result;
                }

                public void MoveTo(Vector3D targetPos, double targetVel, double maxVel, Vector3D refPos)
                {
                    if (!initialized)
                    {
                        LastDebugMessage = "APCK not initialized";
                        return;
                    }

                    var currentPos = remoteControl.GetPosition();
                    var currentVel = remoteControl.GetShipVelocities().LinearVelocity;
                    var direction = targetPos - currentPos;
                    var distance = direction.Length();

                    LastDebugMessage = $"APCK MoveTo: Dist={distance:F1}m Speed={currentVel.Length():F1}m/s";

                    if (distance < 2.0)
                    {
                        // Very close to target - apply minimal braking if moving fast
                        if (currentVel.Length() > 5.0)
                        {
                            var brakeDirection = -currentVel.Normalized();
                            var shipMass = remoteControl.CalculateShipMass().PhysicalMass;
                            var brakeThrust = GetMaxThrustInDirection(brakeDirection);
                            var brakeAccel = brakeThrust / shipMass;
                            var brakeVector = brakeDirection * brakeAccel * 0.5;
                            ApplyThrustVector(brakeVector, shipMass);
                            LastDebugMessage += " (Close - Final Braking)";
                        }
                        else
                        {
                            // Close and slow - turn off thrusters
                            foreach (var group in thrusterGroups.Values)
                            {
                                foreach (var thruster in group)
                                {
                                    thruster.ThrustOverride = 0f;
                                }
                            }
                            LastDebugMessage += " (Arrived - Thrusters Off)";
                        }
                        return;
                    }

                    var normalizedDir = direction.Normalized();
                    var mass = remoteControl.CalculateShipMass().PhysicalMass;

                    var desiredAccel = CalculateAPCKAcceleration(direction, currentVel, targetVel, maxVel, mass);

                    // Determine what action we're taking
                    var velocityTowardTarget = Vector3D.Dot(currentVel, normalizedDir);
                    string action = "COAST";
                    if (desiredAccel.LengthSquared() > 0.1)
                    {
                        var accelTowardTarget = Vector3D.Dot(desiredAccel, normalizedDir);
                        action = accelTowardTarget > 0 ? "ACCEL" : "BRAKE";
                    }

                    LastDebugMessage += $" Accel={desiredAccel.Length():F1} Action={action} VelToTgt={velocityTowardTarget:F1}";

                    ApplyThrustVector(desiredAccel, mass);
                }

                private Vector3D CalculateAPCKAcceleration(Vector3D direction, Vector3D velocity, double targetVel, double maxVel, float mass)
                {
                    var distance = direction.Length();
                    var normalizedDir = direction.Normalized();
                    var speed = velocity.Length();

                    // If we're very close, don't apply thrust
                    if (distance < 1.0) return Vector3D.Zero;

                    var driftVector = speed > 1 ? velocity.Normalized() : Vector3D.Zero;
                    var reflectedDrift = Vector3D.Zero;

                    if (speed > 1)
                    {
                        reflectedDrift = -Vector3D.Normalize(Vector3D.Reflect(driftVector, normalizedDir));
                        if (Vector3D.Dot(reflectedDrift, normalizedDir) < -0.3 || speed < 10)
                            reflectedDrift = Vector3D.Zero;
                    }

                    var correctedDirection = Vector3D.Normalize((4 * reflectedDrift) + normalizedDir);

                    // Debug: Check if corrected direction is reasonable
                    var directionDot = Vector3D.Dot(correctedDirection, normalizedDir);
                    if (directionDot < 0.5) // If corrected direction is more than 60 degrees off, use original
                    {
                        correctedDirection = normalizedDir;
                    }

                    var maxThrust = GetMaxThrustInDirection(correctedDirection);
                    if (maxThrust <= 0)
                    {
                        // Fallback: try forward direction
                        maxThrust = GetMaxThrustInDirection(remoteControl.WorldMatrix.Forward);
                        correctedDirection = normalizedDir;
                    }

                    var maxAccel = maxThrust / mass;
                    if (maxAccel <= 0) return Vector3D.Zero;

                    var stoppingDistance = Math.Max(0, (speed * speed - targetVel * targetVel) / (2 * maxAccel * 0.75));

                    Vector3D accel = Vector3D.Zero;

                    // Check if we're moving toward or away from target
                    var velocityTowardTarget = Vector3D.Dot(velocity, normalizedDir);

                    if (distance > Math.Max(stoppingDistance + 5.0, 10.0))
                    {
                        // Far from target - accelerate toward it
                        accel = correctedDirection * Math.Min(maxAccel, maxAccel * 0.8);
                    }
                    else if (distance > 2.0)
                    {
                        // Close to target - check if we need to brake
                        if (velocityTowardTarget > targetVel + 5.0)
                        {
                            // Moving too fast toward target - apply braking
                            var brakeDirection = -velocity.Normalized();
                            var brakeThrust = GetMaxThrustInDirection(brakeDirection);
                            var brakeAccel = brakeThrust / mass;
                            accel = brakeDirection * Math.Min(brakeAccel * 0.8, maxAccel);
                        }
                        else if (velocityTowardTarget < targetVel - 2.0)
                        {
                            // Moving too slow - gentle acceleration
                            accel = correctedDirection * maxAccel * 0.3;
                        }
                        // If velocity is about right, apply minimal thrust
                        else
                        {
                            accel = correctedDirection * maxAccel * 0.1;
                        }
                    }
                    else if (speed > 5.0)
                    {
                        // Very close to target but still moving fast - emergency braking
                        var brakeDirection = -velocity.Normalized();
                        var brakeThrust = GetMaxThrustInDirection(brakeDirection);
                        var brakeAccel = brakeThrust / mass;
                        accel = brakeDirection * brakeAccel;
                    }

                    var gravity = remoteControl.GetNaturalGravity();
                    if (gravity != Vector3D.Zero)
                        accel -= gravity;

                    return accel;
                }

                private double GetMaxThrustInDirection(Vector3D direction)
                {
                    double maxThrust = 0;
                    var matrix = remoteControl.WorldMatrix;


                    foreach (var group in thrusterGroups)
                    {
                        var dot = Vector3D.Dot(group.Key, direction);
                        if (dot > 0.5)
                        {
                            double groupThrust = 0;
                            foreach (var thruster in group.Value)
                            {
                                if (thruster.IsFunctional)
                                    groupThrust += thruster.MaxEffectiveThrust;
                            }
                            maxThrust = Math.Max(maxThrust, groupThrust * dot);
                        }
                    }

                    return maxThrust;
                }

                private void ApplyThrustVector(Vector3D acceleration, float mass)
                {
                    var matrix = remoteControl.WorldMatrix;
                    var localAccel = Vector3D.TransformNormal(acceleration, MatrixD.Transpose(matrix));

                    // Debug output - this won't work directly, but we can check if acceleration is zero
                    if (acceleration.LengthSquared() < 0.01)
                    {
                        // No acceleration needed, turn off all thrusters
                        foreach (var group in thrusterGroups.Values)
                        {
                            foreach (var thruster in group)
                            {
                                thruster.ThrustOverride = 0.00000001f;
                            }
                        }
                        return;
                    }

                    ApplyAxisThrust(matrix.Forward, localAccel.Z, mass);
                    ApplyAxisThrust(matrix.Backward, -localAccel.Z, mass);
                    ApplyAxisThrust(matrix.Up, localAccel.Y, mass);
                    ApplyAxisThrust(matrix.Down, -localAccel.Y, mass);
                    ApplyAxisThrust(matrix.Right, localAccel.X, mass);
                    ApplyAxisThrust(matrix.Left, -localAccel.X, mass);
                }

                private void ApplyAxisThrust(Vector3D direction, double acceleration, float mass)
                {
                    if (!thrusterGroups.ContainsKey(direction)) return;

                    var thrusters = thrusterGroups[direction];
                    var force = Math.Abs(acceleration * mass);

                    if (acceleration <= 0 || force < 0.1)
                    {
                        foreach (var thruster in thrusters)
                        {
                            if (thruster.IsFunctional)
                                thruster.ThrustOverride = 0f;
                        }
                        return;
                    }

                    double totalMaxThrust = 0;
                    foreach (var thruster in thrusters)
                    {
                        if (thruster.IsFunctional)
                            totalMaxThrust += thruster.MaxThrust;
                    }

                    if (totalMaxThrust > 0)
                    {
                        var thrustRatio = Math.Min(1.0, force / totalMaxThrust);
                        foreach (var thruster in thrusters)
                        {
                            if (thruster.IsFunctional)
                            {
                                var thrustValue = (float)(thrustRatio * thruster.MaxThrust);
                                thruster.ThrustOverride = Math.Max(thrustValue, 0f);
                            }
                        }
                    }
                }

                public void PointTowards(Vector3D targetPos, Vector3D rollVector, string rollOrient, double multiplier)
                {
                    if (!initialized) return;

                    var currentPos = remoteControl.GetPosition();
                    var direction = (targetPos - currentPos).Normalized();


                    var rotationVector = CalculateAPCKRotation(direction, rollVector, rollOrient);


                    ApplyAPCKGyroControl(rotationVector, multiplier);
                }

                public void AgileMoveTo(Vector3D targetPos, double targetVel, bool overrideDrift)
                {
                    if (!initialized) return;

                    var currentPos = remoteControl.GetPosition();
                    var currentVel = remoteControl.GetShipVelocities().LinearVelocity;
                    var velocity = currentVel.Length();
                    var mass = remoteControl.CalculateShipMass().PhysicalMass;
                    var distance = (currentPos - targetPos).Length();


                    var directionToTarget = Vector3D.Normalize(targetPos - currentPos);
                    var driftVector = Vector3D.Normalize(currentVel);
                    var reflectedDrift = Vector3D.Negate(Vector3D.Normalize(Vector3D.Reflect(driftVector, directionToTarget)));


                    if (Vector3D.Dot(reflectedDrift, directionToTarget) < -0.2 || velocity < 10 || overrideDrift)
                        reflectedDrift = Vector3D.Zero;

                    var correctedDirection = Vector3D.Normalize((4 * reflectedDrift) + directionToTarget);
                    var aimingPosition = correctedDirection * 300 + currentPos;


                    PointTowards(aimingPosition, Vector3D.Zero, "FORWARD", 7);


                    var maxThrust = GetMaxThrustInDirection(remoteControl.WorldMatrix.Forward);
                    var maxAccel = maxThrust / mass;
                    var stoppingDistance = ((velocity * velocity) - (targetVel * targetVel)) / (2 * maxAccel);


                    if (distance > stoppingDistance)
                    {
                        ApplyMaxThrustInDirection(remoteControl.WorldMatrix.Forward);
                    }
                }

                private void ApplyMaxThrustInDirection(Vector3D direction)
                {
                    if (!thrusterGroups.ContainsKey(direction)) return;

                    var thrusters = thrusterGroups[direction];
                    foreach (var thruster in thrusters)
                    {
                        if (thruster.IsFunctional)
                            thruster.ThrustOverride = thruster.MaxThrust;
                    }
                }

                public void PrecisionMoveTo(Vector3D startPos, Vector3D targetPos, Vector3D refPos, double maxVel, double precision)
                {
                    if (!initialized) return;


                    var vector = Vector3D.Normalize(startPos - targetPos);
                    var distanceToTarget = (refPos - targetPos).Length();
                    var pathLength = (startPos - targetPos).Length();


                    var clampedDistance = MathHelper.Clamp(distanceToTarget - 0.2, 0, pathLength);
                    var gotoPoint = targetPos + vector * clampedDistance;
                    var distanceToPoint = MathHelper.Clamp((gotoPoint - refPos).Length(), 0, pathLength);


                    Vector3D finalTarget;
                    if (distanceToPoint > precision)
                        finalTarget = gotoPoint;
                    else
                        finalTarget = targetPos;


                    MoveTo(finalTarget, 0, 5, refPos);
                }

                private Vector3D CalculateAPCKRotation(Vector3D targetDirection, Vector3D rollVector, string rollOrient)
                {
                    var matrix = remoteControl.WorldMatrix;
                    var forward = matrix.Forward;
                    var up = matrix.Up;
                    var right = matrix.Right;

                    // Check forward alignment for pitch/yaw tolerance
                    var forwardAlignment = Vector3D.Dot(forward, targetDirection);
                    var pitchAngle = 0.0;
                    var yawAngle = 0.0;

                    if (forwardAlignment < 0.996) // Only calculate pitch/yaw if not aligned within 5 degrees
                    {
                        pitchAngle = -Math.Atan2(Vector3D.Dot(Vector3D.Cross(forward, targetDirection), right), Vector3D.Dot(forward, targetDirection));
                        yawAngle = -Math.Atan2(Vector3D.Dot(Vector3D.Cross(forward, targetDirection), up), Vector3D.Dot(forward, targetDirection));
                    }

                    var rollAngle = 0.0;
                    if (rollVector != Vector3D.Zero)
                    {
                        // Improved roll calculation using cross product for proper error calculation
                        var currentUp = remoteControl.WorldMatrix.Up;
                        var desiredUp = rollVector.Normalized();

                        // Check if we're already aligned within tolerance (about 5 degrees)
                        var rollAlignment = Vector3D.Dot(currentUp, desiredUp);
                        if (rollAlignment > 0.996) // cos(5°) ≈ 0.996
                        {
                            // Already aligned, set roll to 0 to prevent oscillation
                            rollAngle = 0.0;
                        }
                        else
                        {
                            // Calculate roll error using cross product
                            var rollError = Vector3D.Cross(currentUp, desiredUp);

                            // Project the error onto the forward axis to get roll component
                            // Invert the roll direction to fix inverted up direction
                            rollAngle = -Vector3D.Dot(rollError, remoteControl.WorldMatrix.Forward);

                            // Apply stronger multiplier for better responsiveness
                            rollAngle *= 2.0;
                        }
                    }

                    return new Vector3D(pitchAngle, yawAngle, rollAngle);
                }

                private void ApplyAPCKGyroControl(Vector3D rotationVector, double multiplier)
                {
                    var angularVel = remoteControl.GetShipVelocities().AngularVelocity;
                    var rotVel = angularVel.Length();


                    var stoppingDistance = (rotVel * rotVel) / (2 * SHIP_ANGULAR_ACCEL);

                    var pitch = Math.Abs(rotationVector.X) > stoppingDistance ?
                        (float)(rotationVector.X * multiplier) : 0f;
                    var yaw = Math.Abs(rotationVector.Y) > stoppingDistance ?
                        (float)(rotationVector.Y * multiplier * -1) : 0f;
                    // Apply same stopping distance logic to roll as pitch and yaw
                    var roll = Math.Abs(rotationVector.Z) > stoppingDistance ?
                        (float)(rotationVector.Z * multiplier) : 0f;

                    gyro.SetValue("Pitch", pitch);
                    gyro.SetValue("Yaw", yaw);
                    gyro.SetValue("Roll", roll);
                    gyro.SetValue("Override", true);
                }

                private Vector3D ExtractDirection(string direction, IMyTerminalBlock block)
                {
                    Vector3D outputDirection = new Vector3D();

                    if (direction == "UP")
                        outputDirection = block.WorldMatrix.Up;
                    else if (direction == "LEFT")
                        outputDirection = block.WorldMatrix.Left;
                    else if (direction == "FORWARD")
                        outputDirection = block.WorldMatrix.Forward;
                    else if (direction == "DOWN")
                        outputDirection = block.WorldMatrix.Down;
                    else if (direction == "RIGHT")
                        outputDirection = block.WorldMatrix.Right;
                    else if (direction == "BACKWARD")
                        outputDirection = block.WorldMatrix.Backward;

                    return outputDirection;
                }
            }

            #endregion

            #region GyroTurn4 #RFC# - APCK Enhanced
            
            void GyroTurn4(Vector3D TARGETPOSITION, IMyGyro GYRO, double MULTIPLIER, Vector3D ROLLVECTOR, string ROLLORIENT)
            {
                InitializeAPCK();

                if (apckController != null)
                {
                    apckController.PointTowards(TARGETPOSITION, ROLLVECTOR, ROLLORIENT, MULTIPLIER);
                }
                else
                {
                    Echo("ERROR: APCK System Failed - Gyro Control Disabled");
                    // Disable gyro override as safety measure
                    GYRO.SetValue("Override", false);
                }
            }



            #endregion

            #region COLLECT_AND_FIRE #RFC# - APCK Enhanced
            
            class Thrust_info
            {
                public double PositiveMaxForce;
                public double NegativeMaxForce;
                public List<IMyThrust> PositiveThrusters;
                public List<IMyThrust> NegativeThrusters;
                public double VCF;
                public Thrust_info(Vector3D DIRECT, IMyGridTerminalSystem GTS, IMyCubeGrid MEGRID)
                {
                    PositiveThrusters = new List<IMyThrust>(); NegativeThrusters = new List<IMyThrust>();
                    List<IMyTerminalBlock> TEMP_RC = new List<IMyTerminalBlock>();
                    GTS.GetBlocksOfType<IMyThrust>(PositiveThrusters, block => Vector3D.Dot(-1 * block.WorldMatrix.Forward, DIRECT) > 0.7 && block.CubeGrid == MEGRID);
                    GTS.GetBlocksOfType<IMyThrust>(NegativeThrusters, block => Vector3D.Dot(block.WorldMatrix.Forward, DIRECT) > 0.7 && block.CubeGrid == MEGRID);
                    double POWER_COUNT = 0;
                    foreach (var item in PositiveThrusters)
                    { POWER_COUNT = POWER_COUNT + item.MaxEffectiveThrust; }
                    PositiveMaxForce = POWER_COUNT;
                    POWER_COUNT = 0;
                    foreach (var item in NegativeThrusters)
                    { POWER_COUNT = POWER_COUNT + item.MaxEffectiveThrust; }
                    NegativeMaxForce = POWER_COUNT;
                }
            }
            Thrust_info CAF2_FORWARD;
            Thrust_info CAF2_UP;
            Thrust_info CAF2_RIGHT;
            List<Thrust_info> CAFTHI = new List<Thrust_info>();

            List<IMyTerminalBlock> CAF2_THRUST = new List<IMyTerminalBlock>();
            bool C_A_F_HASRUN = false;
            double CAF2_BRAKING_COUNT = 99999999;

            double CAF_SHIP_DECELLERATION;
            double CAF_STOPPING_DIST;
            double CAF_DIST_TO_TARGET;

            void COLLECT_AND_FIRE(Vector3D INPUT_POINT, double INPUT_VELOCITY, double INPUT_MAX_VELOCITY, Vector3D REFPOS)
            {
                InitializeAPCK();

                // Debug target information
                var currentPos = RC.GetPosition();
                var distance = (INPUT_POINT - currentPos).Length();
                Echo($"Target: {Vector3D.Round(INPUT_POINT, 1)}");
                Echo($"Current: {Vector3D.Round(currentPos, 1)}");
                Echo($"Distance: {distance:F1}m");

                if (apckController != null)
                {
                    Echo("Using APCK Movement System");
                    apckController.MoveTo(INPUT_POINT, INPUT_VELOCITY, INPUT_MAX_VELOCITY, REFPOS);
                    Echo(apckController.LastDebugMessage);
                }
                else
                {
                    Echo("ERROR: APCK Movement System Failed to Initialize - Ship Disabled");
                    // Stop all thrusters as safety measure
                    for (int j = 0; j < CAF2_THRUST.Count; j++)
                    {
                        CAF2_THRUST[j].SetValue<float>("Override", 0.0f);
                    }
                }
            }



            #endregion

            #region Vector Projection #RFC#
            
            double Vector_Projection(Vector3D IN, Vector3D Axis)
            {
                double OUT = 0;
                OUT = Vector3D.Dot(IN, Axis) / IN.Length();
                if (OUT + "" == "NaN")
                { OUT = 0; }
                return OUT;
            }
            #endregion

            #region VECTOR_THRUST_MANAGER #RFC# - APCK Enhanced
            
            void VECTOR_THRUST_MANAGER(Vector3D PM_START, Vector3D PM_TARGET, Vector3D PM_REF, double PR_MAX_VELOCITY, double PREC)
            {
                InitializeAPCK();

                if (apckController != null)
                {
                    apckController.PrecisionMoveTo(PM_START, PM_TARGET, PM_REF, PR_MAX_VELOCITY, PREC);
                }
                else
                {
                    Echo("ERROR: APCK System Failed - Precision Movement Disabled");
                    // Stop all thrusters as safety measure
                    for (int j = 0; j < CAF2_THRUST.Count; j++)
                    {
                        CAF2_THRUST[j].SetValue<float>("Override", 0.0f);
                    }
                }
            }



            #endregion



            #region DOCK_SYSTEM
            
            string DOCK_SYST()
            {

                if (DOCKING_INIT == false)
                { DOCK_INIT(); DOCKING_INIT = true; }


                StringBuilder DOCKOUTPUT = new StringBuilder();
                List<string> KEYS = new List<string>(DOCKPOINTS.Keys);
                for (int i = 0; i < DOCKPOINTS.Count; i++)
                {

                    DOCKPOINTS[DOCKPOINTS[KEYS[i]].ID].LAST_PING = DateTime.Now;
                    DOCKPOINTS[DOCKPOINTS[KEYS[i]].ID] = DOCKPOINT_INFO.SAVE_ROUTE_TO_STRING(DOCKPOINTS[KEYS[i]]);


                    DOCKOUTPUT.Append(DOCKPOINTS[KEYS[i]].OUTPUT);
                }
                return DOCKOUTPUT + "";
            }
            #endregion

            #region Connector Trail Setup
            
            List<IMyTerminalBlock> TRAIL_SETUP(IMyShipConnector CONN)
            {


                List<IMyTerminalBlock> TEMP_D = new List<IMyTerminalBlock>();
                List<IMyTerminalBlock> ROUTE_BLOCKS = new List<IMyTerminalBlock>();


                GridTerminalSystem.GetBlocksOfType<IMyTextPanel>(TEMP_D, block => (block.CubeGrid == Me.CubeGrid && block != CONNECTOR));
                if (TEMP_D.Count == 0) { return ROUTE_BLOCKS; }
                var INITIAL_LCD = default(IMyTextPanel);
                double DIST = 5;
                foreach (var item in TEMP_D)
                {
                    var ME_DIST = (item.GetPosition() - CONN.GetPosition()).Length();
                    if (ME_DIST < DIST)
                    { INITIAL_LCD = item as IMyTextPanel; DIST = ME_DIST; }
                }


                if (INITIAL_LCD == null) { Echo("Non Route Detected"); return new List<IMyTerminalBlock>(); }
                ROUTE_BLOCKS.Add(CONN);
                ROUTE_BLOCKS.Add(INITIAL_LCD);


                for (int i = 1; i < 7; i++)
                {
                    List<IMyTerminalBlock> TEMP_E = new List<IMyTerminalBlock>();
                    GridTerminalSystem.GetBlocksOfType<IMyTextPanel>(TEMP_E, block => Vector3D.Dot(ROUTE_BLOCKS[i].WorldMatrix.Up, Vector3D.Normalize(block.GetPosition() - ROUTE_BLOCKS[i].GetPosition())) > 0.999);
                    if (TEMP_E.Count == 0) { break; }
                    IMyTextPanel TEMP_PANEL = default(IMyTextPanel);
                    DIST = 300;
                    foreach (var item in TEMP_E)
                    {
                        var ME_DIST = (item.GetPosition() - CONN.GetPosition()).Length();
                        if (ME_DIST < DIST)
                        { TEMP_PANEL = item as IMyTextPanel; DIST = ME_DIST; }
                    }
                    ROUTE_BLOCKS.Add(TEMP_PANEL);
                }


                Echo("Iteration Through Panels Complete");
                return ROUTE_BLOCKS;
            }
            #endregion

            #region Dock Initialisation                     

            void DOCK_INIT()
            {

                List<IMyTerminalBlock> TEMP = new List<IMyTerminalBlock>();
                GridTerminalSystem.GetBlocksOfType<IMyShipConnector>(TEMP, b => b.CubeGrid == Me.CubeGrid);
                foreach (IMyTerminalBlock TERMINAL_CONNECTOR in TEMP)
                {
                    Echo("started loop");
                    List<IMyTerminalBlock> TRAIL_LIST = TRAIL_SETUP(TERMINAL_CONNECTOR as IMyShipConnector);
                    Echo("ran coord init");
                    if (TRAIL_LIST.Count > 0)
                    {
                        DOCKPOINT_INFO NEW_DOCKPT = new DOCKPOINT_INFO();
                        NEW_DOCKPT.ID = "DK" + TERMINAL_CONNECTOR.EntityId.ToString().Substring(0, 4);
                        NEW_DOCKPT.BASE_TAG = MEINFO.ID;
                        NEW_DOCKPT.LAST_PING = DateTime.Now;
                        NEW_DOCKPT.ROUTE = TRAIL_LIST;
                        List<string> ROUTE_POSITIONS = new List<string>();
                        foreach (var item in NEW_DOCKPT.ROUTE)
                        { ROUTE_POSITIONS.Add(item.GetPosition() + ""); }
                        NEW_DOCKPT.OUTPUTROLL = string.Join("^", ROUTE_POSITIONS);

                        DOCKPOINTS.Add(NEW_DOCKPT.ID, NEW_DOCKPT);
                    }
                }
            }

            #endregion



            #region I_Attack_Location
            
            Vector3D ATTACK_RUN_POS;
            void Attack_Location_Interceptor()
            {
                Echo("In Attack Situation\n");
                Echo(ENEMY_DETECTED + "");



                var aimpos = COORDINATES[0];
                Echo("Using Default Tracking");
                if (ENEMY_DETECTED)
                {
                    aimpos = SCAN_LOCKED_SHIP.POSITION;
                    Echo("Using Turret Tracking");
                    if ((aimpos - COORDINATES[0]).Length() > 1600)
                    { aimpos = COORDINATES[0]; Echo("REASSIGNING COORDS"); }
                }
                if (SCAN_HARDLOCK == true)
                {
                    Echo("Overridden, using Raycast Tracking");
                    aimpos = GetPredictedTargetPosition2(RC, SCAN_LOCKED_SHIP);
                    if ((aimpos - COORDINATES[0]).Length() > 1600)
                    { aimpos = COORDINATES[0]; Echo("REASSIGNING COORDS"); }
                }



                if (SCAN_LOCKED_SHIP.VELOCITY.Length() > 20 && SCAN_HARDLOCK)
                {
                    Echo("target fast, engaging");
                    if ((RC.GetPosition() - aimpos).Length() < SCAN_LOCKED_SHIP.SIZE / 2)
                    { GetFreeDestination(aimpos); DOGPILOT(GFD_FREE_DESTINATION, SCAN_LOCKED_SHIP.VELOCITY.Length(), true); }
                    if ((RC.GetPosition() - aimpos).Length() < SCAN_LOCKED_SHIP.SIZE)
                    { GyroTurn4(aimpos, GYRO, 7, new Vector3D(), "FORWARD"); }
                    else if ((RC.GetPosition() - aimpos).Length() < 600 || SCAN_HARDLOCK == false)
                    { DOGPILOT(aimpos, SCAN_LOCKED_SHIP.VELOCITY.Length(), true); }
                    else
                    { DOGPILOT(aimpos, SCAN_LOCKED_SHIP.VELOCITY.Length(), false); }
                }
                else if (SCAN_LOCKED_SHIP.VELOCITY.Length() < 20 && SCAN_HARDLOCK)
                {
                    Echo("target slow, engaging ");

                    Vector3D TARG_VECT = Vector3D.Normalize(aimpos - RC.GetPosition()) * 2000 + RC.GetPosition();
                    GetFreeDestination(TARG_VECT);


                    if ((RC.GetPosition() - aimpos).Length() < 800 && ATTACK_RUN_POS == new Vector3D())
                    { ATTACK_RUN_POS = Vector3D.Normalize(GFD_FREE_DESTINATION - RC.GetPosition()) * 2000 + RC.GetPosition(); }
                    if ((RC.GetPosition() - aimpos).Length() > 800)
                    { ATTACK_RUN_POS = Vector3D.Normalize(GFD_FREE_DESTINATION - RC.GetPosition()) * 2000 + RC.GetPosition(); Echo("setting pos"); }


                    double VEL_COMP = MathHelper.Clamp(Vector3D.Dot(Vector3D.Normalize(RC.GetShipVelocities().LinearVelocity), Vector3D.Normalize(ATTACK_RUN_POS - RC.GetPosition())), -1, 1);
                    Echo(VEL_COMP + " vel component");
                    if (VEL_COMP > 0.975 && (RC.GetPosition() - aimpos).Length() < 800 && RC.GetShipSpeed() > 60)
                    {
                        Echo("attacking pos");
                        RC.DampenersOverride = false;
                        GyroTurn4(aimpos, GYRO, 7, new Vector3D(), "FORWARD");
                    }
                    else
                    {
                        Echo("going to far away pos");
                        DOGPILOT(ATTACK_RUN_POS, SCAN_LOCKED_SHIP.VELOCITY.Length(), true);
                        return;
                    }

                }
                else if ((RC.GetPosition() - aimpos).Length() < 800)
                {
                    DOGPILOT(aimpos, SCAN_LOCKED_SHIP.VELOCITY.Length(), true);
                }
                else
                {
                    DOGPILOT(aimpos, SCAN_LOCKED_SHIP.VELOCITY.Length(), false);
                }


                if (Vector3D.Dot(RC.WorldMatrix.Forward, Vector3D.Normalize(aimpos - RC.GetPosition())) > 0.975 && (RC.GetPosition() - aimpos).Length() < 800)
                {

                    for (int i = 0; i < DIRECTIONAL_FIRE.Count; i++)
                    { DIRECTIONAL_FIRE[i].ApplyAction("ShootOnce"); DIRECTIONAL_FIRE[i].ApplyAction("Shoot_On"); }
                }
            }
            #endregion

            #region FR_Attack_Location
            
            int FIGHT_TIMER = 0;
            int SEQUUENCE_TIMER = 0;
            void Attack_Location_Frigate()
            {
                Echo("In Attack Situation\n");
                PROJECTILE_VELOCITY = 200;



                var aimpos = COORDINATES[0];
                if (ENEMY_DETECTED)
                {
                    aimpos = SCAN_LOCKED_SHIP.POSITION;
                    if ((aimpos - COORDINATES[0]).Length() > 1400)
                    { aimpos = COORDINATES[0]; }
                }
                if (SCAN_HARDLOCK == true)
                {
                    aimpos = GetPredictedTargetPosition2(RC, SCAN_LOCKED_SHIP);
                    if ((aimpos - COORDINATES[0]).Length() > 1400)
                    { aimpos = COORDINATES[0]; }
                }




                if ((RC.GetPosition() - aimpos).Length() > 800)
                {
                    if (SCAN_HARDLOCK)
                    {
                        aimpos = aimpos - Vector3D.Normalize(aimpos - RC.GetPosition()) * 500;
                        COLLECT_AND_FIRE(aimpos, 0, MAX_SPEED, RC.GetPosition());
                        GyroTurn4(aimpos, GYRO, 5, new Vector3D(), "FORWARD");
                    }
                    else
                    {
                        COLLECT_AND_FIRE(aimpos, 0, MAX_SPEED, RC.GetPosition());
                        GyroTurn4(aimpos, GYRO, 5, new Vector3D(), "FORWARD");
                    }
                }
                else if ((RC.GetPosition() - aimpos).Length() < 400)
                {
                    COLLECT_AND_FIRE(RC.GetPosition() + 200 * RC.WorldMatrix.Backward, 0, 30, RC.GetPosition());
                    GyroTurn4(aimpos, GYRO, 5, new Vector3D(), "FORWARD");
                }
                else if (SCAN_HARDLOCK && SCAN_LOCKED_SHIP.VELOCITY.Length() > 5)
                {
                    COLLECT_AND_FIRE(aimpos, 0, MAX_SPEED, RC.GetPosition());

                    GyroTurn4(aimpos, GYRO, 5, new Vector3D(), "FORWARD");
                }
                else if (SCAN_HARDLOCK)
                {
                    GyroTurn4(aimpos, GYRO, 5, new Vector3D(), "FORWARD");

                    if (FIGHT_TIMER < 100)
                    { COLLECT_AND_FIRE(RC.GetPosition() + 100 * RC.WorldMatrix.Up, 0, 10, RC.GetPosition()); }
                    if (FIGHT_TIMER > 100)
                    { COLLECT_AND_FIRE(RC.GetPosition() + 100 * RC.WorldMatrix.Down, 0, 10, RC.GetPosition()); }
                    if (FIGHT_TIMER == 200)
                    { FIGHT_TIMER = 0; }
                    FIGHT_TIMER++;
                }
                else if (ENEMY_DETECTED && DIRECTOR.IsShooting)
                {
                    GyroTurn4(aimpos, GYRO, 5, new Vector3D(), "FORWARD");
                }


                if (Vector3D.Dot(RC.WorldMatrix.Forward, Vector3D.Normalize(aimpos - RC.GetPosition())) > 0.975 && (RC.GetPosition() - aimpos).Length() < 800 && DIRECTOR.HasTarget)
                {
                    DIRECTIONAL_FIRE[SEQUUENCE_TIMER].ApplyAction("ShootOnce"); DIRECTIONAL_FIRE[SEQUUENCE_TIMER].ApplyAction("Shoot_On");
                    SEQUUENCE_TIMER++;
                    if (SEQUUENCE_TIMER > DIRECTIONAL_FIRE.Count - 1)
                    { SEQUUENCE_TIMER = 0; }
                }
            }
            #endregion

            #region CR_Attack_Location
            
            void Attack_Location_Cruiser()
            {
                Echo("In Attack Situation\n");
                Echo(SCAN_LOCKED_SHIP.AIMPOS + "\n");



                var aimpos = COORDINATES[0];
                if (ENEMY_DETECTED)
                {
                    aimpos = SCAN_LOCKED_SHIP.POSITION;
                    if ((aimpos - COORDINATES[0]).Length() > 1200)
                    { aimpos = COORDINATES[0]; }
                }
                if (SCAN_HARDLOCK == true)
                {
                    aimpos = GetPredictedTargetPosition2(RC, SCAN_LOCKED_SHIP);
                    if ((aimpos - COORDINATES[0]).Length() > 1200)
                    { aimpos = COORDINATES[0]; }
                }




                if ((RC.GetPosition() - aimpos).Length() < 400)
                {

                    COLLECT_AND_FIRE(RC.GetPosition() + 200 * RC.WorldMatrix.Backward, 0, 10, RC.GetPosition());

                    GyroTurn4(aimpos, GYRO, 3, new Vector3D(), "FORWARD");
                }

                else if ((RC.GetPosition() - aimpos).Length() > 700)
                {
                    RC_MANAGER(COORDINATES[0]);

                }







                else if (SCAN_HARDLOCK)
                {
                    GyroTurn4(aimpos, GYRO, 3, new Vector3D(), "FORWARD");
                }



                if (Vector3D.Dot(RC.WorldMatrix.Forward, Vector3D.Normalize(aimpos - RC.GetPosition())) > 0.975 && (RC.GetPosition() - aimpos).Length() < 800)
                {

                    for (int i = 0; i < DIRECTIONAL_FIRE.Count; i++)
                    { DIRECTIONAL_FIRE[i].ApplyAction("ShootOnce"); DIRECTIONAL_FIRE[i].ApplyAction("Shoot_On"); }
                }
            }
            #endregion

            #region HM_Attack_Location
            
            double ENGAGE_DIST = 4000;
            int FIRETIMER = 0;
            int FIRE_TIME = 30;
            void Attack_Location_Heavy()
            {
                Echo("In Attack Situation\n");
                Echo(SCAN_LOCKED_SHIP.AIMPOS + "\n");

                if (SHIP_CLASS == "FGRAV")
                { PROJECTILE_VELOCITY = int.Parse(HW_TIMER.CustomData); }



                var aimpos = COORDINATES[0];
                if (ENEMY_DETECTED)
                {
                    aimpos = SCAN_LOCKED_SHIP.POSITION;
                    if ((aimpos - COORDINATES[0]).Length() > 1000)
                    { aimpos = COORDINATES[0]; }
                }
                if (SCAN_HARDLOCK == true)
                {
                    aimpos = GetPredictedTargetPosition2(RC, SCAN_LOCKED_SHIP);
                    if ((aimpos - COORDINATES[0]).Length() > 1000)
                    { aimpos = COORDINATES[0]; }
                }




                if ((RC.GetPosition() - aimpos).Length() < ENGAGE_DIST)
                {
                    COLLECT_AND_FIRE(RC.GetPosition() + 200 * RC.WorldMatrix.Backward, 0, 10, RC.GetPosition());
                    GyroTurn4(aimpos, GYRO, 2, new Vector3D(), "FORWARD");
                }
                else if (SCAN_LOCKED_SHIP.VELOCITY.Length() > 5 || (RC.GetPosition() - aimpos).Length() > ENGAGE_DIST)
                {
                    aimpos = aimpos - Vector3D.Normalize(aimpos - RC.GetPosition()) * 500;
                    COLLECT_AND_FIRE(aimpos, 0, MAX_SPEED, RC.GetPosition());
                    GyroTurn4(aimpos, GYRO, 2, new Vector3D(), "FORWARD");
                }
                else
                {
                    GyroTurn4(aimpos, GYRO, 2, new Vector3D(), "FORWARD");
                }









                if (SCAN_HARDLOCK && SHIP_CLASS == "FMISSILE")
                { HW_TIMER.CustomData = SCAN_LOCKED_SHIP.POSITION + "#" + (SCAN_LOCKED_SHIP.VELOCITY / 60); }
                if (Vector3D.Dot(RC.WorldMatrix.Forward, Vector3D.Normalize(aimpos - RC.GetPosition())) > 0.6 && (RC.GetPosition() - aimpos).Length() < ENGAGE_DIST && FIRETIMER > FIRE_TIME && SCAN_HARDLOCK)
                { HW_TIMER.Trigger(); FIRETIMER = 0; }
                FIRETIMER++;
            }
            #endregion

            #region DogPilot #RFC# - APCK Enhanced
            
            bool DP_HASRUN = false;
            List<IMyTerminalBlock> DP_THRUST = new List<IMyTerminalBlock>();
            List<IMyTerminalBlock> DP_BRAKE = new List<IMyTerminalBlock>();
            List<IMyTerminalBlock> DP_UP = new List<IMyTerminalBlock>();
            double DP_THRUST_MULTIPLIER;
            void DOGPILOT(Vector3D TARGETPOS, double TARG_VEL, bool OVERRIDE)
            {
                InitializeAPCK();

                if (apckController != null)
                {
                    apckController.AgileMoveTo(TARGETPOS, TARG_VEL, OVERRIDE);
                }
                else
                {
                    Echo("ERROR: APCK System Failed - Agile Movement Disabled");
                    // Stop all thrusters as safety measure
                    for (int j = 0; j < CAF2_THRUST.Count; j++)
                    {
                        CAF2_THRUST[j].SetValue<float>("Override", 0.0f);
                    }
                }
            }



            #endregion



            #region Connector Direction #RFC#
            
            void RETURN_CONNECTOR_DIRECTION()
            {
                if (CONNECTOR.Orientation.Forward == RC.Orientation.TransformDirection(Base6Directions.Direction.Down))
                { CONNECTOR_PLANE = "LEFT"; }
                if (CONNECTOR.Orientation.Forward == RC.Orientation.TransformDirection(Base6Directions.Direction.Up))
                { CONNECTOR_PLANE = "RIGHT"; }
                if (CONNECTOR.Orientation.Forward == RC.Orientation.TransformDirection(Base6Directions.Direction.Right))
                { CONNECTOR_PLANE = "UP"; }
                if (CONNECTOR.Orientation.Forward == RC.Orientation.TransformDirection(Base6Directions.Direction.Left))
                { CONNECTOR_PLANE = "DOWN"; }
            }
            #endregion

            #region SCAN
            
            struct DEC_INFO { public Vector3D POSITION; public Vector3D VELOCITY; public Vector3D AIMPOS; public double SIZE; public string ID; }
            bool SCAN_HARDLOCK = false;
            DEC_INFO SCAN_LOCKED_SHIP;
            int SCAN_PREDICTIVE = 0;
            int GOTO_SCAN_TIMER = 0;
            void SCAN_MANAGER(out bool ISTARGETING)
            {

                ISTARGETING = false;
                DEC_INFO TEMP_DETECT_INFO = SCAN_LOCKED_SHIP;
                MyDetectedEntityInfo TEMP_INFO = new MyDetectedEntityInfo();



                foreach (var item in DIRECTORS)
                {
                    if (item.IsShooting && item.HasTarget)
                    { DIRECTOR = item; break; }
                }



                if (DIRECTOR.HasTarget)
                {
                    ISTARGETING = true;
                    TEMP_INFO = DIRECTOR.GetTargetedEntity();
                }



                if (GOTO_SCAN_TIMER > 5)
                {
                    foreach (var ITEM in CAMERAS)
                    {
                        IMyCameraBlock CAMERA = (ITEM as IMyCameraBlock);
                        if (CAMERA.CanScan(COORDINATES[0]) && CAMERA.IsWorking)
                        {
                            Vector3D DIRECTION = Vector3D.Normalize(COORDINATES[0] - CAMERA.GetPosition());
                            double DISTANCE = (COORDINATES[0] - CAMERA.GetPosition()).Length() + 40;
                            Vector3D POS = DIRECTION * DISTANCE + CAMERA.GetPosition();
                            TEMP_INFO = CAMERA.Raycast(POS);
                            break;
                        }
                    }
                    GOTO_SCAN_TIMER = 0;
                }
                GOTO_SCAN_TIMER++;



                if (SCAN_HARDLOCK == true && TEMP_INFO.IsEmpty())
                {
                    Vector3D TESTPOS = SCAN_LOCKED_SHIP.POSITION + (SCAN_LOCKED_SHIP.VELOCITY) * 0.13333333 * SCAN_PREDICTIVE;
                    Vector3D DIRECTION = Vector3D.Normalize(TESTPOS - RC.GetPosition());
                    double DISTANCE = (TESTPOS - RC.GetPosition()).Length() + 40;
                    Vector3D POS = DIRECTION * DISTANCE + RC.GetPosition();
                    foreach (var ITEM in CAMERAS)
                    {
                        IMyCameraBlock CAMERA = (ITEM as IMyCameraBlock);
                        if (CAMERA.CanScan(POS) && CAMERA.IsWorking)
                        {
                            Echo(CAMERA.CustomName);

                            TEMP_INFO = CAMERA.Raycast(POS);
                            break;
                        }
                    }
                }



                if (!TEMP_INFO.IsEmpty() && TEMP_INFO.Relationship == MyRelationsBetweenPlayerAndBlock.Enemies)
                {
                    ISTARGETING = true;
                    Echo("SYSTEM LOCK");
                    SCAN_HARDLOCK = true;
                    SCAN_PREDICTIVE = 0;
                    TEMP_DETECT_INFO.POSITION = TEMP_INFO.Position;

                    TEMP_DETECT_INFO.AIMPOS = TEMP_INFO.Position;
                    TEMP_DETECT_INFO.SIZE = (TEMP_INFO.BoundingBox.Max - TEMP_INFO.BoundingBox.Min).Length();
                    TEMP_DETECT_INFO.VELOCITY = TEMP_INFO.Velocity;
                    TEMP_DETECT_INFO.ID = TEMP_INFO.EntityId + "";
                }


                if (SCAN_PREDICTIVE > 10)
                { SCAN_HARDLOCK = false; SCAN_PREDICTIVE = 0; Echo("Target Lost, Clearing Lock"); }


                SCAN_LOCKED_SHIP = TEMP_DETECT_INFO;
                SCAN_PREDICTIVE++;

            }


            #endregion

            #region RFC Function bar #RFC#
            
            string[] FUNCTION_BAR = new string[] { "", " ===||===", " ==|==|==", " =|====|=", " |======|", "  ======" };
            int FUNCTION_TIMER = 0;
            void OP_BAR()
            {
                FUNCTION_TIMER++;
                Echo("     ~ MKII RFC AI Running~  \n               " + FUNCTION_BAR[FUNCTION_TIMER] + "");
                if (FUNCTION_TIMER == 5) { FUNCTION_TIMER = 0; }
            }
            #endregion

            #region EXTRACTDIRECTION
            
            Vector3D ExtractDirection(String DIRECTION, IMyTerminalBlock BLOCK)
            {
                Vector3D OUTPUTDIRECTION = new Vector3D();

                if (DIRECTION == "UP")
                { OUTPUTDIRECTION = BLOCK.WorldMatrix.Up; }

                if (DIRECTION == "LEFT")
                { OUTPUTDIRECTION = BLOCK.WorldMatrix.Left; }

                if (DIRECTION == "FORWARD")
                { OUTPUTDIRECTION = BLOCK.WorldMatrix.Forward; }

                if (DIRECTION == "RIGHT")
                { OUTPUTDIRECTION = BLOCK.WorldMatrix.Right; }

                return OUTPUTDIRECTION;
            }

            #endregion

            #region GET_FREE_DESTINATION (prototype)
            
            Vector3D GFD_FREE_DESTINATION;
            int SCAN_CTIMER = 6;
            void GetFreeDestination(Vector3D TARGET)
            {

                if (SCAN_CTIMER > 5)
                {

                    Vector3D SHIP_VEl = Vector3D.Normalize(RC.GetShipVelocities().LinearVelocity);
                    Vector3D TO_TARG = Vector3D.Normalize(TARGET - RC.GetPosition());
                    MyDetectedEntityInfo TEMP_INFO = new MyDetectedEntityInfo();
                    GFD_FREE_DESTINATION = TARGET;


                    if (SCAN_HARDLOCK && (COORDINATES[0] - SCAN_LOCKED_SHIP.POSITION).Length() < 950)
                    {

                        double DIST_TO = (SCAN_LOCKED_SHIP.POSITION - RC.GetPosition()).Length();
                        Vector3D FREE_DIRECT_P1 = SHIP_VEl * DIST_TO + RC.GetPosition();
                        Vector3D FREE_DIRECT_P2 = SCAN_LOCKED_SHIP.POSITION;
                        Vector3D FREE_DIRECT = Vector3D.Normalize(FREE_DIRECT_P1 - FREE_DIRECT_P2);
                        GFD_FREE_DESTINATION = SCAN_LOCKED_SHIP.POSITION + FREE_DIRECT * (SCAN_LOCKED_SHIP.SIZE / 2 + 15);
                        Vector3D TARG = Vector3D.Normalize(SCAN_LOCKED_SHIP.POSITION - RC.GetPosition());
                        return;
                    }


                    Random RND = new Random();
                    double x = RND.Next(20) * Math.Pow(-1, RND.Next(2));
                    double y = RND.Next(20) * Math.Pow(-1, RND.Next(2));
                    double z = RND.Next(20) * Math.Pow(-1, RND.Next(2));
                    Vector3D RANDOMISER = new Vector3D(x, y, z);

                    Vector3D RAYPOS = MathHelper.Clamp(((TARGET - RC.GetPosition()).Length()), 0f, 600f) * TO_TARG + RC.GetPosition() + RANDOMISER;


                    foreach (var ITEM in CAMERAS)
                    {
                        IMyCameraBlock CAMERA = (ITEM as IMyCameraBlock);
                        if (CAMERA.CanScan(RAYPOS))
                        { TEMP_INFO = CAMERA.Raycast(RAYPOS); SCAN_CTIMER = -1; break; }
                    }


                    if (!TEMP_INFO.IsEmpty() && TEMP_INFO.Name != Me.CubeGrid.DisplayName)
                    {
                        Echo("collision detected");

                        double DIST_TO = (TEMP_INFO.Position - RC.GetPosition()).Length();
                        Vector3D FREE_DIRECT_P1 = SHIP_VEl * DIST_TO + RC.GetPosition();
                        Vector3D FREE_DIRECT_P2 = TEMP_INFO.Position;
                        Vector3D FREE_DIRECT = Vector3D.Normalize(FREE_DIRECT_P1 - FREE_DIRECT_P2);
                        GFD_FREE_DESTINATION = TEMP_INFO.Position + FREE_DIRECT * TEMP_INFO.BoundingBox.Size.Length() / 2;
                    }
                }

                SCAN_CTIMER++;
            }

            #endregion


            double PREV_MISSILEELEVATION;
            double PREV_MISSILEAZIMUTH;
double PREV_MISSILEROLL = 0;