          #region Introduction
            /*
            Introduction
            ----------------
            Hello and thank you for downloading Rdav's Fleet Command Beta
            Rdav's fleet command is a total-conversion code for automatic fleets.
            This code allows for artificially intelligent drone operation, drones
            adopt unique and intelligent behaviour allowing them to engage targets
            follow commands, dock, undock along with giving players advanced 
            control capabilities for an entire amarda.
            Please see the workshop page for more details;

            You are currently looking at the 'Central Command' Unit, which
            is the main hub of operations for the code, only ONE of these units
            should be used per-fleet, others can be kept as backups, but only one
            should be operational at any one time.

            Rdav 28/08/17

            Installation
            --------------
            The code should come in a Pre-Fab format to make installation a breeze.
            Setup of these modules manually can be achieved however for streamlining
            or otherwise making the components of the module smaller, please refer to
            the manual to do so.

            The Central Command Unit will automatically:
             * Find an antennae on the ship and use it for broadcast
             * Find a command seat used for issuing commands called 'RFC_RC' (only renaming required)
             * Find and use for target detection any turret with the required customdata tag

            Bughunting/Problems
            --------------------
            The code will automatically create a bugreport and save it to the custom-data of the
            designated remote block.

            Suggestions Planned Features
            -----------------------------
            - Let me know in the workshop page or my discord what you think it needs!

             ChangeLog:
             *Enables antenna
             *reversed forward direction on central command module
             *Enabled carrier on own grid and related functions
             * Added new stuff but had to remove it due to keen bugs
             * reimplemented keens new borked igc API

            */

            //USER ASSIGNED VARIABLES
            string DefaultFormation = ""; //Choose From Line abreast, line astern, drone wall (NOT YET IMPLEMENTED)

            //Tagname For Use With The System (note does not take ownership into consideration)
            const string IGCTagOUT = "RFC_FLEET_OUT";
            const string IGCTagIN = "RFC_FLEET_IN";
            IMyBroadcastListener Lstn;
            #endregion

            #region SETUP
            //STORED VARIABLES
            //---------------------------------------------------------------------------------------------------------------------- 

            //SUBCATEGORY PERMANENT ASSIGNMENTS:
            string VERSION = "Ver_008_Updated";                         //Script Current Version
            Dictionary<string, double> SPACINGS = new Dictionary<string, double>() { { "I", 40 }, { "C", 200 }, { "F", 150 }, { "U", -150 } };
            Dictionary<string, double> SPACINGS_X = new Dictionary<string, double>() { { "I", 100 }, { "C", 0 }, { "F", 0 }, { "U", -150 } };

            //SUBCATEGORY STORED BLOCKS
            IMyShipController RC;
            IMyRadioAntenna RADIO;
            List<IMyCameraBlock> CAMERAS = new List<IMyCameraBlock>();
            List<IMyLargeTurretBase> DIRECTORS = new List<IMyLargeTurretBase>();

            // Display System Blocks
            IMyTextPanel DISPLAY_PANEL;
            List<IMyGyro> DISPLAY_GYROS = new List<IMyGyro>();

            //SUBCATEGORY CLASSES
            class DRONE_INFO
            {
                public string ID; //Drone Identifier (contains classification)
                public string COMLOC; //Drone Command & locator
                public string GLOC; //Generated Locator
                public Vector3D LOC; //Current Drone Location
                public Vector3D VEL; //Drone Velocity
                public Vector3D TVEl; //Target Velocity vector
                public string ISDOCKED; //id of docked ship (own Id if not docked)
                public double HEALTH; //Health of the ship
                public DateTime LAST_PING; //Last recieved ping from the ship
                public string EXT_INF; //Drone Extra Info
                public string OUTPUT;   // String Drone Data Output

                //Standardised System Of Updating And Saving Drone Data
                public static DRONE_INFO DRONE_DATA_RS(string IN_ARG, DRONE_INFO DRONE_INF, bool[] RUN_ID)
                {
                    //Retrieves Data From Store
                    string[] DRN_INFO = IN_ARG.Split('*');
                    DRONE_INF.ID = (RUN_ID[0] != true) ? DRONE_INF.ID : DRN_INFO[0];
                    DRONE_INF.COMLOC = (RUN_ID[1] != true) ? DRONE_INF.COMLOC : DRN_INFO[1];
                    DRONE_INF.GLOC = (RUN_ID[2] != true) ? DRONE_INF.GLOC : DRN_INFO[2];
                    if (RUN_ID[3] == true) { Vector3D.TryParse(DRN_INFO[3], out DRONE_INF.LOC); }
                    if (RUN_ID[4] == true) { Vector3D.TryParse(DRN_INFO[4], out DRONE_INF.VEL); }
                    if (RUN_ID[5] == true) { Vector3D.TryParse(DRN_INFO[5], out DRONE_INF.TVEl); }
                    if (RUN_ID[6] == true) { DRONE_INF.ISDOCKED = DRN_INFO[6]; }
                    if (RUN_ID[7] == true) { DRONE_INF.HEALTH = double.Parse(DRN_INFO[7]); }
                    if (RUN_ID[8] == true) { DRONE_INF.LAST_PING = DateTime.Parse(DRN_INFO[8]); }
                    if (RUN_ID[9] == true) { DRONE_INF.EXT_INF = DRN_INFO[9]; }
                    return DRONE_INF;
                }

                public static DRONE_INFO SAVE(DRONE_INFO DRONE_INF)
                {
                    DRONE_INF.OUTPUT = string.Join("*", "#" + DRONE_INF.ID, DRONE_INF.COMLOC, DRONE_INF.GLOC, DRONE_INF.LOC, DRONE_INF.VEL, DRONE_INF.TVEl, DRONE_INF.ISDOCKED, DRONE_INF.HEALTH, DRONE_INF.LAST_PING, DRONE_INF.EXT_INF, "#" + DRONE_INF.ID);
                    return DRONE_INF;
                }
            }
            class DOCKPOINT_INFO
            {
                public string ID; //Dockpoint Identifier (contains docktype classification)
                public Vector3D LOC; //Current Dockpoint Location
                public string BASE_TAG; //ID of base ship
                public string ISDOCKED; //Id of docked ship (own Id if not docked)
                public DateTime LAST_PING; //Last recieved ping from the dockpoint
                public string OUTPUTROLL; //Coordinates package for drones to interperate
                public string OUTPUT;   // String Drone Data Output

                public List<IMyTerminalBlock> ROUTE; //List of route (drone ship only, not updated)

                //Standardised System Of Updating And Saving Drone Data
                public static DOCKPOINT_INFO DOCK_DATA_RS(string IN_ARG, DOCKPOINT_INFO DOCKPT_INF, bool[] RUN_ID)
                {
                    //Retrieves Data From Store
                    string[] DCK_INFO = IN_ARG.Split('*');
                    if (RUN_ID[0] == true) { DOCKPT_INF.ID = DCK_INFO[0]; }
                    if (RUN_ID[1] == true) { Vector3D.TryParse(DCK_INFO[1], out DOCKPT_INF.LOC); }
                    if (RUN_ID[2] == true) { DOCKPT_INF.BASE_TAG = DCK_INFO[2]; }
                    if (RUN_ID[3] == true) { DOCKPT_INF.ISDOCKED = DCK_INFO[3]; }
                    if (RUN_ID[4] == true) { DOCKPT_INF.LAST_PING = DateTime.Parse(DCK_INFO[4]); }
                    if (RUN_ID[5] == true) { DOCKPT_INF.OUTPUTROLL = DCK_INFO[5]; }

                    DOCKPT_INF.OUTPUT = string.Join("*", "#" + DOCKPT_INF.ID, DOCKPT_INF.LOC, DOCKPT_INF.BASE_TAG, DOCKPT_INF.ISDOCKED, DOCKPT_INF.LAST_PING, DOCKPT_INF.OUTPUTROLL, "#" + DOCKPT_INF.ID);
                    return DOCKPT_INF;
                }

                //Standardised DockString Saving Procedure
                public static DOCKPOINT_INFO SAVE_ROUTE_TO_STRING(DOCKPOINT_INFO DOCKPT_INFO)
                {
                    List<string> OUTPUT = new List<string>();
                    double OFFSET_CONST = 4;
                    List<IMyTerminalBlock> DOCKPT_TRAIL = DOCKPT_INFO.ROUTE;

                    //Adds First Ordinates (self and forwards position)
                    OUTPUT.Add(Vector3D.Round(DOCKPT_TRAIL[0].GetPosition() + DOCKPT_TRAIL[0].WorldMatrix.Forward * (1.5), 2) + "");
                    OUTPUT.Add(Vector3D.Round(DOCKPT_TRAIL[0].GetPosition() + DOCKPT_TRAIL[0].WorldMatrix.Forward * OFFSET_CONST, 2) + "");

                    //Iterates Through List Of LCD's
                    for (int i = 1; i < DOCKPT_TRAIL.Count; i++)
                    { var IMYPLACE = DOCKPT_TRAIL[i]; OUTPUT.Add(Vector3D.Round(IMYPLACE.GetPosition() + IMYPLACE.WorldMatrix.Backward * OFFSET_CONST, 2) + ""); }

                    //Adds Final Position
                    OUTPUT.Add(Vector3D.Round(DOCKPT_TRAIL[DOCKPT_TRAIL.Count - 1].GetPosition() +
                        DOCKPT_TRAIL[DOCKPT_TRAIL.Count - 1].WorldMatrix.Backward * OFFSET_CONST + DOCKPT_TRAIL[DOCKPT_TRAIL.Count - 1].WorldMatrix.Up * 100, 2) + "");


                    //Saves To String, Updates Locator, (And Updates OUTPUT)
                    OUTPUT.Reverse();
                    DOCKPT_INFO.OUTPUTROLL = string.Join("^", OUTPUT);
                    DOCKPT_INFO.LOC = Vector3D.Round(DOCKPT_TRAIL[0].GetPosition(), 2);
                    DOCKPT_INFO.OUTPUT = string.Join("*", "#" + DOCKPT_INFO.ID, DOCKPT_INFO.LOC, DOCKPT_INFO.BASE_TAG, DOCKPT_INFO.ISDOCKED, DOCKPT_INFO.LAST_PING, DOCKPT_INFO.OUTPUTROLL, "#" + DOCKPT_INFO.ID);

                    return DOCKPT_INFO;
                }

                public static DOCKPOINT_INFO SAVE(DOCKPOINT_INFO DOCKPT_INFO)
                {
                    DOCKPT_INFO.OUTPUT = string.Join("*", "#" + DOCKPT_INFO.ID, DOCKPT_INFO.LOC, DOCKPT_INFO.BASE_TAG, DOCKPT_INFO.ISDOCKED, DOCKPT_INFO.LAST_PING, DOCKPT_INFO.OUTPUTROLL, "#" + DOCKPT_INFO.ID);
                    return DOCKPT_INFO;
                }

            }
            class DC_INF_INFO
            {
                public int SIZE; //Size of target
                public string TYPE; //Type of target
                public Vector3D POSITION; //Position
                public string DIRECT_TO_OUTPUT; //Directly Outputted string
                public int ST_SIZE; //Start Size
                public Vector3D VELOCITY; //Only used For Internal Locks
            }
            Dictionary<string, DOCKPOINT_INFO> DOCKPOINTS = new Dictionary<string, DOCKPOINT_INFO>();
            Dictionary<string, DRONE_INFO> DRONES = new Dictionary<string, DRONE_INFO>();
            Dictionary<string, DC_INF_INFO> DECENTIN_INFO = new Dictionary<string, DC_INF_INFO>();
            DRONE_INFO MEINFO = new DRONE_INFO(); //Info About Command ship

            //SUBCATEGORY TEMPORARY ASSIGNMENTS: 
            bool FIRSTSETUP_HASRUN = false;                             //Whether Or Not Code has Been Setup
            int TIMER_SYSTEM = 0;
            Dictionary<string, int> FOLLOW_SPACING = new Dictionary<string, int>(); //

            //SUBCATEGORY DOCKING SETUP
            bool DOCKING_INIT;

            #endregion

            //Primary Operators

            #region MAIN METHOD (unfinished)
            /*====================================================================================================================                             
            Function: MAIN METHOD                
            ---------------------------------------------                            
            function will: Main Method, Timing Calculator And Diagnostics
            Performance Cost:
           //======================================================================================================================*/
            double LAST_RUNTIME;
            void Main(string argument, UpdateType updateSource)
            {

                try
                {
                    // Process IGC messages
                    while (Lstn.HasPendingMessage)
                    {
                        var message = Lstn.AcceptMessage();
                        argument = message.Data.ToString();
                        Echo($"[IGC] Received from fleet: {argument.Substring(0, Math.Min(50, argument.Length))}...");
                        break; // Process one message per tick
                    }

                    // If no IGC message, use default run command
                    if (string.IsNullOrEmpty(argument))
                    { argument = "~RFC RUN~"; }

                    Echo($"[DEBUG] Processing: {argument.Substring(0, Math.Min(30, argument.Length))}...");

                    //System Initialisation
                    //---------------------------------------          
                    if (FIRSTSETUP_HASRUN == false)
                    { FIRST_TIME_SETUP(); FIRSTSETUP_HASRUN = true; Echo("System Booting"); return; }

                    if (TIMER_SYSTEM == 0) { LAST_RUNTIME = Runtime.LastRunTimeMs; }
                    if (argument != "~RFC RUN~" && argument.Contains("#") == false) { return; }

                    //if (TIMER_SYSTEM != 8 && argument.Contains("#") == false)
                    //{ TIMER_SYSTEM++; return; }
                    //TIMER_SYSTEM = 0;

                    //Input Handler/Updater
                    //-------------------------------------------
                    if (argument != "~RFC RUN~" && argument.Contains("#") == false)
                    { return; }
                    INPUT_ORGANISER(argument);
                    if (argument != "~RFC RUN~")
                    { return; }

                    //syst diag
                    OP_BAR();
                    Echo(VERSION);
                    Echo(DRONES.Count + " Drones");
                    Echo(DOCKPOINTS.Count + " Docks");
                    Echo(DECENTIN_INFO.Count + " DEI'S");
                    Echo("Runtime: " + Math.Round(LAST_RUNTIME, 3));
                    Echo(VERSION);

                    //Loads CustomData
                    //---------------------------------
                    string CUSTDATA = Me.CustomData;
                    string SQ_DATA = CUSTDATA.Split(new string[] { "##INFO##" }, StringSplitOptions.None)[0]; //0 = sqdata
                    string[] SQ_DATA_LIST = SQ_DATA.Split(new string[] { "\n" }, StringSplitOptions.None);
                    foreach (var item in SQ_DATA_LIST)
                    {
                        if (item.Split('*').Length < 7) { continue; }
                        string[] itemParts = item.Split('*');
                        if (itemParts[0].Length < 7) { continue; } // Ensure we have enough characters for Substring
                        string DRONE_ID = itemParts[0].Substring(1, 6);
                        if (!DRONES.ContainsKey(DRONE_ID)) { continue; }
                        bool[] UPDATE_RUN = new bool[] { false, true, false, false, false, false, false, false, false, true };
                        DRONES[DRONE_ID] = DRONE_INFO.DRONE_DATA_RS(item, DRONES[DRONE_ID], UPDATE_RUN);
                    }

                    //Runs For Self Docking
                    //---------------------------
                    DOCK_SYST();
                    DRONES["CA0000"].LAST_PING = DateTime.Now;
                    MEINFO.LOC = Vector3D.Round(Me.GetPosition(), 2);
                    MEINFO.VEL = Vector3D.Round(RC.GetShipVelocities().LinearVelocity + (RC.WorldMatrix.Forward * 2));

                    //Scans Sensors For Objects Of Interest (sep method reqd) 
                    //---------------------------------------------------------
                    var SYST_SENSOR = GridTerminalSystem.GetBlockWithName("RFC_SENSOR") as IMySensorBlock;
                    List<MyDetectedEntityInfo> DETECTED_ENTITIES = new List<MyDetectedEntityInfo>();
                    if (SYST_SENSOR != null) //Only runs if able to
                    {
                        SYST_SENSOR.DetectedEntities(DETECTED_ENTITIES);
                        foreach (var item in DETECTED_ENTITIES)
                        {
                            if (DECENTIN_INFO.ContainsKey("DEI" + item.EntityId) == false && item.Relationship != MyRelationsBetweenPlayerAndBlock.FactionShare)
                            {
                                DC_INF_INFO NEW_ENTITY = new DC_INF_INFO();

                                string ID = "DEI" + item.EntityId;
                                NEW_ENTITY.SIZE = (int)item.BoundingBox.Size.Length();
                                NEW_ENTITY.ST_SIZE = (int)item.BoundingBox.Size.Length();
                                NEW_ENTITY.TYPE = (item.Type == MyDetectedEntityType.Asteroid) ? "AS" : "EN"; //item.Relationship != MyRelationsBetweenPlayerAndBlock.Enemies &&
                                NEW_ENTITY.POSITION = Vector3D.Round(item.Position, 2);
                                if ((item.BoundingBox.Size.Length() < 5 || item.Relationship == MyRelationsBetweenPlayerAndBlock.Owner)) { continue; } //halts if not applicable
                                DECENTIN_INFO.Add(ID, NEW_ENTITY);
                                DECENTIN_INFO[ID].DIRECT_TO_OUTPUT = "#" + ID + "^" + DECENTIN_INFO[ID].SIZE + "^" + DECENTIN_INFO[ID].TYPE + "^" + DECENTIN_INFO[ID].POSITION;
                            }
                            else if (item.Relationship != MyRelationsBetweenPlayerAndBlock.FactionShare)
                            {
                                DECENTIN_INFO["DEI" + item.EntityId].SIZE = (int)item.BoundingBox.Size.Length();
                                //DECENTIN_INFO["DEI" + item.EntityId].TYPE = (item.Type == MyDetectedEntityType.Asteroid) ? "AS" : "EN"; //(item.Relationship != MyRelationsBetweenPlayerAndBlock.Enemies) ? "AS" : "EN";
                                DECENTIN_INFO["DEI" + item.EntityId].POSITION = Vector3D.Round(item.Position, 2);
                                DECENTIN_INFO["DEI" + item.EntityId].DIRECT_TO_OUTPUT = "#" + "DEI" + item.EntityId + "^" + DECENTIN_INFO["DEI" + item.EntityId].SIZE + "^" + DECENTIN_INFO["DEI" + item.EntityId].TYPE + "^" + DECENTIN_INFO["DEI" + item.EntityId].POSITION;
                            }
                        }
                    }

                    //Runs DockPoint Logic (maybe once every ten seconds?)
                    //--------------------------------------------------------
                    List<string> DOCKKEYS = new List<string>(DOCKPOINTS.Keys);
                    string DOCKEDIDS = "";
                    foreach (var item in DRONES)
                    {
                        if (item.Value.COMLOC.Contains("DOCK")) //preassigns comlocced drones
                        { DOCKEDIDS = DOCKEDIDS + (item.Value.COMLOC); }
                    }
                    for (int i = 0; i < DOCKPOINTS.Count; i++)
                    {
                        //Echo(((DateTime)DOCKPOINTS[DOCKKEYS[i]].LAST_PING).Ticks + "Dk");
                        if (DateTime.Now.Ticks - ((DateTime)(DOCKPOINTS[DOCKKEYS[i]]).LAST_PING).Ticks > 100000000)
                        { DOCKPOINTS.Remove(DOCKKEYS[i]); continue; }
                        else if (DOCKEDIDS.Contains(DOCKKEYS[i]) == false)
                        { DOCKPOINTS[DOCKKEYS[i]].ISDOCKED = "free"; }
                    }

                    //Runs Primary Logic
                    //------------------------------
                    FOLLOW_SPACING = new Dictionary<string, int>();
                    List<string> KEYS = new List<string>(DRONES.Keys);
                    KEYS.Sort(); //Sorts keys
                    for (int i = 0; i < DRONES.Count; i++)
                    {
                        //Echo(((DateTime)DRONES[KEYS[i]].LAST_PING).Ticks + "");
                        if (DateTime.Now.Ticks - ((DateTime)(DRONES[KEYS[i]]).LAST_PING).Ticks > 100000000)
                        { DRONES.Remove(KEYS[i]); continue; }
                        else
                        { DRONES[KEYS[i]] = DRONE_RUN(DRONES[KEYS[i]]); }
                    }

                    //Output Handler
                    //-------------------------------------------
                    StringBuilder OUTPUT = new StringBuilder();
                    OUTPUT.Append("BRDCST");
                    foreach (var item in DRONES)
                    { OUTPUT.Append(item.Value.OUTPUT); }
                    string outputMessage = OUTPUT.ToString();
                    IGC.SendBroadcastMessage(IGCTagOUT, outputMessage, TransmissionDistance.TransmissionDistanceMax);
                    Echo($"[IGC] Sent to fleet: {outputMessage.Substring(0, Math.Min(50, outputMessage.Length))}...");
                    Echo($"[DEBUG] Broadcasting to {DRONES.Count} drones");
                    //RADIO.TransmitMessage(OUTPUT.ToString(), MyTransmitTarget.Owned); OLD IGC
                    EN_SCAN_MANAGER();

                    //Saves To Custom Data
                    //-----------------------
                    StringBuilder CUSTSAVE = new StringBuilder();
                    KEYS = new List<string>(DRONES.Keys);
                    KEYS.Sort(); //sorts keys
                    for (int i = 0; i < DRONES.Count; i++)
                    {
                        DRONES[KEYS[i]] = DRONE_INFO.SAVE(DRONES[KEYS[i]]);
                        CUSTSAVE.Append(DRONES[KEYS[i]].OUTPUT);
                        CUSTSAVE.Append("\n");
                    }
                    CUSTSAVE.Append("##INFO##\n");
                    DOCKKEYS = new List<string>(DOCKPOINTS.Keys);
                    for (int i = 0; i < DOCKPOINTS.Count; i++)
                    {
                        DOCKPOINTS[DOCKKEYS[i]] = DOCKPOINT_INFO.SAVE(DOCKPOINTS[DOCKKEYS[i]]);
                        CUSTSAVE.Append(DOCKPOINTS[DOCKKEYS[i]].OUTPUT);
                        CUSTSAVE.Append("\n");
                    }
                    CUSTSAVE.Append("##INFO##\n");
                    foreach (var item in DECENTIN_INFO)
                    {
                        CUSTSAVE.Append(item.Value.DIRECT_TO_OUTPUT);
                        CUSTSAVE.Append("\n");
                    }
                    Me.CustomData = CUSTSAVE + "";

                }
                catch (Exception e)
                { Echo(e + ""); }

            }
            #endregion

            #region DroneRun
            /*=======================================================================================                             
              Function: SQUAD_RUN                     
              ---------------------------------------                            
              function will: Output a string for all military ships
            //=======================================================================================*/
            DRONE_INFO DRONE_RUN(DRONE_INFO DRONE)
            {
                try
                {
                    //Error Checks
                    //-------------------------
                    if (DRONE.COMLOC == null) //Empty Comlocs
                    { return DRONE; }
                    if (DRONE.COMLOC.Split('^').Length > 1) //Comloc not in dictionary
                    {
                        //Echo((DRONES.ContainsKey(DRONE.COMLOC.Split('^')[1])) + " drone comloc in library");
                        if (DRONES.ContainsKey(DRONE.COMLOC.Split('^')[1]) == false && DOCKPOINTS.ContainsKey(DRONE.COMLOC.Split('^')[1]) == false && DRONE.COMLOC.Contains("GOTO") == false && DRONE.COMLOC.Contains("ATTACK") == false && DRONE.COMLOC.Contains("ME") == false)
                        { return DRONE; }
                    }

                    //Dock Reader (ensures undocking prior to any further actions)
                    //-------------------------------------------------------------
                    string COMM = DRONE.COMLOC.Split('^')[0];
                    if (DRONE.ISDOCKED != null)
                    {
                        //Dock Code Is Stating 
                        if (COMM != "DOCK" && DOCKPOINTS.ContainsKey(DRONE.ISDOCKED))
                        {
                            string ID = DRONE.ISDOCKED;
                            DRONE.GLOC = DOCKPOINTS[ID].OUTPUTROLL;
                            DRONE.TVEl = new Vector3D(0, 0, 0);
                            DRONE.COMLOC = "UNDOCK^" + ID;
                            DOCKPOINTS[ID].ISDOCKED = "UD";

                            Echo((DOCKPOINTS[ID].LOC - DRONE.LOC).Length() + " dist");
                            if ((DOCKPOINTS[ID].LOC - DRONE.LOC).Length() > 70)
                            { DRONE.ISDOCKED = DRONE.ID; DOCKPOINTS[ID].ISDOCKED = "free"; DRONE.COMLOC = ""; }

                            return DRONE;
                        }
                    }

                    //Command Is Dock
                    //------------------
                    if (DRONE.COMLOC.Contains("DOCK"))
                    {
                        //Mating Point Setup
                        //-------------------------------------------------
                        string LOC_ID = DRONE.COMLOC.Split('^')[1];

                        //First Time Dockpoint Assigner
                        //--------------------------------------------
                        if (DRONES.ContainsKey(LOC_ID))
                        {
                            //Creates List Of Free Dockpoints On Selected Ship
                            List<DOCKPOINT_INFO> TEMP_DOCKS = new List<DOCKPOINT_INFO>();
                            foreach (var item in DOCKPOINTS)
                            {
                                if (item.Value.ISDOCKED == "free" && item.Value.BASE_TAG == LOC_ID)
                                { TEMP_DOCKS.Add(item.Value); }
                            }

                            //Allocates First One
                            if (TEMP_DOCKS.Count > 0)
                            { DRONE.COMLOC = "DOCK^" + TEMP_DOCKS[0].ID; DOCKPOINTS[TEMP_DOCKS[0].ID].ISDOCKED = "NC"; }
                            else
                            { DRONE.COMLOC = "FOLLOW^" + LOC_ID; return DRONE; } //follow handled on next sequential cycle (eg this only reassigns, next cycle reevaluates this)
                        }

                        //Sets Mating Dockpoint And Sets Outputroll
                        //--------------------------------------------
                        else if (DOCKPOINTS.ContainsKey(LOC_ID))
                        {
                            DOCKPOINTS[LOC_ID].ISDOCKED = "NC";
                            DRONE.ISDOCKED = LOC_ID;
                            DRONE.GLOC = DOCKPOINTS[LOC_ID].OUTPUTROLL;
                            DRONE.TVEl = new Vector3D(0, 0, 0);
                        }
                        return DRONE;
                    }

                    //Command Is Follow
                    //--------------------
                    if (DRONE.COMLOC.Contains("FOLLOW"))
                    {
                        //Find Following Squad positional Data
                        string LOC = DRONE.COMLOC.Split('^')[1];
                        if (FOLLOW_SPACING.ContainsKey(LOC) == false)
                        { FOLLOW_SPACING.Add(LOC, 2); }
                        else { FOLLOW_SPACING[LOC]++; }
                        int DIFFER = FOLLOW_SPACING[LOC];

                        //Generates Target Position
                        Vector3D LEFT_VECT = Vector3D.Normalize(RC.WorldMatrix.Left * 10); //+ DRONES[LOC].VEL (for more precice coordinates)
                        Vector3D UP_VECT = RC.WorldMatrix.Up;
                        Vector3D TARGPOS = DRONES.ContainsKey(LOC) ? DRONES[LOC].LOC : RC.GetPosition();
                        Vector3D TARGVEL = DRONES.ContainsKey(LOC) ? DRONES[LOC].VEL : RC.GetShipVelocities().LinearVelocity + (RC.WorldMatrix.Backward * 0.01);

                        string droneClass = DRONE.ID.Length > 0 ? DRONE.ID.Substring(0, 1) : "A"; // Safe substring with fallback
                        Vector3D ADD_VERTICAL = (LOC.Contains("I")) ? new Vector3D() : SPACINGS_X[droneClass] * UP_VECT; //only y coord if not I-class
                        Vector3D ADD_ON_POS = (Math.Pow(-1, DIFFER) == -1) ? TARGPOS + SPACINGS[droneClass] * (DIFFER - 1) * -1 * LEFT_VECT + ADD_VERTICAL : TARGPOS + SPACINGS[droneClass] * DIFFER * LEFT_VECT + ADD_VERTICAL; //staggers centreline * Math.Pow(-1,DIFFER)

                        //Saves Data
                        DRONE.GLOC = ADD_ON_POS + "";
                        DRONE.TVEl = TARGVEL;

                        // Send command ship orientation for formation alignment
                        // Format: ORIENT|Forward|Up (using pipe separator to avoid conflicts with coordinate parsing)
                        DRONE.EXT_INF = "ORIENT|" + RC.WorldMatrix.Forward + "|" + RC.WorldMatrix.Up;
                    }

                    //Command Is Attack
                    //-------------------
                    if (DRONE.COMLOC.Contains("ATTACK")) //Attack Tagged Onto End
                    {
                        //Splits Command
                        //Vector3D OUTTER = new Vector3D();
                        //Vector3D.TryParse(DRONE.COMLOC.Split('^')[1], out OUTTER);

                        //Retrieves DEI From List;
                        if (DECENTIN_INFO.ContainsKey(DRONE.COMLOC.Split('^')[1]))
                        {
                            DRONE.GLOC = DECENTIN_INFO[DRONE.COMLOC.Split('^')[1]].POSITION + "";
                            DRONE.TVEl = DECENTIN_INFO[DRONE.COMLOC.Split('^')[1]].VELOCITY;
                        }
                        else
                        {
                            DRONE.COMLOC = "GOTO^" + DRONE.GLOC; //Last detected position
                            DRONE.TVEl = new Vector3D(0, 0, 0);
                        }


                        ////Finds DEI That's Closest To Command (if within 1000m of target location)
                        //double DIST = 1000;
                        //DC_INF_INFO ID_EN = new DC_INF_INFO();
                        //foreach (var item in DECENTIN_INFO)
                        //{
                        //    //Retrieves values For Important comparators;
                        //    Vector3D GLOC_PROCES = item.Value.POSITION; // Vector3D.TryParse(, out GLOC_PROCES);
                        //    double DIST_TT = (GLOC_PROCES - OUTTER).Length();
                        //    bool IS_EN = item.Value.DIRECT_TO_OUTPUT.Contains("EN");
                        //    if (IS_EN && DIST_TT < DIST) { ID_EN = item.Value; DIST = DIST_TT; }
                        //}

                        ////If Target Detected Or Not Near Detected Location Sets To Attack, Otherwise goto a normal command & Clear Attack
                        //if (ID_EN.DIRECT_TO_OUTPUT != null && ID_EN.POSITION!= new Vector3D())
                        //{
                        //    DRONE.COMLOC = "ATTACK^" + ID_EN.POSITION; //Reassigns goto to ensure chase
                        //    DRONE.GLOC = ID_EN.POSITION + "";
                        //    DRONE.TVEl = new Vector3D(0, 0, 0);
                        //}
                        //else  //Distance Larger Than 100m do not redesignate if ((DRONE.LOC - OUTTER).Length() > 100)
                        //{
                        //    DRONE.GLOC = OUTTER + "";
                        //    DRONE.TVEl = new Vector3D(0, 0, 0);
                        //}
                        //else
                        //{
                        //    DRONE.COMLOC = "GOTO^" + OUTTER; //Last detected position
                        //    DRONE.GLOC = OUTTER + "";
                        //    DRONE.TVEl = new Vector3D(0, 0, 0);
                        //}   

                    }

                    //Command Is GoTo
                    //------------------
                    if (DRONE.COMLOC.Contains("GOTO"))
                    {

                        //Splits Command
                        Vector3D OUTTER = new Vector3D();
                        Vector3D.TryParse(DRONE.COMLOC.Split('^')[1], out OUTTER);

                        //Saves Data
                        DRONE.GLOC = OUTTER + "";
                        DRONE.TVEl = new Vector3D(0, 0, 0);

                        // Send command ship orientation for roll normalization during GOTO
                        // Format: ORIENT|Forward|Up (using pipe separator to avoid conflicts with coordinate parsing)
                        DRONE.EXT_INF = "ORIENT|" + RC.WorldMatrix.Forward + "|" + RC.WorldMatrix.Up;

                    }

                    //Sets Attack Based Logic Systems (Outdated Code)
                    //------------------------------------------------------
                    //double DIST = 999999999;
                    //DC_INF_INFO ID_EN = new DC_INF_INFO();
                    //foreach (var item in DECENTIN_INFO)
                    //{
                    //    //Retrieves values For Important comparators;
                    //    Vector3D GLOC_PROCES; Vector3D.TryParse(DRONE.GLOC, out GLOC_PROCES);
                    //    double DIST_TT = (GLOC_PROCES - item.Value.POSITION).Length();
                    //    bool IS_EN = item.Value.DIRECT_TO_OUTPUT.Contains("EN");
                    //    if (IS_EN && DIST_TT < 1000 && DIST_TT < DIST) { ID_EN = item.Value; DIST = DIST_TT; }
                    //}
                    //if (ID_EN.DIRECT_TO_OUTPUT != null)
                    //{
                    //    //Checks For Agrro And Assigns
                    //    if (ID_EN.DIRECT_TO_OUTPUT.Contains("EN") && DRONE.ID.Contains("CA") == false && DRONE.ID.Contains("CR") == false)
                    //    {
                    //        Echo("reassigning to attack");
                    //        DRONE.GLOC = ID_EN.POSITION + "";
                    //        DRONE.TVEl = new Vector3D(0, 0, 0);

                    //        if (DRONE.COMLOC == "")
                    //        { DRONE.COMLOC = "GOTO^" + ID_EN.POSITION; }
                    //    }
                    //}



                }
                catch (Exception e) { Echo(DRONE.ID + " ERROR IN "); RC.CustomData = e + ""; }
                return DRONE;
            }
            #endregion

            #region First Time Setup #RFC#
            /*====================================================================================================================                             
            Function: FIRST_TIME_SETUP                   
            ---------------------------------------                            
            function will: Initiates Systems and initiasing Readouts to LCD
            Performance Cost:
           //======================================================================================================================*/
            void FIRST_TIME_SETUP()
            {
                //Gathers Key Components
                //-----------------------------------

                //Gathers Remote Control
                try
                {
                    List<IMyTerminalBlock> TEMP_RC = new List<IMyTerminalBlock>();
                    GridTerminalSystem.GetBlocksOfType<IMyShipController>(TEMP_RC, b => b.CubeGrid == Me.CubeGrid && b.CustomName == "RFC_RC");
                    RC = TEMP_RC[0] as IMyShipController;
                }
                catch { }

                //Gathers Antennae
                try
                {
                    List<IMyTerminalBlock> TEMP = new List<IMyTerminalBlock>();
                    GridTerminalSystem.GetBlocksOfType<IMyRadioAntenna>(TEMP, b => (b.CubeGrid == Me.CubeGrid && b.CustomData.Contains("RFC_ANT")));
                    RADIO = TEMP[0] as IMyRadioAntenna;
                    // Note: PBList property is deprecated, but antenna still used for identification
                    RADIO.Enabled = true;
                }
                catch { }

                //Gathers Cameras
                try
                {
                    GridTerminalSystem.GetBlocksOfType<IMyCameraBlock>(CAMERAS); //, b => b.CubeGrid == Me.CubeGrid
                    foreach (var item in CAMERAS)
                    { (item as IMyCameraBlock).EnableRaycast = true; }
                }
                catch { }

                //Gathers Display Panel
                try
                {
                    DISPLAY_PANEL = GridTerminalSystem.GetBlockWithName("RFC_PANEL") as IMyTextPanel;
                }
                catch { }

                //Gathers Gyros for Display Control
                try
                {
                    GridTerminalSystem.GetBlocksOfType<IMyGyro>(DISPLAY_GYROS, b => b.CubeGrid == Me.CubeGrid);
                }
                catch { }

                //Gathers Directors
                try
                {
                    GridTerminalSystem.GetBlocksOfType<IMyLargeTurretBase>(DIRECTORS, b => b.CustomData == "DIRECTOR" && b.CubeGrid == Me.CubeGrid);
                }
                catch { }

                //Initialises Self Ship
                MEINFO = new DRONE_INFO();
                MEINFO.ID = "CA0000";
                MEINFO.COMLOC = "++++";
                MEINFO.LOC = Vector3D.Round(Me.GetPosition(), 2);
                MEINFO.GLOC = "++++";
                MEINFO.ISDOCKED = "false";
                MEINFO.TVEl = new Vector3D();
                MEINFO.VEL = Vector3D.Round(RC.GetShipVelocities().LinearVelocity + (RC.WorldMatrix.Forward * 2));
                MEINFO.HEALTH = 0.9;
                MEINFO.LAST_PING = DateTime.Now;
                DRONE_INFO.SAVE(MEINFO);

                // Check if command ship already exists before adding
                if (!DRONES.ContainsKey(MEINFO.ID))
                {
                    DRONES.Add(MEINFO.ID, MEINFO);
                }
                else
                {
                    DRONES[MEINFO.ID] = MEINFO; // Update existing entry
                }

                //Initialises Self And Docking Routes
                if (DOCKING_INIT == false)
                { DOCK_INIT(); DOCKING_INIT = true; }

                //Creates From Permanent Storage
                //--------------------------------
                string CUSTDATA = Me.CustomData;

                // Safe splitting with bounds checking
                string[] CUSTDATA_PARTS = CUSTDATA.Split(new string[] { "##INFO##" }, StringSplitOptions.None);
                string SQ_DATA = CUSTDATA_PARTS.Length > 0 ? CUSTDATA_PARTS[0] : "";
                string[] SQ_DATA_LIST = SQ_DATA.Split(new string[] { "\n" }, StringSplitOptions.None);
                string DK_DATA = CUSTDATA_PARTS.Length > 1 ? CUSTDATA_PARTS[1] : "";
                string[] DK_DATA_LIST = DK_DATA.Split(new string[] { "\n" }, StringSplitOptions.None);

                foreach (var item in SQ_DATA_LIST)
                {
                    if (item.Split('*').Length < 7) { continue; }
                    string[] itemParts = item.Split('*');
                    if (itemParts[0].Length < 7) { continue; } // Ensure we have enough characters for Substring
                    string DRONE_ID = itemParts[0].Substring(1, 6);
                    if (DRONE_ID.Length < 4 || DRONE_ID == "CA0000") { continue; }
                    DRONE_INFO NEW_DRONE = new DRONE_INFO();
                    bool[] UPDATE_RUN = new bool[] { false, true, false, false, false, false, false, false, false, false };
                    NEW_DRONE = DRONE_INFO.DRONE_DATA_RS(item, NEW_DRONE, UPDATE_RUN);
                    NEW_DRONE.LAST_PING = DateTime.Now;
                    NEW_DRONE.ID = DRONE_ID;

                    // Check if drone already exists before adding
                    if (!DRONES.ContainsKey(DRONE_ID))
                    {
                        DRONES.Add(DRONE_ID, NEW_DRONE);
                        Echo("Successfully Added New Drone " + DRONES.Count + " " + DRONE_ID);
                    }
                    else
                    {
                        DRONES[DRONE_ID] = NEW_DRONE; // Update existing entry
                        Echo("Updated Existing Drone " + DRONE_ID);
                    }
                }

                foreach (var item in DK_DATA_LIST)
                {
                    if (item.Split('*').Length < 4) { continue; }
                    string[] itemParts = item.Split('*');
                    if (itemParts[0].Length < 7) { continue; } // Ensure we have enough characters for Substring
                    string ID = itemParts[0].Substring(1, 6);
                    if (ID.Length < 4 || DOCKPOINTS.ContainsKey(ID)) { continue; }
                    DOCKPOINT_INFO NEW_DRONE = new DOCKPOINT_INFO();
                    bool[] UPDATE_RUN = new bool[] { true, true, true, true, true, true };
                    NEW_DRONE = DOCKPOINT_INFO.DOCK_DATA_RS(item, NEW_DRONE, UPDATE_RUN);
                    NEW_DRONE.LAST_PING = DateTime.Now;
                    NEW_DRONE.ID = ID;
                    DOCKPOINTS.Add(ID, NEW_DRONE);
                    Echo("Sucessfully Added New Dockpoint " + DRONES.Count + " " + ID);
                }

            }
            //----------==--------=------------=-----------=---------------=------------=-------==--------=------------=-----------=----------
            #endregion

            #region RFC Function bar #RFC#
            /*=================================================                           
              Function: RFC Function bar #RFC#                  
              ---------------------------------------     */
            string[] FUNCTION_BAR = new string[] { "", " ===||===", " ==|==|==", " =|====|=", " |======|", "  ======" };
            int FUNCTION_TIMER = 0;                                     //For Runtime Indicator
            void OP_BAR()
            {
                FUNCTION_TIMER++;
                Echo("     ~ MKII RFC AI Running~  \n               " + FUNCTION_BAR[FUNCTION_TIMER] + "");
                if (FUNCTION_TIMER == 5) { FUNCTION_TIMER = 0; }
            }
            #endregion

            #region Input Organiser
            /*=======================================================================================                             
              Function: INPUT_ORGANISER                   
              ---------------------------------------                            
              function will: Save or update newly Found Drones Or Docking Points
            //=======================================================================================*/
            void INPUT_ORGANISER(string argument)
            {
                Echo($"[INPUT_ORGANISER] Processing: {argument.Substring(0, Math.Min(100, argument.Length))}...");
                string[] InputData = argument.Split('#');
                foreach (var item in InputData)
                {
                    if (string.IsNullOrEmpty(item) || item.Split('*').Length < 2) continue;
                    string ID = item.Split('*')[0]; //Item ID
                    Echo($"[INPUT_ORGANISER] Processing item with ID: {ID}");

                    //Case, (Standard Drones)
                    //------------------------------------------------
                    if (ID.Contains("DK") == false && ID.Contains("DEI") == false && ID.Length >= 4)
                    {
                        if (DRONES.ContainsKey(ID) == false && item.Split('*').Length > 8)
                        {
                            DRONE_INFO NEW_DRONE = new DRONE_INFO();
                            bool[] FC_CENTRAL_RUN = new bool[] { true, true, false, true, true, true, true, true, true, false };
                            NEW_DRONE = DRONE_INFO.DRONE_DATA_RS(item, NEW_DRONE, FC_CENTRAL_RUN);
                            NEW_DRONE.GLOC = NEW_DRONE.LOC + "";
                            if (DOCKPOINTS.ContainsKey(NEW_DRONE.ISDOCKED)) //Function To ensure if it appears already docked allows it to initiaslise docked
                            { NEW_DRONE.GLOC = "DOCK^" + NEW_DRONE.ISDOCKED; }
                            DRONES.Add(ID, NEW_DRONE);
                            Echo("Successfully Added New Drone " + ID + " (Total: " + DRONES.Count + ")");
                        }
                        else if (item.Split('*').Length > 8 && DRONES.ContainsKey(ID))
                        {
                            bool[] FC_CENTRAL_RUN = new bool[] { false, false, false, true, true, true, false, true, true, false };
                            DRONES[ID] = DRONE_INFO.DRONE_DATA_RS(item, DRONES[ID], FC_CENTRAL_RUN);
                            Echo($"Updated drone {ID} data");
                        }
                    }

                    //Case, Dockpooints
                    //---------------------------------------------
                    else if (ID.Contains("DK") == true)
                    {
                        if (DOCKPOINTS.ContainsKey(ID) == false && item.Split('*').Length > 4)
                        {
                            DOCKPOINT_INFO NEW_DOCKPT = new DOCKPOINT_INFO();
                            bool[] DOCKPOINT_INPUT = new bool[] { true, true, true, false, true, true };
                            NEW_DOCKPT = DOCKPOINT_INFO.DOCK_DATA_RS(item, NEW_DOCKPT, DOCKPOINT_INPUT);
                            DOCKPOINTS.Add(ID, NEW_DOCKPT);
                            Echo("Sucessfully Added New Dockpoint " + DRONES.Count + "");
                        }
                        else if (item.Split('*').Length > 4)
                        {
                            bool[] DOCKPOINT_INPUT = new bool[] { false, true, false, false, true, true };
                            DOCKPOINTS[ID] = DOCKPOINT_INFO.DOCK_DATA_RS(item, DOCKPOINTS[ID], DOCKPOINT_INPUT);
                        }
                    }

                    //Case, Is a DEI
                    //--------------------------
                    else if (ID.Contains("DEI") == true)
                    {
                        ID = item.Split('^')[0]; //Item ID
                        if (DECENTIN_INFO.ContainsKey(ID) == false && item.Split('^').Length > 3)
                        {
                            DC_INF_INFO NEW_DEI = new DC_INF_INFO();
                            NEW_DEI.DIRECT_TO_OUTPUT = "#" + item;
                            NEW_DEI.TYPE = "EN";
                            Vector3D TRIAL_POS_AS; Vector3D.TryParse(item.Split('^')[3], out TRIAL_POS_AS);
                            NEW_DEI.POSITION = TRIAL_POS_AS;
                            DECENTIN_INFO.Add(ID, NEW_DEI);
                            //Echo("Sucessfully Added New DEI" + DRONES.Count + "");
                        }
                        else if (item.Split('^').Length > 3)
                        {
                            Vector3D TRIAL_POS_AS; Vector3D.TryParse(item.Split('^')[3], out TRIAL_POS_AS);
                            DECENTIN_INFO[ID].POSITION = TRIAL_POS_AS;
                            DECENTIN_INFO[ID].DIRECT_TO_OUTPUT = "#" + item;
                        }
                    }
                }
            }
            #endregion

            #region SCAN
            /*====================================================================================================================================                                  
            Secondary Function: SCAN_MANAGER                            
            -----------------------------------------------------                                 
            Function will: Given two inputs manage vector-based thrusting               
            Outputs: Position for shot convergence, if there is a target in the way, if that target is friendly, information reagrding locked ship                  
            //-=--------------=-----------=-----------=-------------------=-------------------=----------------------=----------------------------*/
            void EN_SCAN_MANAGER()
            {
                //Initialises Temporary Camera List
                //-------------------------------------
                List<IMyCameraBlock> TEMP_CAMERAS = new List<IMyCameraBlock>();
                TEMP_CAMERAS.AddList(CAMERAS);

                //Runs through For Each DEI, If Enemy Initialise Tracking
                //---------------------------------------------------------
                foreach (var item in DECENTIN_INFO)
                {

                    //Only Enemy Ships
                    //----------------------------
                    if (item.Value.TYPE != "EN")
                    { continue; }

                    //Initialises DEI
                    //-------------------
                    MyDetectedEntityInfo TEMP_INFO = new MyDetectedEntityInfo();
                    var SCAN_LOCKED_SHIP = item.Value;

                    //Runs Locked System Scan
                    //-------------------------
                    Vector3D TESTPOS = SCAN_LOCKED_SHIP.POSITION + (SCAN_LOCKED_SHIP.VELOCITY) * 0.13333333 * 1; //Runtime.TimeSinceLastRun.Milliseconds/1000
                    Vector3D DIRECTION = Vector3D.Normalize(TESTPOS - RC.GetPosition());
                    double DISTANCE = (TESTPOS - RC.GetPosition()).Length() + 40;
                    Vector3D POS = DIRECTION * DISTANCE + RC.GetPosition();
                    foreach (var ITEM in TEMP_CAMERAS)
                    {
                        IMyCameraBlock CAMERA = (ITEM as IMyCameraBlock);
                        if (CAMERA.CanScan(POS) && CAMERA.IsWorking && CAMERA.AvailableScanRange > ((POS - CAMERA.GetPosition()).Length() + 40))
                        {
                            //Echo(CAMERA.CustomName);
                            TEMP_CAMERAS.Remove(ITEM);
                            TEMP_INFO = CAMERA.Raycast(POS);
                            break;
                        }
                    }

                    //Detects enemy entity info and outputs
                    //Echo((!TEMP_INFO.IsEmpty()) +""+ (TEMP_INFO.Relationship == MyRelationsBetweenPlayerAndBlock.Enemies) + "");
                    if (!TEMP_INFO.IsEmpty() && TEMP_INFO.Relationship == MyRelationsBetweenPlayerAndBlock.Enemies)
                    {
                        item.Value.POSITION = TEMP_INFO.Position;
                        item.Value.SIZE = (int)(TEMP_INFO.BoundingBox.Max - TEMP_INFO.BoundingBox.Min).Length();
                        item.Value.VELOCITY = TEMP_INFO.Velocity;
                    }
                    else
                    {
                        if ((DateTime.Now.Second + "").Contains("7"))
                        { DECENTIN_INFO.Remove(item.Key); }
                    }
                }



                //Runs Through Each Opoerational Turret, Adds Anything Detected to list
                //----------------------------------------------------------------------
                foreach (var item in DIRECTORS)
                {
                    if (item.IsUnderControl)
                    {
                        //Initialises Scan
                        var DIRECTOR = item;
                        #region Turret Vector Snippet
                        //GETTING TURRET VECTOR                                           //If tracking identify direction and position
                        float AZ = DIRECTOR.Azimuth;
                        float EL = DIRECTOR.Elevation;
                        Vector3 TURRETVECTOR = new Vector3(0, 0, 0);
                        Vector3.CreateFromAzimuthAndElevation(AZ, EL, out TURRETVECTOR);
                        //-----------------------------------------------------------------------------------------      
                        Vector3D ORIGINPOS = DIRECTOR.GetPosition();
                        //---------------------------------------- 
                        //Get forward unit vector       
                        var FORWARDPOS = DIRECTOR.Position + Base6Directions.GetIntVector(DIRECTOR.Orientation.TransformDirection(Base6Directions.Direction.Forward));
                        var FORWARD = DIRECTOR.CubeGrid.GridIntegerToWorld(FORWARDPOS);
                        var FORWARDVECTOR = Vector3D.Normalize(FORWARD - ORIGINPOS);
                        //---------------------------------------- 
                        //Get Up unit vector        
                        var UPPOS = DIRECTOR.Position + Base6Directions.GetIntVector(DIRECTOR.Orientation.TransformDirection(Base6Directions.Direction.Up));
                        var UP = DIRECTOR.CubeGrid.GridIntegerToWorld(UPPOS);
                        var UPVECTOR = Vector3D.Normalize(UP - ORIGINPOS);
                        //----------------------------------------     
                        Quaternion QUAT_ONE = Quaternion.CreateFromForwardUp(FORWARDVECTOR, UPVECTOR);
                        //---------------------------------------- 
                        //APPLYING QUAT TO A         
                        Vector3D TARGETPOS1 = Vector3D.Transform(TURRETVECTOR, QUAT_ONE);
                        #endregion
                        MyDetectedEntityInfo TEMP_INFO = new MyDetectedEntityInfo();

                        //Runs Through Each Camera
                        foreach (var ITEM in TEMP_CAMERAS)
                        {
                            IMyCameraBlock CAMERA = (ITEM as IMyCameraBlock);
                            if (CAMERA.CanScan(TARGETPOS1 * 10000 + DIRECTOR.GetPosition()))
                            {
                                TEMP_CAMERAS.Remove(ITEM);
                                TEMP_INFO = CAMERA.Raycast(TARGETPOS1 * 10000 + DIRECTOR.GetPosition());
                                break;
                            }
                        }

                        //Adds DEI If not already Added
                        if (!TEMP_INFO.IsEmpty() && TEMP_INFO.Relationship != MyRelationsBetweenPlayerAndBlock.Owner)
                        {
                            if (DECENTIN_INFO.ContainsKey("DEI" + TEMP_INFO.EntityId) == false)
                            {
                                DC_INF_INFO NEW_DEI = new DC_INF_INFO();
                                NEW_DEI.DIRECT_TO_OUTPUT = "#" + "DEI" + TEMP_INFO.EntityId + "^" + 20 + "^" + "EN" + "^" + Vector3D.Round(TEMP_INFO.Position, 2);
                                NEW_DEI.TYPE = "EN";
                                NEW_DEI.POSITION = TEMP_INFO.Position;
                                NEW_DEI.VELOCITY = TEMP_INFO.Velocity;
                                DECENTIN_INFO.Add("DEI" + TEMP_INFO.EntityId, NEW_DEI);
                                //Echo("Sucessfully Added New DEI" + DRONES.Count + "");
                            }
                        }
                    }
                }
            }
            //----------==--------=------------=-----------=---------------=------------=-------==--------=------------=-----------=----------

            #endregion

            //Docking Systems

            #region DOCK_SYSTEM CENCOM
            /*====================================================================================================================================                                  
            Secondary Function: DOCK_SYSTEM                          
            -----------------------------------------------------                                 
            Function will: Operate docking & undocking route setup and outputs             
            //-=--------------=-----------=-----------=-------------------=-------------------=----------------------=----------------------------*/
            string DOCK_SYST()
            {

                //Generates System Dock Outputs 
                StringBuilder DOCKOUTPUT = new StringBuilder();
                List<string> KEYS = new List<string>(DOCKPOINTS.Keys);
                for (int i = 0; i < DOCKPOINTS.Count; i++)
                {
                    //Only For dockpoints on this ship
                    if (DOCKPOINTS[DOCKPOINTS[KEYS[i]].ID].BASE_TAG.Contains("CA0000") == false || DOCKPOINTS[DOCKPOINTS[KEYS[i]].ID].ROUTE == null)
                    { continue; }

                    //Updates Docking Location
                    DOCKPOINTS[DOCKPOINTS[KEYS[i]].ID].LAST_PING = DateTime.Now;
                    DOCKPOINTS[DOCKPOINTS[KEYS[i]].ID] = DOCKPOINT_INFO.SAVE_ROUTE_TO_STRING(DOCKPOINTS[KEYS[i]]);

                    //Appends Location To String
                    DOCKOUTPUT.Append(DOCKPOINTS[KEYS[i]].OUTPUT);
                }
                return DOCKOUTPUT + "";
            }
            #endregion

            #region Connector Trail Setup
            /*=======================================================================================                             
              Function: Connector Trail Setup  
              ---------------------------------------                            
              Function will: Setup A Connector-LCD Trail
            //=======================================================================================*/
            List<IMyTerminalBlock> TRAIL_SETUP(IMyShipConnector CONN)
            {

                //Sets Up Temporary 
                List<IMyTerminalBlock> TEMP_D = new List<IMyTerminalBlock>();
                List<IMyTerminalBlock> ROUTE_BLOCKS = new List<IMyTerminalBlock>();

                //Retrieves And Stores First Text Panel
                GridTerminalSystem.GetBlocksOfType<IMyTextPanel>(TEMP_D, block => (block.CubeGrid == Me.CubeGrid));
                if (TEMP_D.Count == 0) { return ROUTE_BLOCKS; }
                var INITIAL_LCD = default(IMyTextPanel);
                double DIST = 5;
                foreach (var item in TEMP_D)
                {
                    var ME_DIST = (item.GetPosition() - CONN.GetPosition()).Length();
                    if (ME_DIST < DIST)
                    { INITIAL_LCD = item as IMyTextPanel; DIST = ME_DIST; }
                }

                //Logic Check To Ensure No Enpty Routes Are Output
                if (INITIAL_LCD == null) { Echo("Non Route Detected"); return new List<IMyTerminalBlock>(); }
                ROUTE_BLOCKS.Add(CONN);
                ROUTE_BLOCKS.Add(INITIAL_LCD);

                //Iterates Through Panels Until Path Is Open
                for (int i = 1; i < 7; i++)
                {
                    List<IMyTerminalBlock> TEMP_E = new List<IMyTerminalBlock>();
                    GridTerminalSystem.GetBlocksOfType<IMyTextPanel>(TEMP_E, block => Vector3D.Dot(ROUTE_BLOCKS[i].WorldMatrix.Up, Vector3D.Normalize(block.GetPosition() - ROUTE_BLOCKS[i].GetPosition())) > 0.999);
                    if (TEMP_E.Count == 0) { break; } //ends if nothing above
                    IMyTextPanel TEMP_PANEL = default(IMyTextPanel);
                    DIST = 300;
                    foreach (var item in TEMP_E)
                    {
                        var ME_DIST = (item.GetPosition() - CONN.GetPosition()).Length();
                        if (ME_DIST < DIST)
                        { TEMP_PANEL = item as IMyTextPanel; DIST = ME_DIST; }
                    }
                    ROUTE_BLOCKS.Add(TEMP_PANEL);
                }

                //Outputs Panels And Connector
                Echo("Iteration Through Panels Complete");
                return ROUTE_BLOCKS;
            }
            #endregion

            #region Dock Initialisation
            /*=======================================================================================                             
              Function: DOCK INIT    
              ---------------------------------------                            
              function will: Initialise the docking manager
            //=======================================================================================*/
            void DOCK_INIT()
            {
                //Runs Through All Docking Routes, Forms New Docking routes as terminal block lists
                List<IMyTerminalBlock> TEMP = new List<IMyTerminalBlock>();
                GridTerminalSystem.GetBlocksOfType<IMyShipConnector>(TEMP, b => b.CubeGrid == Me.CubeGrid);
                foreach (IMyTerminalBlock TERMINAL_CONNECTOR in TEMP)
                {
                    Echo("started loop");
                    List<IMyTerminalBlock> TRAIL_LIST = TRAIL_SETUP(TERMINAL_CONNECTOR as IMyShipConnector);
                    Echo("ran coord init");
                    if (TRAIL_LIST.Count > 0)
                    {
                        DOCKPOINT_INFO NEW_DOCKPT = new DOCKPOINT_INFO();
                        string entityIdStr = TERMINAL_CONNECTOR.EntityId.ToString();
                        NEW_DOCKPT.ID = "DK" + (entityIdStr.Length >= 4 ? entityIdStr.Substring(0, 4) : entityIdStr);
                        NEW_DOCKPT.BASE_TAG = MEINFO.ID;
                        NEW_DOCKPT.LAST_PING = DateTime.Now;
                        NEW_DOCKPT.ROUTE = TRAIL_LIST;
                        List<string> ROUTE_POSITIONS = new List<string>();
                        foreach (var item in NEW_DOCKPT.ROUTE)
                        { ROUTE_POSITIONS.Add(item.GetPosition() + ""); }
                        NEW_DOCKPT.OUTPUTROLL = string.Join("^", ROUTE_POSITIONS);

                        // Check if dockpoint already exists before adding
                        if (!DOCKPOINTS.ContainsKey(NEW_DOCKPT.ID))
                        {
                            DOCKPOINTS.Add(NEW_DOCKPT.ID, NEW_DOCKPT);
                        }
                        else
                        {
                            DOCKPOINTS[NEW_DOCKPT.ID] = NEW_DOCKPT; // Update existing entry
                        }
                    }
                }
            }

            #endregion

            // COPY TO HERE  

            //Some additional stuff for some other stuff to fix keens fucking bullshit
            public Program()
            {
                // Set script to run every 10 ticks for better performance
                Runtime.UpdateFrequency = UpdateFrequency.Update10;

                // Set up IGC listener for fleet communication
                Lstn = IGC.RegisterBroadcastListener(IGCTagIN);

                // Initialize the system
                Echo("Fleet Central Command Ver_008_Updated Initialized");
                Echo("IGC Communication System Active");
                Echo($"Listening on channel: {IGCTagIN}");
                Echo($"Sending on channel: {IGCTagOUT}");
            }