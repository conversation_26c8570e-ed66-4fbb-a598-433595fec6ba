string Ver = "0.9.251";

static bool WholeAirspaceLocking = false;
static long DbgIgc = 0;
static bool IsLargeGrid;
static double Dt = 1 / 60f;
static float MAX_SP = 104.38f;
const float G = 9.81f;
const string DockHostTag = "docka-min3r";
const string ForwardGyroTag = "forward-gyro";
bool ClearDocksOnReload = false;

static float StoppingPowerQuotient = 0.5f;
static bool MaxBrakeInProximity = true;
static bool MaxAccelInProximity = false;
static bool MoreRejectDampening = true;

static string LOCK_NAME_GeneralSection = "general";
//static string LOCK_NAME_ForceFinishSection = "general";
static string LOCK_NAME_ForceFinishSection = "force-finish";

Action<IMyTextPanel> outputPanelInitializer = x =>
{
	x.ContentType = ContentType.TEXT_AND_IMAGE;
};

Action<IMyTextPanel> logPanelInitializer = x =>
{
	x.ContentType = ContentType.TEXT_AND_IMAGE;
	x.FontColor = new Color(r: 0, g: 255, b: 116);
	x.FontSize = 0.65f;
};

static class Variables
{
	static Dictionary<string, object> v = new Dictionary<string, object> {
		{ "depth-limit", new Variable<float> { value = 80, parser = s => float.Parse(s) } },
		{ "max-generations", new Variable<int> { value = 7, parser = s => int.Parse(s) } },
		{ "circular-pattern-shaft-radius", new Variable<float> { value = 3.6f, parser = s => float.Parse(s) } },
		{ "echelon-offset", new Variable<float> { value = 12f, parser = s => float.Parse(s) } },
		{ "getAbove-altitude", new Variable<float> { value = 20, parser = s => float.Parse(s) } },
		{ "skip-depth", new Variable<float> { value = 0, parser = s => float.Parse(s) } },
		{ "ct-raycast-range", new Variable<float> { value = 1000, parser = s => float.Parse(s) } },
		{ "preferred-container", new Variable<string> { value = "", parser = s => s } },
		{ "group-constraint", new Variable<string> { value = "general", parser = s => s } },
		{ "logger-char-limit", new Variable<int> { value = 5000, parser = s => int.Parse(s) } },
		{ "cargo-full-factor", new Variable<float> { value = 0.8f, parser = s => float.Parse(s) } },
		{ "battery-low-factor", new Variable<float> { value = 0.2f, parser = s => float.Parse(s) } },
		{ "battery-full-factor", new Variable<float> { value = 0.8f, parser = s => float.Parse(s) } },
		{ "gas-low-factor", new Variable<float> { value = 0.2f, parser = s => float.Parse(s) } },
		{ "speed-clear", new Variable<float> { value = 2f, parser = s => float.Parse(s) } },
		{ "speed-drill", new Variable<float> { value = 0.6f, parser = s => float.Parse(s) } },
		{ "roll-power-factor", new Variable<float> { value = 1f, parser = s => float.Parse(s) } },
		// apck
		{ "ggen-tag", new Variable<string> { value = "", parser = s => s } },
		{ "hold-thrust-on-rotation", new Variable<bool> { value = true, parser = s => s == "true" } },
		{ "amp", new Variable<bool> { value = false, parser = s => s == "true" } }
	};
	public static void Set(string key, string value) { (v[key] as ISettable).Set(value); }
	public static void Set<T>(string key, T value) { (v[key] as ISettable).Set(value); }
	public static T Get<T>(string key) { return (v[key] as ISettable).Get<T>(); }
	public interface ISettable
	{
		void Set(string v);
		T1 Get<T1>();
		void Set<T1>(T1 v);
	}
	public class Variable<T> : ISettable
	{
		public T value;
		public Func<string, T> parser;
		public void Set(string v) { value = parser(v); }
		public void Set<T1>(T1 v) { value = (T)(object)v; }
		public T1 Get<T1>() { return (T1)(object)value; }
	}
}
class λ{static λ ſ;λ(){}Action<string>κ;Dictionary<string,bool>ι;λ(Dictionary<string,bool>θ,Action<string>μ){κ=μ;ι=θ;}
public static λ ƅ=>ſ;public static void ģ(Dictionary<string,bool>θ,Action<string>μ){if(ſ==null)ſ=new λ(θ,μ);}public void ω(
string ğ,bool ψ){if(ι[ğ]!=ψ)χ(ğ);}public void χ(string ğ){ι[ğ]=!ι[ğ];κ(ğ);}public bool φ(string ğ){return ι[ğ];}public
ImmutableArray<MyTuple<string,string>>υ(){return ι.Select(Ť=>new MyTuple<string,string>("Toggle "+Ť.Key+(Ť.Value?" (off)":" (on)"),
"toggle:"+Ť.Key)).ToImmutableArray();}}bool ϊ;σ τ;class σ{Dictionary<string,Action<string[]>>ς;public σ(Dictionary<string,Action<
string[]>>ς){this.ς=ς;}public void ρ(string Ȇ,string[]π){this.ς[Ȇ].Invoke(π);}}static int ο;void ξ(string ν){ο++;Echo(
"Run count: "+ο);if(ϊ&&string.IsNullOrEmpty(ν)){ϊ=false;ν=string.Join(",",Me.CustomData.Trim('\n').Split(new[]{'\n'},
StringSplitOptions.RemoveEmptyEntries).Where(ƃ=>!ƃ.StartsWith("//")).Select(ƃ=>"["+ƃ+"]"));}if(!string.IsNullOrEmpty(ν)&&ν.Contains(":")){
var ς=ν.Split(new[]{"],["},StringSplitOptions.RemoveEmptyEntries).Select(ƃ=>ƃ.Trim('[',']')).ToList();foreach(var á in ς){
string[]π=á.Split(new[]{':'},StringSplitOptions.RemoveEmptyEntries);if(π[0]=="command"){try{this.τ.ρ(π[1],π);}catch(Exception
ex){Ψ($"Run command '{π[1]}' failed.\n{ex}");}}if(π[0]=="toggle"){λ.ƅ.χ(π[1]);Ψ(
$"Switching '{π[1]}' to state '{λ.ƅ.φ(π[1])}'");}}}}void Ƃ(){ƀ.ƅ.Ƙ();Р.Ƃ();}IMyProgrammableBlock ϓ;void ϒ(){if(!string.IsNullOrEmpty(Me.CustomData))ϊ=true;Р.ģ(Echo,
GridTerminalSystem,Me);λ.ģ(new Dictionary<string,bool>{{"adaptive-mining",false},{"adjust-entry-by-elevation",true},{"log-message",false},
{"show-pstate",false},{"suppress-transition-control",false},{"suppress-gyro-control",false},{"damp-when-idle",true},{
"ignore-user-thruster",false},{"cc",true}},ğ=>{switch(ğ){case"log-message":var ϑ=є?.Ҟ;if(ϑ!=null)ϑ.CustomData="";break;}});ě.Add("docking",new
Ĕ(1,"docking"));stateWrapper=new StateWrapper(ƃ=>Storage=ƃ);if(!stateWrapper.TryLoad(Storage)){Р.Ƌ(
"State load failed, clearing Storage now");stateWrapper.Save();Runtime.UpdateFrequency=UpdateFrequency.None;}GridTerminalSystem.GetBlocksOfType(Ͱ,á=>á.
IsSameConstructAs(Me));Ͱ.ForEach(á=>á.EnableRaycast=true);IsLargeGrid=Me.CubeGrid.GridSizeEnum==MyCubeSize.Large;this.τ=new σ(new
Dictionary<string,Action<string[]>>{{"set-value",(ˀ)=>Variables.Set(ˀ[2],ˀ[3])},{"add-panel",(ˀ)=>{List<IMyTextPanel>â=new List<
IMyTextPanel>();GridTerminalSystem.GetBlocksOfType(â,é=>é.IsSameConstructAs(Me)&&é.CustomName.Contains(ˀ[2]));var ë=â.FirstOrDefault
();if(ë!=null){Р.Ƈ($"Added {ë.CustomName} as GUI panel");outputPanelInitializer(ë);Ч=ë;}}},{"add-gui-controller",(ˀ)=>{
List<IMyShipController>â=new List<IMyShipController>();GridTerminalSystem.GetBlocksOfType(â,é=>é.IsSameConstructAs(Me)&&é.
CustomName.Contains(ˀ[2]));Х=â.FirstOrDefault();if(Х!=null)Р.Ƈ($"Added {Х.CustomName} as GUI controller");}},{"add-logger",(ˀ)=>{
List<IMyTextPanel>â=new List<IMyTextPanel>();GridTerminalSystem.GetBlocksOfType(â,é=>é.IsSameConstructAs(Me)&&é.CustomName.
Contains(ˀ[2]));var ë=â.FirstOrDefault();if(ë!=null){logPanelInitializer(ë);Р.Ƅ(ë);Р.Ƈ("Added logger: "+ë.CustomName);}}},{
"create-task",(ˀ)=>є?.Μ()},{"mine",(ˀ)=>є?.Ҷ()},{"skip",(ˀ)=>є?.ҵ()},{"set-role",(ˀ)=>Ϙ(ˀ[2])},{"low-update-rate",(ˀ)=>Runtime.
UpdateFrequency=UpdateFrequency.Update10},{"create-task-raycast",(ˀ)=>ͷ(ˀ)},{"create-task-gps",(ˀ)=>ˇ(ˀ)},{"force-finish",(ˀ)=>є?.Ҩ()},
{"recall",(ˀ)=>ˡ?.Ι()},{"static-dock",(ˀ)=>є?.Ҵ(ˀ)},{"set-state",(ˀ)=>є?.Ĉ(ˀ[2])},{"halt",(ˀ)=>є?.ј()},{
"clear-storage-state",(ˀ)=>stateWrapper?.ClearPersistentState()},{"save",(ˀ)=>stateWrapper?.Save()},{"static-dock-gps",(ˀ)=>{if((є!=null)&&(є
.Ҟ!=null)){є.Ҟ.CustomData="GPS:static-dock:"+(stateWrapper.PState.StaticDockOverride.HasValue?ђ.т(stateWrapper.PState.
StaticDockOverride.Value):"-")+":";}}},{"dispatch",(ˀ)=>є?.ў()},{"global",(ˀ)=>{var π=ˀ.Skip(2).ToArray();IGC.SendBroadcastMessage(
"miners.command",string.Join(":",π),TransmissionDistance.TransmissionDistanceMax);Ψ("broadcasting global "+string.Join(":",π));τ.ρ(π[1],
π);}},{"get-toggles",(ˀ)=>{IGC.SendUnicastMessage(long.Parse(ˀ[2]),
$"menucommand.get-commands.reply:{string.Join(":",ˀ.Take(3))}",λ.ƅ.υ());}},});}void Ϙ(string ϗ){Role ϖ;if(Enum.TryParse(ϗ,out ϖ)){CurrentRole=ϖ;Р.Ƈ("Assigned role: "+ϖ);if(ϖ==Role.
Dispatcher){ˡ=new ˠ(IGC,stateWrapper);var ϕ=new List<IMyShipConnector>();GridTerminalSystem.GetBlocksOfType(ϕ,á=>á.
IsSameConstructAs(Me)&&á.CustomName.Contains(DockHostTag));if(ClearDocksOnReload)ϕ.ForEach(ƭ=>ƭ.CustomData="");ʫ=new ʨ(ϕ,stateWrapper.
PState,GridTerminalSystem);if(stateWrapper.PState.ShaftStates.Count>0){var ϔ=stateWrapper.PState.ShaftStates;ˡ.Μ(stateWrapper.
PState.shaftRadius.Value,stateWrapper.PState.corePoint.Value,stateWrapper.PState.miningPlaneNormal.Value,stateWrapper.PState.
MaxGenerations,stateWrapper.PState.CurrentTaskGroup);for(int Ť=0;Ť<ˡ.Η.ΐ.Count;Ť++){ˡ.Η.ΐ[Ť].ß=(ShaftState)ϔ[Ť];}stateWrapper.PState.
ShaftStates=ˡ.Η.ΐ.Select(é=>(byte)é.ß).ToList();Р.Ƈ($"Restored task from pstate, shaft count: {ϔ.Count}");}ό("miners",
"dispatcher-change");}else{var â=new List<IMyProgrammableBlock>();GridTerminalSystem.GetBlocksOfType(â,ϐ=>ϐ.CustomName.Contains("core")&&ϐ.
IsSameConstructAs(Me)&&ϐ.Enabled);ϓ=â.FirstOrDefault();є=new Ѭ(ϖ,GridTerminalSystem,IGC,stateWrapper,Ŏ,Me);if(ϓ!=null){є.њ(ϓ);}else{Ɩ=new
ƕ(Me,stateWrapper.PState,GridTerminalSystem,IGC,Ŏ);є.њ(Ɩ);є.Ѿ=new σ(new Dictionary<string,Action<string[]>>{{"create-wp",
(ˀ)=>ǉ(ˀ)},{"set-sp-limit",(ˀ)=>{if(Ɩ.Ƒ?.đ=="Deserialized Behavior")Ɩ.Ƒ.ð=float.Parse(ˀ[2]);}},{"pillock-mode",(ˀ)=>Ɩ?.Ĉ(
ˀ[2])},{"request-docking",(ˀ)=>{Р.Ƈ("Embedded lone mode is not supported");}},{"request-depart",(ˀ)=>{Р.Ƈ(
"Embedded lone mode is not supported");}}});}if(!string.IsNullOrEmpty(stateWrapper.PState.lastAPckCommand)){ƀ.ƅ.ƞ(5000).Ɯ(()=>є.ѽ(stateWrapper.PState.
lastAPckCommand));}if(ϖ==Role.Lone){є.Ѫ=new ˠ(IGC,stateWrapper);}if(ϖ==Role.Agent){ƀ.ƅ.ƚ(()=>!є.ѧ.HasValue).ƞ(1000).Ɯ(()=>ό(
"miners.handshake",Variables.Get<string>("group-constraint")));}if(stateWrapper.PState.miningEntryPoint.HasValue){є.Ҧ();}}}}static void ώ<
Ŧ>(Ŧ ύ,IList<Ŧ>á)where Ŧ:class{if((ύ!=null)&&!á.Contains(ύ))á.Add(ύ);}void ό<Ŧ>(string ŵ,Ŧ Τ){var ϋ=IGC.
RegisterBroadcastListener(ŵ);IGC.SendBroadcastMessage(ϋ.Tag,Τ,TransmissionDistance.TransmissionDistanceMax);}void Ψ(string ˋ){Р.Ƈ(ˋ);}
public enum MinerState : byte
{
	Disabled = 0, Idle, GoingToEntry, Drilling, GettingOutTheShaft, GoingToUnload, WaitingForDocking,
	Docking, ReturningToShaft, WaitingForLockInShaft, ChangingShaft, Maintenance, ForceFinish
}

public enum ShaftState { Planned, InProgress, Complete, Cancelled }

Role CurrentRole;
public enum Role : byte { None = 0, Dispatcher, Agent, Lone }

public enum ApckState
{
	Inert, Standby, Formation, DockingAwait, DockingFinal, Brake, CwpTask
}

StateWrapper stateWrapper;
public class StateWrapper
{
	public PersistentState PState { get; private set; }

	public void ClearPersistentState()
	{
		var currentState = PState;
		PState = new PersistentState();
		PState.StaticDockOverride = currentState.StaticDockOverride;
		PState.LifetimeAcceptedTasks = currentState.LifetimeAcceptedTasks;
		PState.LifetimeOperationTime = currentState.LifetimeOperationTime;
		PState.LifetimeWentToMaintenance = currentState.LifetimeWentToMaintenance;
		PState.LifetimeOreAmount = currentState.LifetimeOreAmount;
		PState.LifetimeYield = currentState.LifetimeYield;
	}

	Action<string> stateSaver;
	public StateWrapper(Action<string> stateSaver)
	{
		this.stateSaver = stateSaver;
	}

	public void Save()
	{
		try
		{
			PState.Save(stateSaver);
		}
		catch (Exception ex)
		{
			Р.Ƈ("State save failed.");
			Р.Ƈ(ex.ToString());
		}
	}

	public bool TryLoad(string serialized)
	{
		PState = new PersistentState();
		try
		{
			PState.Load(serialized);
			return true;
		}
		catch (Exception ex)
		{
			Р.Ƈ("State load failed.");
			Р.Ƈ(ex.ToString());
		}
		return false;
	}
}

public class PersistentState
{
	public int LifetimeOperationTime = 0;
	public int LifetimeAcceptedTasks = 0;
	public int LifetimeWentToMaintenance = 0;
	public float LifetimeOreAmount = 0;
	public float LifetimeYield = 0;

	// cleared by specific command
	public Vector3D? StaticDockOverride { get; set; }

	// cleared by clear-storage-state (task-dependent)
	public MinerState MinerState = MinerState.Idle;
	public Vector3D? miningPlaneNormal;
	public Vector3D? getAbovePt;
	public Vector3D? miningEntryPoint;
	public Vector3D? corePoint;
	public float? shaftRadius;

	public float? maxDepth;
	public Vector3D? currentWp;
	public float? skipDepth;

	public float? lastFoundOreDepth;
	public float CurrentJobMaxShaftYield;

	public float? minFoundOreDepth;
	public float? maxFoundOreDepth;
	public float? prevTickValCount = 0;

	public int? CurrentShaftId;
	public List<byte> ShaftStates = new List<byte>();
	public int MaxGenerations;
	public string CurrentTaskGroup;

	public string lastAPckCommand;
	// banned directions?

	T ParseValue<T>(Dictionary<string, string> values, string key)
	{
		string res;
		if (values.TryGetValue(key, out res) && !string.IsNullOrEmpty(res))
		{
			if (typeof(T) == typeof(String))
				return (T)(object)res;
			else if (typeof(T) == typeof(int))
				return (T)(object)int.Parse(res);
			else if (typeof(T) == typeof(int?))
				return (T)(object)int.Parse(res);
			else if (typeof(T) == typeof(float))
				return (T)(object)float.Parse(res);
			else if (typeof(T) == typeof(float?))
				return (T)(object)float.Parse(res);
			else if (typeof(T) == typeof(long?))
				return (T)(object)long.Parse(res);
			else if (typeof(T) == typeof(Vector3D?))
			{
				var d = res.Split(':');
				return (T)(object)new Vector3D(double.Parse(d[0]), double.Parse(d[1]), double.Parse(d[2]));
			}
			else if (typeof(T) == typeof(List<byte>))
			{
				var d = res.Split(':');
				return (T)(object)d.Select(x => byte.Parse(x)).ToList();
			}
			else if (typeof(T) == typeof(MinerState))
			{
				return (T)Enum.Parse(typeof(MinerState), res);
			}
		}
		return default(T);
	}

	public PersistentState Load(string storage)
	{
		if (!string.IsNullOrEmpty(storage))
		{
			Р.Ƌ(storage);

			var values = storage.Split('\n').ToDictionary(s => s.Split('=')[0], s => string.Join("=", s.Split('=').Skip(1)));

			LifetimeAcceptedTasks = ParseValue<int>(values, "LifetimeAcceptedTasks");
			LifetimeOperationTime = ParseValue<int>(values, "LifetimeOperationTime");
			LifetimeWentToMaintenance = ParseValue<int>(values, "LifetimeWentToMaintenance");
			LifetimeOreAmount = ParseValue<float>(values, "LifetimeOreAmount");
			LifetimeYield = ParseValue<float>(values, "LifetimeYield");

			StaticDockOverride = ParseValue<Vector3D?>(values, "StaticDockOverride");
			MinerState = ParseValue<MinerState>(values, "MinerState");
			miningPlaneNormal = ParseValue<Vector3D?>(values, "miningPlaneNormal");
			getAbovePt = ParseValue<Vector3D?>(values, "getAbovePt");
			miningEntryPoint = ParseValue<Vector3D?>(values, "miningEntryPoint");
			corePoint = ParseValue<Vector3D?>(values, "corePoint");
			shaftRadius = ParseValue<float?>(values, "shaftRadius");

			maxDepth = ParseValue<float?>(values, "maxDepth");
			currentWp = ParseValue<Vector3D?>(values, "currentWp");
			skipDepth = ParseValue<float?>(values, "skipDepth");

			lastFoundOreDepth = ParseValue<float?>(values, "lastFoundOreDepth");
			CurrentJobMaxShaftYield = ParseValue<float>(values, "CurrentJobMaxShaftYield");

			minFoundOreDepth = ParseValue<float?>(values, "minFoundOreDepth");
			maxFoundOreDepth = ParseValue<float?>(values, "maxFoundOreDepth");

			CurrentShaftId = ParseValue<int?>(values, "CurrentShaftId");
			MaxGenerations = ParseValue<int>(values, "MaxGenerations");
			CurrentTaskGroup = ParseValue<string>(values, "CurrentTaskGroup");

			lastAPckCommand = ParseValue<string>(values, "lastAPckCommand");

			ShaftStates = ParseValue<List<byte>>(values, "ShaftStates") ?? new List<byte>();
		}
		return this;
	}

	public void Save(Action<string> store)
	{
		store(Serialize());
	}

	string Serialize()
	{
		string[] pairs = new string[]
		{
			"LifetimeAcceptedTasks=" + LifetimeAcceptedTasks,
			"LifetimeOperationTime=" + LifetimeOperationTime,
			"LifetimeWentToMaintenance=" + LifetimeWentToMaintenance,
			"LifetimeOreAmount=" + LifetimeOreAmount,
			"LifetimeYield=" + LifetimeYield,
			"StaticDockOverride=" + (StaticDockOverride.HasValue ? ђ.т(StaticDockOverride.Value) : ""),
			"MinerState=" + MinerState,
			"miningPlaneNormal=" + (miningPlaneNormal.HasValue ? ђ.т(miningPlaneNormal.Value) : ""),
			"getAbovePt=" + (getAbovePt.HasValue ? ђ.т(getAbovePt.Value) : ""),
			"miningEntryPoint=" + (miningEntryPoint.HasValue ? ђ.т(miningEntryPoint.Value) : ""),
			"corePoint=" + (corePoint.HasValue ? ђ.т(corePoint.Value) : ""),
			"shaftRadius=" + shaftRadius,
			"maxDepth=" + maxDepth,
			"currentWp=" +  (currentWp.HasValue ? ђ.т(currentWp.Value) : ""),
			"skipDepth=" + skipDepth,
			"lastFoundOreDepth=" + lastFoundOreDepth,
			"CurrentJobMaxShaftYield=" + CurrentJobMaxShaftYield,
			"minFoundOreDepth=" + minFoundOreDepth,
			"maxFoundOreDepth=" + maxFoundOreDepth,
			"CurrentShaftId=" + CurrentShaftId ?? "",
			"MaxGenerations=" + MaxGenerations,
			"CurrentTaskGroup=" + CurrentTaskGroup,
			"ShaftStates=" + string.Join(":", ShaftStates),
			"lastAPckCommand=" + lastAPckCommand
		};
		return string.Join("\n", pairs);
	}

	public override string ToString()
	{
		return Serialize();
	}
}
void Save(){stateWrapper.Save();}Program(){Runtime.UpdateFrequency=UpdateFrequency.Update1;ϒ();}List<MyIGCMessage>ˏ=new
List<MyIGCMessage>();void Main(string ˎ,UpdateType ˍ){ˏ.Clear();while(IGC.UnicastListener.HasPendingMessage){ˏ.Add(IGC.
UnicastListener.AcceptMessage());}var ˌ=IGC.RegisterBroadcastListener("miners.command");if(ˌ.HasPendingMessage){var ˋ=ˌ.AcceptMessage()
;ˎ=ˋ.Data.ToString();Ψ("Got miners.command: "+ˎ);}ξ(ˎ);foreach(var ȉ in ˏ){if(ȉ.Tag=="apck.ntv.update"){var ĥ=(MyTuple<
MyTuple<string,long,long,byte,byte>,Vector3D,Vector3D,MatrixD,BoundingBoxD>)ȉ.Data;var ã=ĥ.Item1.Item1;Ġ(ã,ĥ);if(є?.ћ!=null){
IGC.SendUnicastMessage(є.ћ.EntityId,"apck.ntv.update",ĥ);}}else if(ȉ.Tag=="apck.depart.complete"){ʫ?.ȇ(ȉ.Source.ToString())
;}else if(ȉ.Tag=="apck.depart.request"){ʫ.ȅ(ȉ.Source,(Vector3D)ȉ.Data,true);}else if(ȉ.Tag=="apck.docking.request"){ʫ.ȅ(ȉ
.Source,(Vector3D)ȉ.Data);}else if(ȉ.Tag=="apck.depart.complete"){if(є?.ѧ!=null)IGC.SendUnicastMessage(є.ѧ.Value,
"apck.depart.complete","");}else if(ȉ.Tag=="apck.docking.approach"||ȉ.Tag=="apck.depart.approach"){if(є?.ћ!=null){IGC.SendUnicastMessage(є.ћ.
EntityId,ȉ.Tag,(ImmutableArray<Vector3D>)ȉ.Data);}else{if(ȉ.Tag.Contains("depart")){var ţ=new ɻ("fin",Ɩ.Ƒ);ţ.ɸ=1;ţ.ʹ=()=>IGC.
SendUnicastMessage(ȉ.Source,"apck.depart.complete","");Ɩ.ǉ(ţ);}Ɩ.Ɣ.Disconnect();var ˊ=(ImmutableArray<Vector3D>)ȉ.Data;if(ˊ.Length>0){
foreach(var ë in ˊ){Func<Vector3D>ʪ=()=>Vector3D.Transform(ë,Ŏ("docking").č.Value);var D=new ƻ(){đ="r",î=true,ƹ=é=>ʪ(),ư=()=>Ɩ.
Ɣ.GetPosition(),Ƴ=Ʋ=>Ɩ.Ɣ.GetPosition()-Ŏ("docking").č.Value.Forward*10000,Ʊ=()=>Ɩ.Ɣ.WorldMatrix};Ɩ.ǉ(ɻ.ʴ("r",ʪ,D));}}}}}Р
.Ƌ($"Version: {Ver}");Р.Ƌ("Min3r role: "+CurrentRole);if(CurrentRole==Role.Dispatcher){Р.Ƌ(ˡ.ToString());ˡ.ά(ˏ);if(ș!=
null){foreach(var ƃ in ˡ.ˑ)IGC.SendUnicastMessage(ƃ.Ȩ,"report.request","");}ș?.ɦ(ˡ);ʫ.ǈ(IGC,ο);if(Ч!=null){if(Х!=null){if(ș
==null){ș=new Ș(Ч,ˡ,stateWrapper);ˡ.ͽ=ș.ɉ;ș.ɗ=Ȇ=>ˡ.Ϗ(Ȇ);if(ˡ.Η!=null)ˡ.ͽ.Invoke(ˡ.Η);}else ș.ǈ(Ч,Х);}}}else if((
CurrentRole==Role.Agent)||(CurrentRole==Role.Lone)){є.ǈ(ˏ);Р.Ƌ("Min3r state: "+є.ѳ());Р.Ƌ("Static dock override: "+(stateWrapper.
PState.StaticDockOverride.HasValue?"ON":"OFF"));Р.Ƌ("Dispatcher: "+є.ѧ);Р.Ƌ("Echelon: "+є.Ϋ);Р.Ƌ("HoldingLock: "+є.έ);Р.Ƌ(
"WaitedSection: "+є.Ѧ);Р.Ƌ($"Estimated shaft radius: {Variables.Get<float>("circular-pattern-shaft-radius"):f2}");Р.Ƌ(
"LifetimeAcceptedTasks: "+stateWrapper.PState.LifetimeAcceptedTasks);Р.Ƌ("LifetimeOreAmount: "+У(stateWrapper.PState.LifetimeOreAmount));Р.Ƌ(
"LifetimeOperationTime: "+TimeSpan.FromSeconds(stateWrapper.PState.LifetimeOperationTime).ToString());Р.Ƌ("LifetimeWentToMaintenance: "+
stateWrapper.PState.LifetimeWentToMaintenance);if(Ɩ!=null){if(Ɩ.Š.M!=Vector3D.Zero)Ȼ("agent-dest",Ɩ.Š.M,"");if(Ɩ.Š.K!=Vector3D.Zero)
Ȼ("agent-vel",Ɩ.Š.K,Ɩ.Š.ø);}if(Ч!=null){Ш($"Version: {Ver}");Ш(
$"LifetimeAcceptedTasks: {stateWrapper.PState.LifetimeAcceptedTasks}");Ш($"LifetimeOreAmount: {У(stateWrapper.PState.LifetimeOreAmount)}");Ш(
$"LifetimeOperationTime: {TimeSpan.FromSeconds(stateWrapper.PState.LifetimeOperationTime)}");Ш($"LifetimeWentToMaintenance: {stateWrapper.PState.LifetimeWentToMaintenance}");Ш("\n");Ш(
$"CurrentJobMaxShaftYield: {У(stateWrapper.PState.CurrentJobMaxShaftYield)}");Ш($"CurrentShaftYield: "+є?.ѩ?.ϰ());Ш(є?.ѩ?.ToString());Ф();}}if(λ.ƅ.φ("show-pstate"))Р.Ƌ(stateWrapper.PState.ToString
());Ƃ();ĝ();if(DbgIgc!=0)Ⱥ(DbgIgc);Dt=Math.Max(0.001,Runtime.TimeSinceLastRun.TotalSeconds);Р.Ŧ+=Dt;ˈ=Math.Max(ˈ,Runtime.
CurrentInstructionCount);Р.Ƌ($"InstructionCount (Max): {Runtime.CurrentInstructionCount} ({ˈ})");Р.Ƌ(
$"Processed in {Runtime.LastRunTimeMs:f3} ms");}int ˈ;void ˇ(string[]ˆ){if(ˡ!=null){var ʼ=ˆ.Skip(2).ToArray();var ę=new Vector3D(double.Parse(ʼ[0]),double.Parse(ʼ[1]
),double.Parse(ʼ[2]));Vector3D Ť;if(ʼ.Length>3){Ť=Vector3D.Normalize(ę-new Vector3D(double.Parse(ʼ[3]),double.Parse(ʼ[4])
,double.Parse(ʼ[5])));}else{if(Х==null){Р.Ƈ(
"WARNING: the normal was not supplied and there is no Control Station available to check if we are in gravity");Ť=-ʫ.Ǿ();Р.Ƈ("Using 'first dock connector Backward' as a normal");}else{Vector3D ˉ;if(Х.TryGetPlanetPosition(out ˉ)){Ť
=Vector3D.Normalize(ˉ-ę);Р.Ƈ("Using mining-center-to-planet-center direction as a normal because we are in gravity");}
else{Ť=-ʫ.Ǿ();Р.Ƈ("Using 'first dock connector Backward' as a normal");}}}var á=Variables.Get<string>("group-constraint");if
(!string.IsNullOrEmpty(á)){ˡ.Μ(Variables.Get<float>("circular-pattern-shaft-radius"),ę,Ť,Variables.Get<int>(
"max-generations"),á);ˡ.ΰ(á);}else Р.Ƈ(
"To use this mode specify group-constraint value and make sure you have intended circular-pattern-shaft-radius");}else Р.Ƈ("GPStaskHandler is intended for Dispatcher role");}List<IMyCameraBlock>Ͱ=new List<IMyCameraBlock>();Vector3D
?ͼ;Vector3D?ͺ;void ͷ(string[]ˆ){var Ͷ=Ͱ.Where(á=>á.IsActive).FirstOrDefault();if(Ͷ!=null){Ͷ.CustomData="";var ę=Ͷ.
GetPosition()+Ͷ.WorldMatrix.Forward*Variables.Get<float>("ct-raycast-range");Ͷ.CustomData+="GPS:dir0:"+ђ.т(ę)+":\n";Ψ(
$"RaycastTaskHandler tries to raycast point GPS:create-task base point:{ђ.т(ę)}:");if(Ͷ.CanScan(ę)){var ʹ=Ͷ.Raycast(ę);if(!ʹ.IsEmpty()){ͼ=ʹ.HitPosition.Value;Ψ(
$"GPS:Raycasted base point:{ђ.т(ʹ.HitPosition.Value)}:");Ͷ.CustomData+="GPS:castedSurfacePoint:"+ђ.т(ͼ.Value)+":\n";IMyShipController ͳ=є?.Ă??Х;Vector3D ˉ;if((ͳ!=null)&&ͳ.
TryGetPlanetPosition(out ˉ)){ͺ=Vector3D.Normalize(ˉ-ͼ.Value);Р.Ƈ(
"Using mining-center-to-planet-center direction as a normal because we are in gravity");}else{var Ͳ=ͼ.Value-Ͷ.GetPosition();var ͻ=Vector3D.Normalize(Vector3D.CalculatePerpendicularVector(Ͳ));var ͱ=ͼ.Value+ͻ
*Math.Min(10,Ͳ.Length());var ˮ=ͼ.Value+Vector3D.Normalize(Vector3D.Cross(ͻ,Ͳ))*Math.Min(20,Ͳ.Length());var ˬ=ͱ+Vector3D.
Normalize(ͱ-Ͷ.GetPosition())*500;var ˤ=ˮ+Vector3D.Normalize(ˮ-Ͷ.GetPosition())*500;Ͷ.CustomData+="GPS:target1:"+ђ.т(ˬ)+":\n";if(Ͷ
.CanScan(ˬ)){var ˣ=Ͷ.Raycast(ˬ);if(!ˣ.IsEmpty()){Ψ($"GPS:Raycasted aux point 1:{ђ.т(ˣ.HitPosition.Value)}:");Ͷ.CustomData
+="GPS:cast1:"+ђ.т(ˣ.HitPosition.Value)+":\n";Ͷ.CustomData+="GPS:target2:"+ђ.т(ˤ)+":\n";if(Ͷ.CanScan(ˤ)){var ˢ=Ͷ.Raycast(ˤ
);if(!ˢ.IsEmpty()){Ψ($"GPS:Raycasted aux point 2:{ђ.т(ˢ.HitPosition.Value)}:");Ͷ.CustomData+="GPS:cast2:"+ђ.т(ˢ.
HitPosition.Value)+":";ͺ=-Vector3D.Normalize(Vector3D.Cross(ˣ.HitPosition.Value-ͼ.Value,ˢ.HitPosition.Value-ͼ.Value));}}}}}if(ͺ.
HasValue&&ͼ.HasValue){Р.Ƈ("Successfully got mining center and mining normal");if(ˡ!=null){var á=Variables.Get<string>(
"group-constraint");if(!string.IsNullOrEmpty(á)){ˡ.ΰ(á);ˡ.Μ(Variables.Get<float>("circular-pattern-shaft-radius"),ͼ.Value-ͺ.Value*10,ͺ.
Value,Variables.Get<int>("max-generations"),á);}else Ψ(
"To use this mode specify group-constraint value and make sure you have intended circular-pattern-shaft-radius");}else if(є!=null){if(є.Ѫ!=null){ˡ.Μ(Variables.Get<float>("circular-pattern-shaft-radius"),ͼ.Value-ͺ.Value*10,ͺ.Value,
Variables.Get<int>("max-generations"),"LocalDispatcher");є.Ҷ();}else if(є.ѧ.HasValue)IGC.SendUnicastMessage(є.ѧ.Value,
"create-task",new MyTuple<float,Vector3D,Vector3D>(Variables.Get<float>("circular-pattern-shaft-radius"),ͼ.Value-ͺ.Value*10,ͺ.Value))
;}}else{Р.Ƈ($"RaycastTaskHandler failed to get castedNormal or castedSurfacePoint");}}}else{Р.Ƈ($"RaycastTaskHandler couldn't raycast initial position. Camera '{Ͷ.CustomName}' had {Ͷ.AvailableScanRange} AvailableScanRange"
);}}else{throw new Exception($"No active cam, {Ͱ.Count} known");}}ˠ ˡ;class ˠ{public List<Ά>ˑ=new List<Ά>();Dictionary<
string,Queue<long>>ː=new Dictionary<string,Queue<long>>();public Action<Ζ>ͽ;public class Ά{public long Ȩ;public string έ;
public float Ϋ;public string Ϊ;public Ȳ Ω;}IMyIntergridCommunicationSystem X;StateWrapper ȑ;public ˠ(
IMyIntergridCommunicationSystem Ā,StateWrapper ȑ){X=Ā;this.ȑ=ȑ;}void Ψ(string ˋ){Р.Ƈ(ˋ);}public void ά(List<MyIGCMessage>ˏ){var Χ=X.
RegisterBroadcastListener("miners");while(Χ.HasPendingMessage){var ˋ=Χ.AcceptMessage();if(ˋ.Data!=null){if(ˋ.Data.ToString().Contains(
"common-airspace-ask-for-lock")){var Φ=ˋ.Data.ToString().Split(':')[1];if(!ˑ.Any(ƃ=>ƃ.έ==Φ&&ƃ.Ȩ!=ˋ.Source)){ˑ.First(ƃ=>ƃ.Ȩ==ˋ.Source).έ=Φ;X.
SendUnicastMessage(ˋ.Source,"miners","common-airspace-lock-granted:"+Φ);Ψ(Φ+" granted to "+ˋ.Source);}else{if(!ː.ContainsKey(Φ))ː.Add(Φ,
new Queue<long>());if(!ː[Φ].Contains(ˋ.Source))ː[Φ].Enqueue(ˋ.Source);Ψ(
"commonSpaceLockOwner rejected, added to requests queue: "+ˋ.Source);}}if(ˋ.Data.ToString().Contains("common-airspace-lock-released")){var Φ=ˋ.Data.ToString().Split(':')[1];Ψ(
"(Dispatcher) received lock-released notification "+Φ+" from "+ˋ.Source);ˑ.Single(ƃ=>ƃ.Ȩ==ˋ.Source).έ="";if(ː.ContainsKey(Φ)&&(ː[Φ].Count>0)){var Ȇ=ː[Φ].Dequeue();X.
SendUnicastMessage(Ȇ,"miners","common-airspace-lock-granted:"+Φ);ˑ.First(ƃ=>ƃ.Ȩ==Ȇ).έ=Φ;Ψ(Φ+" common-airspace-lock-granted to "+Ȇ);}}}}var
Υ=X.RegisterBroadcastListener("miners.handshake");while(Υ.HasPendingMessage){var ˋ=Υ.AcceptMessage();if(ˋ.Data is string)
{var Τ=(string)ˋ.Data;Ψ($"Initiated handshake by {ˋ.Source}, group tag: {Τ}");Ά ή;if(!ˑ.Any(ƃ=>ƃ.Ȩ==ˋ.Source)){ή=new Ά{Ȩ=
ˋ.Source,Ϋ=(ˑ.Count+1)*Variables.Get<float>("echelon-offset")+10f,Ϊ=Τ};ˑ.Add(ή);ή.Ω=new Ȳ(){Ȩ=ή.Ȩ,ȩ=Color.White};}else{ή=
ˑ.Single(ƃ=>ƃ.Ȩ==ˋ.Source);ή.Ϊ=Τ;}X.SendUnicastMessage(ˋ.Source,"miners.handshake.reply",X.Me);X.SendUnicastMessage(ˋ.
Source,"miners.echelon",ή.Ϋ);if(ȑ.PState.miningPlaneNormal.HasValue){X.SendUnicastMessage(ˋ.Source,"miners.normal",ȑ.PState.
miningPlaneNormal.Value);}var η=new string[]{"skip-depth","depth-limit","getAbove-altitude"};ƀ.ƅ.ƞ(500).Ɯ(()=>{foreach(var Ĝ in η){Ψ(
$"Propagating set-value:'{Ĝ}' to {ˋ.Source}");X.SendUnicastMessage(ˋ.Source,"set-value",$"{Ĝ}:{Variables.Get<float>(Ĝ)}");}});}}var ζ=X.RegisterBroadcastListener(
"miners.report");while(ζ.HasPendingMessage){var ˋ=ζ.AcceptMessage();var Τ=(MyTuple<long,MatrixD,Vector4,ImmutableArray<MyTuple<string,
string>>>)ˋ.Data;var ε=ˑ.FirstOrDefault(ƃ=>ƃ.Ȩ==ˋ.Source);if(ε!=null)ε.Ω.Ȧ(Τ);}foreach(var ˋ in ˏ){if(ˋ.Tag=="create-task"){
var Τ=(MyTuple<float,Vector3D,Vector3D>)ˋ.Data;Ψ("Got new mining task from agent");var ε=ˑ.First(ƃ=>ƃ.Ȩ==ˋ.Source);ε.έ=
LOCK_NAME_GeneralSection;Μ(Τ.Item1,Τ.Item2,Τ.Item3,Variables.Get<int>("max-generations"),ε.Ϊ);ΰ(ε.Ϊ);}if(ˋ.Tag.Contains("request-new")){if(ˋ.Tag
=="shaft-complete-request-new"){ѯ((int)ˋ.Data);Р.Ƈ($"Shaft {ˋ.Data} complete");}Vector3D?Π=Vector3D.Zero;Vector3D?δ=
Vector3D.Zero;int γ=0;if((Η!=null)&&ѭ(ref Π,ref δ,ref γ)){X.SendUnicastMessage(ˋ.Source,"miners.assign-shaft",new MyTuple<int,
Vector3D,Vector3D>(γ,Π.Value,δ.Value));Р.Ƈ($"AssignNewShaft with id {γ} sent");}else{X.SendUnicastMessage(ˋ.Source,"command",
"force-finish");}}if(ˋ.Tag=="ban-direction"){Ѯ((int)ˋ.Data);}}foreach(var ƃ in ˑ){Р.Ƌ(ƃ.Ȩ+": echelon = "+ƃ.Ϋ+" lock: "+ƃ.έ);}}public
void β(){var Ŵ=ȑ.PState.CurrentTaskGroup;if(!string.IsNullOrEmpty(Ŵ)){Ψ($"Broadcasting task resume for mining group '{Ŵ}'");
foreach(var ƃ in ˑ.Where(é=>é.Ϊ==Ŵ)){X.SendUnicastMessage(ƃ.Ȩ,"miners.resume",ȑ.PState.miningPlaneNormal.Value);}}}public void
α(){Ψ($"Broadcasting global Halt & Clear state");X.SendBroadcastMessage("miners.command","command:halt");}public void ΰ(
string ί){Ψ($"Preparing start for mining group '{ί}'");X.SendBroadcastMessage("miners.command","command:clear-storage-state");
ƀ.ƅ.Ɨ();ȑ.PState.LifetimeAcceptedTasks++;ƀ.ƅ.ƞ(500).Ɯ(()=>{foreach(var ƃ in ˑ.Where(é=>é.Ϊ==ί)){X.SendUnicastMessage(ƃ.Ȩ,
"miners.normal",ȑ.PState.miningPlaneNormal.Value);}});ƀ.ƅ.ƞ(1000).Ɯ(()=>{Ψ($"Broadcasting start for mining group '{ί}'");foreach(var ƃ
in ˑ.Where(é=>é.Ϊ==ί)){X.SendUnicastMessage(ƃ.Ȩ,"command","mine");}});}public void Ι(){X.SendBroadcastMessage(
"miners.command","command:force-finish");Ψ($"Broadcasting Recall");}public void Θ(){X.SendBroadcastMessage("miners.command",
"command:dispatch");ː.Clear();ˑ.ForEach(é=>é.έ="");Ψ($"WARNING! Purging Locks, green light for everybody...");}public Ζ Η;public class Ζ{
public float Ε{get;private set;}public Vector3D Δ{get;private set;}public Vector3D Γ{get;private set;}public Vector3D Β{get;
private set;}public Vector3D Α{get;private set;}public string Κ{get;private set;}public List<Ξ>ΐ;public Ζ(int Ώ,float Ύ,
Vector3D Ό,Vector3D Ί,string Ή){Ε=Ύ;Κ=Ή;Δ=Ί;Γ=Ό;Β=Vector3D.Normalize(Vector3D.Cross(Ό,Ί));Α=Vector3D.Cross(Β,Ί);ΐ=new List<Ξ>{
new Ξ()};int Ȇ=1;while(Ȥ.Ɇ(++Ȇ)<=Ώ){var ę=Ȥ.Ⱦ(Ȇ);ΐ.Add(new Ξ{Ν=ę*Ε,Ȩ=Ȇ});}}public void Έ(int Ȇ){var ň=ΐ.First(é=>é.Ȩ==Ȇ).Ν;
foreach(var ʻ in ΐ){if(Vector2.Dot(Vector2.Normalize(ʻ.Ν),Vector2.Normalize(ň))>.8f)ʻ.ß=ShaftState.Cancelled;}}public void Σ(
int Ȇ,ShaftState ñ){var ʻ=ΐ.First(é=>é.Ȩ==Ȇ);var ʓ=ʻ.ß;if(ʓ==ShaftState.Cancelled&&ʓ==ñ)ñ=ShaftState.Planned;ʻ.ß=ñ;}public
bool Ρ(ref Vector3D?Π,ref Vector3D?Ο,ref int Ȇ){var ʻ=ΐ.FirstOrDefault(é=>é.ß==ShaftState.Planned);if(ʻ!=null){Π=Γ+Β*ʻ.Ν.X+Α
*ʻ.Ν.Y;Ο=Π.Value-Δ*Variables.Get<float>("getAbove-altitude");Ȇ=ʻ.Ȩ;ʻ.ß=ShaftState.InProgress;return true;}return false;}
public class Ξ{public ShaftState ß=ShaftState.Planned;public Vector2 Ν;public int Ȩ;}}public void Μ(float Â,Vector3D Γ,
Vector3D Δ,int Ώ,string Ή){Η=new Ζ(Ώ,Â,Γ,Δ,Ή);ͽ?.Invoke(Η);ȑ.ClearPersistentState();ȑ.PState.corePoint=Γ;ȑ.PState.shaftRadius=Â;
ȑ.PState.miningPlaneNormal=Δ;ȑ.PState.MaxGenerations=Ώ;ȑ.PState.ShaftStates=Η.ΐ.Select(é=>(byte)é.ß).ToList();ȑ.PState.
CurrentTaskGroup=Ή;Ψ($"Creating task...");Ψ(ђ.Ъ("min3r.task.P",Γ,Color.Red));Ψ(ђ.Ъ("min3r.task.Np",Γ-Δ,Color.Red));Ψ($"shaftRadius: {Â}"
);Ψ($"maxGenerations: {Ώ}");Ψ($"shafts: {Η.ΐ.Count}");Ψ($"groupConstraint: {Η.Κ}");Ψ($"Task created");}public void Ϗ(int
Ȇ){var ƃ=ShaftState.Cancelled;Η?.Σ(Ȇ,ƃ);ȑ.PState.ShaftStates[Ȇ]=(byte)ƃ;ͽ?.Invoke(Η);}public void ѯ(int Ȇ){var ƃ=
ShaftState.Complete;Η?.Σ(Ȇ,ShaftState.Complete);ȑ.PState.ShaftStates[Ȇ]=(byte)ƃ;ͽ?.Invoke(Η);}public void Ѯ(int Ȇ){Η?.Έ(Ȇ);ͽ?.
Invoke(Η);}public bool ѭ(ref Vector3D?Π,ref Vector3D?Ο,ref int Ȇ){Ψ($"CurrentTask.RequestShaft");bool Ȱ=Η.Ρ(ref Π,ref Ο,ref Ȇ)
;ȑ.PState.ShaftStates[Ȇ]=(byte)ShaftState.InProgress;ͽ?.Invoke(Η);return Ȱ;}StringBuilder ή=new StringBuilder();public
override string ToString(){ή.Clear();ή.AppendLine($"CircularPattern radius: {ȑ.PState.shaftRadius:f2}");ή.AppendLine($" ");ή.
AppendLine($"Total subordinates: {ˑ.Count}");ή.AppendLine($"Lock queue: {ː.Count}");ή.AppendLine(
$"LifetimeAcceptedTasks: {ȑ.PState.LifetimeAcceptedTasks}");return ή.ToString();}}Ѭ є;class Ѭ{public ˠ Ѫ{get;set;}è Ů;public ҋ ѩ{get;private set;}public Role Ѩ;public long?ѧ;
public float?Ϋ;public string έ="";public string Ѧ="";public bool ѥ;public bool Ѥ;bool?ѫ;public bool ѣ{set{if(ѫ!=value){ҝ.
ForEach(ƭ=>ƭ.TerrainClearingMode=value);ѽ($"command:set-sp-limit:{Variables.Get<float>(value?"speed-clear":"speed-drill")}");ѫ=value;}}}
public Vector3D Ѵ(){if(!Ѳ.miningPlaneNormal.HasValue){var Ѣ=Ă.GetNaturalGravity();if(Ѣ==Vector3D.Zero)throw new Exception(
"Need either natural gravity or miningPlaneNormal");else return Vector3D.Normalize(Ѣ);}return Ѳ.miningPlaneNormal.Value;}public MinerState ѳ(){return Ѳ.MinerState;}public
PersistentState Ѳ{get{return ȑ.PState;}}Func<string,Ĕ>ѱ;StateWrapper ȑ;public Ѭ(Role ϗ,IMyGridTerminalSystem Ũ,
IMyIntergridCommunicationSystem Ā,StateWrapper ȑ,Func<string,Ĕ>Ŏ,IMyTerminalBlock Ţ){ѱ=Ŏ;this.Ѩ=ϗ;this.Ũ=Ũ;X=Ā;this.ȑ=ȑ;Ҟ=ї<IMyGyro>(â=>â.CustomName.
Contains(ForwardGyroTag)&&â.IsSameConstructAs(Ţ));Ă=ї<IMyRemoteControl>(â=>â.IsSameConstructAs(Ţ));Ɣ=ї<IMyShipConnector>(â=>â.
IsSameConstructAs(Ţ));Ũ.GetBlocksOfType(ҝ,ƭ=>ƭ.IsSameConstructAs(Ţ));Ũ.GetBlocksOfType(ҟ,ƭ=>ƭ.IsSameConstructAs(Ţ)&&ƭ.HasInventory&&((ƭ
is IMyCargoContainer)||(ƭ is IMyShipDrill)||(ƭ is IMyShipConnector)));Ũ.GetBlocksOfType(Ҝ,â=>â.IsSameConstructAs(Ţ));Ũ.
GetBlocksOfType(қ,â=>â.IsSameConstructAs(Ţ));List<IMyTimerBlock>å=new List<IMyTimerBlock>();Ũ.GetBlocksOfType(å,â=>â.IsSameConstructAs(
Ţ));Ů=new è(å);float Ѱ=0;float ř=Ţ.CubeGrid.GridSizeEnum==MyCubeSize.Large?2f:1.5f;foreach(var ƭ in ҝ){var Â=Vector3D.
Reject(ƭ.GetPosition()-Ҟ.GetPosition(),Ҟ.WorldMatrix.Forward).Length();Ѱ=(float)Math.Max(Â+ř,Ѱ);}Variables.Set(
"circular-pattern-shaft-radius",Ѱ);var Ǐ=new List<IMyRadioAntenna>();Ũ.GetBlocksOfType(Ǐ,â=>â.IsSameConstructAs(Ţ));ý=Ǐ.FirstOrDefault();var ќ=new List
<IMyLightingBlock>();Ũ.GetBlocksOfType(ќ,â=>â.IsSameConstructAs(Ţ));Ҡ=ќ.FirstOrDefault();Ũ.GetBlocksOfType(Қ,â=>â.
IsSameConstructAs(Ţ));}public void њ(IMyProgrammableBlock ћ){this.ћ=ћ;}public void њ(ƕ ǒ){ҡ=ǒ;}public MinerState љ{get;private set;}
public void Ċ(MinerState Ć){Ů.ä(ѳ()+".OnExit");Ψ("SetState: "+ѳ()+"=>"+Ć);Ů.ä(Ć+".OnEnter");љ=Ѳ.MinerState;Ѳ.MinerState=Ć;if((
Ć==MinerState.Disabled)||(Ć==MinerState.Idle)){ҝ.ForEach(ƭ=>ƭ.Enabled=false);ѽ("command:pillock-mode:Inert",U=>U.Ċ(
ApckState.Inert));}}public void ј(){ѷ(1,1);ѽ("command:pillock-mode:Disabled",U=>U.Š.Ċ(à.ß.Þ));ҝ.ForEach(ƭ=>ƭ.Enabled=false);ȑ.
ClearPersistentState();}public void Ĉ(string ć){MinerState Ć;if(Enum.TryParse(ć,out Ć))Ċ(Ć);}public Ŧ ї<Ŧ>(Func<IMyTerminalBlock,bool>і)
where Ŧ:class{var ѕ=new List<IMyTerminalBlock>();Ũ.GetBlocksOfType(ѕ,â=>((â is Ŧ)&&і(â)));return ѕ.First()as Ŧ;}public void Μ
(){var Ѣ=Ă.GetNaturalGravity();if(Ѣ!=Vector3D.Zero)Ѳ.miningPlaneNormal=Vector3D.Normalize(Ѣ);else Ѳ.miningPlaneNormal=Ҟ.
WorldMatrix.Forward;double À;if(Ă.TryGetPlanetElevation(MyPlanetElevation.Surface,out À))Ѳ.miningEntryPoint=Ҟ.WorldMatrix.
Translation+Ѳ.miningPlaneNormal.Value*(À-5);else Ѳ.miningEntryPoint=Ҟ.WorldMatrix.Translation;if(Ѩ==Role.Agent){if(ѧ.HasValue){X.
SendUnicastMessage(ѧ.Value,"create-task",new MyTuple<float,Vector3D,Vector3D>(Variables.Get<float>("circular-pattern-shaft-radius"),Ѳ.
miningEntryPoint.Value,Ѳ.miningPlaneNormal.Value));}}else if(Ѩ==Role.Lone){var Ͻ=Ѳ.miningEntryPoint.Value;Ѫ.Μ(Variables.Get<float>(
"circular-pattern-shaft-radius"),Ͻ,Ѳ.miningPlaneNormal.Value,Variables.Get<int>("max-generations"),"LocalDispatcher");Ѳ.getAbovePt=Ͻ-Ѳ.
miningPlaneNormal.Value*Variables.Get<float>("getAbove-altitude");Ѳ.miningEntryPoint=Ͻ;}}public void ǈ(List<MyIGCMessage>ˏ){Р.Ƌ(ҡ!=null?
"Embedded APck":ћ.CustomName);ҡ?.ǈ(ο,Р.Ƌ);if((ѩ!=null)&&(!ѥ)){if((Ѩ!=Role.Agent)||(ѧ.HasValue))ѩ.ϻ(Ѳ.MinerState);}var Ļ=ѩ;var Χ=X.
RegisterBroadcastListener("miners");foreach(var ˋ in ˏ){if(!ˋ.Tag.Contains("set-vectors"))Ҥ(ˋ,false);if((ˋ.Tag=="miners.assign-shaft")&&(ˋ.Data
is MyTuple<int,Vector3D,Vector3D>)&&(Ѩ==Role.Agent)){var Τ=(MyTuple<int,Vector3D,Vector3D>)ˋ.Data;if(Ļ!=null){Ļ.Ͼ(Τ.Item1,
Τ.Item2,Τ.Item3);Ψ("Got new ShaftVectors");ў();}}if(ˋ.Tag=="miners.handshake.reply"){Ψ("Received reply from dispatcher "+
ˋ.Source);ѧ=ˋ.Source;}if(ˋ.Tag=="miners.echelon"){Ψ("Was assigned an echelon of "+ˋ.Data);Ϋ=(float)ˋ.Data;}if(ˋ.Tag==
"miners.normal"){var Ί=(Vector3D)ˋ.Data;Ψ("Was assigned a normal of "+Ί);Ѳ.miningPlaneNormal=Ί;}if(ˋ.Tag=="miners.resume"){var Ί=(
Vector3D)ˋ.Data;Ψ("Received resume command. Clearing state, running MineCommandHandler, assigned a normal of "+Ί);ȑ.
ClearPersistentState();Ѳ.miningPlaneNormal=Ί;Ҷ();}if(ˋ.Tag=="command"){if(ˋ.Data.ToString()=="force-finish")Ҩ();if(ˋ.Data.ToString()=="mine"
)Ҷ();}if(ˋ.Tag=="set-value"){var ˀ=((string)ˋ.Data).Split(':');Ψ($"Set value '{ˀ[0]}' to '{ˀ[1]}'");Variables.Set(ˀ[0],ˀ[
1]);}if(ˋ.Data.ToString().Contains("common-airspace-lock-granted")){var Φ=ˋ.Data.ToString().Split(':')[1];if(!string.
IsNullOrEmpty(έ)&&(έ!=Φ)){Ψ($"{Φ} common-airspace-lock hides current ObtainedLock {έ}!");}έ=Φ;Ψ(Φ+" common-airspace-lock-granted");if
(Ѧ==Φ)ў();}if(ˋ.Tag=="report.request"){var Ϟ=new Ȳ();Ϟ.Ȩ=X.Me;Ϟ.Ȫ=Ҟ.WorldMatrix;Ϟ.ȩ=Ҡ?.Color??Color.White;ѩ?.ϟ(Ϟ,Ѳ.
MinerState);X.SendBroadcastMessage("miners.report",Ϟ.ȥ());}}while(Χ.HasPendingMessage){var ˋ=Χ.AcceptMessage();Ҥ(ˋ,false);if(ˋ.
Data!=null){if(ˋ.Data.ToString().Contains("common-airspace-lock-released")){var Φ=ˋ.Data.ToString().Split(':')[1];if(Ѩ==Role
.Agent){Ψ("(Agent) received lock-released notification "+Φ+" from "+ˋ.Source);}}if(Ѩ==Role.Agent){if(ˋ.Data.ToString()==
"dispatcher-change"){ѧ=null;ƀ.ƅ.ƚ(()=>!ѧ.HasValue).ƞ(1000).Ɯ(()=>ό("miners.handshake",Variables.Get<string>("group-constraint")));}}}}}
Queue<Action<Ѭ>>ѡ=new Queue<Action<Ѭ>>();public void Ѡ(string Φ,Action<Ѭ>џ){ѥ=true;if(!string.IsNullOrEmpty(Φ))Ѧ=Φ;ѡ.Enqueue(
џ);Ψ("WaitForDispatch section \""+Φ+"\", callback chain: "+ѡ.Count);}public void ў(){ѥ=false;Ѧ="";var ѝ=ѡ.Count;if(ѝ>0){Ψ
("Dispatching, callback chain: "+ѝ);var Ȋ=ѡ.Dequeue();Ȋ.Invoke(this);}else Ψ("WARNING: empty Dispatch()");}public void ό<
Ŧ>(string ŵ,Ŧ Τ){X.SendBroadcastMessage(ŵ,Τ,TransmissionDistance.TransmissionDistanceMax);Ҥ(Τ,true);}public void ҥ<Ŧ>(
string ŵ,Ŧ Τ){if(ѧ.HasValue)X.SendUnicastMessage(ѧ.Value,ŵ,Τ);}public void Ψ(object ˋ){Р.Ƈ($"MinerController -> {ˋ}");}public
void Ҥ(object ˋ,bool ң){string Τ=ˋ.GetType().Name;if(ˋ is string)Τ=(string)ˋ;else if((ˋ is ImmutableArray<Vector3D>)||(ˋ is
Vector3D))Τ="some vector(s)";if(λ.ƅ.φ("log-message")){if(!ң)Р.Ƈ($"MinerController MSG-IN -> {Τ}");else Р.Ƈ(
$"MinerController MSG-OUT -> {Τ}");}}public Action Ң;public IMyProgrammableBlock ћ;ƕ ҡ;public IMyGridTerminalSystem Ũ;public
IMyIntergridCommunicationSystem X;public IMyRemoteControl Ă;public List<IMyTerminalBlock>ҟ=new List<IMyTerminalBlock>();public IMyTerminalBlock Ҟ;
public List<IMyShipDrill>ҝ=new List<IMyShipDrill>();public IMyShipConnector Ɣ;public IMyRadioAntenna ý;public List<
IMyBatteryBlock>Ҝ=new List<IMyBatteryBlock>();public List<IMyGasTank>қ=new List<IMyGasTank>();public IMyLightingBlock Ҡ;public List<
IMyTerminalBlock>Қ=new List<IMyTerminalBlock>();public void Ҧ(){ѩ=new ҋ(this);ѩ.ϛ=DateTime.Now;}public void Ҷ(){ѩ=new ҋ(this);ѩ.ϛ=
DateTime.Now;Ѳ.LifetimeAcceptedTasks++;Ѳ.maxDepth=Variables.Get<float>("depth-limit");Ѳ.skipDepth=Variables.Get<float>(
"skip-depth");if(!ҙ()){ѩ.Ђ();}}public void ҵ(){if(ѩ!=null){ѩ.Ѐ();}}public void Ҵ(string[]ˆ){if((ˆ.Length>2)&&(ˆ[2]=="clear"))Ѳ.
StaticDockOverride=null;else Ѳ.StaticDockOverride=Ҟ.WorldMatrix.Translation;}public Vector3D ҳ(Vector3D ň){if(Ϋ.HasValue){return ň-Ѵ()*Ϋ.
Value;}return ň;}public Vector3D ҳ(Vector3D ň,Vector3D Ί){if(Ϋ.HasValue){return ň-Ί*Ϋ.Value;}return ň;}public bool Ҳ(bool ұ){
if(Ѳ.StaticDockOverride.HasValue){string Ұ="command:create-wp:Name=StaticDock,Ng=Forward:"+ђ.т(Ѳ.StaticDockOverride.Value)
;if(!WholeAirspaceLocking)ѿ(LOCK_NAME_GeneralSection);if(Ϋ.HasValue){Ұ=
"command:create-wp:Name=StaticDock.echelon,Ng=Forward:"+ђ.т(ҳ(Ѳ.StaticDockOverride.Value))+":"+Ұ;}if(ұ){if(Ѳ.getAbovePt.HasValue)ѽ(
"command:create-wp:Name=StaticDock.getAbovePt,Ng=Forward,SpeedLimit="+Variables.Get<float>("speed-clear")+":"+ђ.т(ҳ(Ѳ.getAbovePt.Value))+":"+Ұ);else{var ү=Ѳ.StaticDockOverride.Value;
Vector3D Ǎ;Ă.TryGetPlanetPosition(out Ǎ);var Ү=ү-Ǎ;var ҭ=(Ҟ.GetPosition()-Ǎ);var Ћ=Vector3D.Normalize(ҭ);var Ҭ=Ү.Length()>ҭ.
Length()?Ү:ҭ;var ҫ=Ǎ+Ћ*(Ҭ.Length()+100f);ѽ("command:create-wp:Name=StaticDock.approachP,Ng=Forward:"+ђ.т(ҫ)+":"+Ұ);}}else{ѽ(Ұ)
;}return true;}return false;}public void Ҫ(){bool ҩ=ѳ()==MinerState.ForceFinish;if(Ѳ.StaticDockOverride.HasValue){if(Ҳ(ҩ)
){if(!WholeAirspaceLocking)ѿ(LOCK_NAME_GeneralSection);if(!ҩ)Ċ(MinerState.Docking);}}else{if(ѧ.HasValue){if(!
WholeAirspaceLocking)ѿ(LOCK_NAME_GeneralSection);Ң?.Invoke();X.SendUnicastMessage(ѧ.Value,"apck.docking.request",Ɣ.GetPosition());Ċ(
MinerState.WaitingForDocking);}else{if(!ҩ){Ċ(MinerState.Docking);ѽ("command:request-docking");}else{ѽ(
"[command:create-wp:Name=AutoDock.getAbovePt,Ng=Forward,SpeedLimit="+Variables.Get<float>("speed-clear")+":"+ђ.т(ҳ(Ѳ.getAbovePt.HasValue?Ѳ.getAbovePt.Value:(Ҟ.GetPosition()+Ҟ.WorldMatrix.
Up*50)))+":command:request-docking]");}}}}public void Ҩ(){if(Ɣ.Status==MyShipConnectorStatus.Connected){Ҝ.ForEach(â=>â.
ChargeMode=ChargeMode.Recharge);қ.ForEach(â=>â.Stockpile=true);Ѳ.lastAPckCommand="";Ċ(MinerState.Disabled);}else{ҝ.ForEach(ҧ=>ҧ.
Enabled=false);Ѧ="";ѡ.Clear();Ҁ(LOCK_NAME_ForceFinishSection,(ϧ)=>{if(Ѳ.MinerState==MinerState.ForceFinish||Ѳ.MinerState==
MinerState.Docking||Ɣ.Status==MyShipConnectorStatus.Connected){Ċ(MinerState.ForceFinish);Ψ(
"Started force-finish callback during docking in progress!");ѿ(LOCK_NAME_ForceFinishSection);return;}Ċ(MinerState.ForceFinish);ѩ=ѩ??new ҋ(this);Ҫ();});}}public bool ҙ(){if(Ɣ.
Status==MyShipConnectorStatus.Connected){if(Ѳ.getAbovePt.HasValue){Ċ(MinerState.Docking);return true;}else{if(Ѩ==Role.Agent){ҥ
("request-new","");Ѡ("",ϧ=>{ϧ.Ċ(MinerState.Docking);});return true;}}}return false;}public void Ҁ(string Φ,Action<Ѭ>Ǉ){if
(Ѩ==Role.Agent){ό("miners","common-airspace-ask-for-lock:"+Φ);Ѡ(Φ,Ǉ);}else{Ǉ(this);}}public void ѿ(string Φ){if(έ==Φ){έ=
null;if(Ѩ==Role.Agent){ό("miners","common-airspace-lock-released:"+Φ);Ψ($"Released lock: {Φ}");}}else{Ψ(
"Tried to release non-owned lock section "+Φ);}}public σ Ѿ;public void ѽ(string ƛ,Action<ƕ>Ѽ=null){Ѳ.lastAPckCommand=ƛ;Р.Ƈ("CommandAutoPillock: "+ƛ);if(ҡ!=null){
if(Ѽ!=null){Ѽ(ҡ);}else{var ѻ=ƛ.Split(new[]{"],["},StringSplitOptions.RemoveEmptyEntries).Select(ƃ=>ƃ.Trim('[',']')).ToList
();foreach(var ĺ in ѻ){string[]π=ĺ.Split(new[]{':'},StringSplitOptions.RemoveEmptyEntries);if(π[0]=="command"){Ѿ.ρ(π[1],π
);}}}}else{if(X.IsEndpointReachable(ћ.EntityId)){X.SendUnicastMessage(ћ.EntityId,"apck.command",ƛ);}else{throw new
Exception($"APck {ћ.EntityId} is not reachable");}}}DateTime Ѻ;bool ѹ(float Ѷ,float ѵ){var Ѹ=DateTime.Now;if((Ѹ-Ѻ).TotalSeconds>
60){Ѻ=Ѹ;return ѷ(Ѷ,ѵ);}return true;}bool ѷ(float Ѷ,float ѵ){Қ.ForEach(é=>Ғ(é,ҕ(é),љ!=MinerState.ForceFinish));if(Қ.Any(â=>
!â.IsFunctional)){if(ý!=null)ý.CustomName=ý.CubeGrid.CustomName+"> Damaged. Fix me asap!";Қ.Where(â=>!â.IsFunctional).
ToList().ForEach(â=>Р.Ƈ($"{â.CustomName} is damaged or destroyed"));return false;}float Ҙ=0;float җ=0;foreach(var â in Ҝ){җ+=â
.MaxStoredPower;Ҙ+=â.CurrentStoredPower;}double Җ=0;foreach(var â in қ){Җ+=â.FilledRatio;}if(қ.Any()&&(Җ/қ.Count<ѵ)){if(ý
!=null)ý.CustomName=$"{ý.CubeGrid.CustomName}> Maintenance. Gas level: {Җ/қ.Count:f2}/{ѵ:f2}";return false;}else if(Ҙ/җ<Ѷ)
{if(ý!=null)ý.CustomName=$"{ý.CubeGrid.CustomName}> Maintenance. Charge level: {Ҙ/җ:f2}/{Ѷ:f2}";return false;}else{return
true;}}float ҕ(IMyTerminalBlock Ҕ){IMySlimBlock ғ=Ҕ.CubeGrid.GetCubeBlock(Ҕ.Position);if(ғ!=null)return(ғ.BuildIntegrity-ғ.
CurrentDamage)/ғ.MaxIntegrity;else return 1f;}void Ғ(IMyTerminalBlock Ҍ,float ґ,bool Ґ){string ã=Ҍ.CustomName;if((ґ<1f)&&(!Ґ||!Ҍ.
IsFunctional)){if(!(Ҍ is IMyRadioAntenna)&&!(Ҍ is IMyBeacon)){Ҍ.SetValue("ShowOnHUD",true);}string ҏ;if(ã.Contains("||")){string Ҏ=
@"(?<=DAMAGED: )(?<label>\d+)(?=%)";System.Text.RegularExpressions.Regex Â=new System.Text.RegularExpressions.Regex(Ҏ);ҏ=Â.Replace(ã,delegate(System.Text.
RegularExpressions.Match ȉ){return(ґ*100).ToString("F0");});}else{ҏ=string.Format("{0} || DAMAGED: {1}%",ã,ґ.ToString("F0"));Ψ(
$"{ã} was damaged. Showing on HUD.");}Ҍ.CustomName=ҏ;}else{ҍ(Ҍ);}}void ҍ(IMyTerminalBlock Ҍ){if(Ҍ.CustomName.Contains("||")){string ã=Ҍ.CustomName;Ҍ.
CustomName=ã.Split('|')[0].Trim();if(!(Ҍ is IMyRadioAntenna)&&!(Ҍ is IMyBeacon)){Ҍ.SetValue("ShowOnHUD",false);}Ψ(
$"{Ҍ.CustomName} was fixed.");}}public class ҋ{protected Ѭ á;bool Ҋ(double ҁ){return(!á.Ѳ.currentWp.HasValue||(á.Ѳ.currentWp.Value-á.Ҟ.WorldMatrix.
Translation).Length()<=ҁ);}public ҋ(Ѭ є){á=є;}public void Ђ(){if(á.Ѩ==Role.Agent){á.ҥ("request-new","");á.Ѡ("",ϧ=>{á.Ҁ(
LOCK_NAME_GeneralSection,é=>{é.Ċ(MinerState.ChangingShaft);é.ҝ.ForEach(ƭ=>ƭ.Enabled=false);var ϡ=-15;var ň=é.ҳ(á.Ѳ.miningEntryPoint.Value+á.Ѵ()*
ϡ);var Ё=$"command:create-wp:Name=ChangingShaft,Ng=Forward,UpNormal=1;0;0,"+$"AimNormal={ђ.т(á.Ѵ()).Replace(':',';')}"+
$":{ђ.т(ň)}";á.ѽ(Ё);á.Ѳ.currentWp=ň;});});}else if(á.Ѩ==Role.Lone){á.Ѳ.maxDepth=Variables.Get<float>("depth-limit");á.Ѳ.skipDepth=
Variables.Get<float>("skip-depth");á.Ċ(MinerState.GoingToEntry);á.Ѳ.currentWp=á.Ѳ.miningEntryPoint;var Ё=
$"command:create-wp:Name=drill entry,Ng=Forward,"+$"AimNormal={ђ.т(á.Ѵ()).Replace(':',';')}"+$":{ђ.т(á.Ѳ.miningEntryPoint.Value)}";á.ѽ(Ё);}}public void Ѐ(){if(á.Ѳ.
CurrentJobMaxShaftYield<ϵ+ϸ-Ϸ)á.Ѳ.CurrentJobMaxShaftYield=ϵ+ϸ-Ϸ;if(λ.ƅ.φ("adaptive-mining")){if(!Ϛ.HasValue||((ϵ+ϸ-Ϸ)/á.Ѳ.
CurrentJobMaxShaftYield<0.5f)){if(á.Ѩ==Role.Agent){á.ҥ("ban-direction",á.Ѳ.CurrentShaftId.Value);}else{á.Ѫ.Ѯ(á.Ѳ.CurrentShaftId.Value);}}}Ϲ();Ϛ
=null;var ϡ=-15;var ň=á.Ѳ.miningEntryPoint.Value+á.Ѵ()*ϡ;if(á.Ѩ==Role.Agent){á.ҥ("shaft-complete-request-new",á.Ѳ.
CurrentShaftId.Value);á.Ѡ("",ϧ=>{á.Ҁ(LOCK_NAME_GeneralSection,é=>{é.Ċ(MinerState.ChangingShaft);é.ҝ.ForEach(ƭ=>ƭ.Enabled=false);é.ѽ(
"command:create-wp:Name=ChangingShaft,Ng=Forward:"+ђ.т(ň));á.Ѳ.currentWp=ň;});});}else if(á.Ѩ==Role.Lone){int Ͽ=0;if(á.Ѫ.ѭ(ref á.Ѳ.miningEntryPoint,ref á.Ѳ.getAbovePt,ref
Ͽ)){á.Ċ(MinerState.ChangingShaft);á.ҝ.ForEach(ƭ=>ƭ.Enabled=false);á.Ѳ.CurrentShaftId=Ͽ;á.ѽ(
"command:create-wp:Name=ChangingShaft,Ng=Forward:"+ђ.т(ň));á.Ѳ.currentWp=ň;}}}public void Ͼ(int Ȇ,Vector3D Ͻ,Vector3D ϼ){á.Ѳ.miningEntryPoint=Ͻ;á.Ѳ.getAbovePt=ϼ;á.Ѳ.
CurrentShaftId=Ȇ;}public void ϻ(MinerState ñ){if(ñ==MinerState.GoingToEntry){if(Ҋ(0.5f)){á.ѿ(LOCK_NAME_GeneralSection);á.ҝ.ForEach(ƭ=>
ƭ.Enabled=true);á.Ċ(MinerState.Drilling);á.ѽ("command:create-wp:Name=drill,Ng=Forward,PosDirectionOverride=Forward"+
",AimNormal="+ђ.т(á.Ѵ()).Replace(':',';')+",UpNormal=1;0;0,SpeedLimit="+Variables.Get<float>("speed-drill")+":0:0:0");}}if(ñ==
MinerState.Drilling){ϝ=(float)(á.Ҟ.WorldMatrix.Translation-á.Ѳ.miningEntryPoint.Value).Length();Р.Ƌ(
$"Depth: current: {ϝ:f1} skip: {á.Ѳ.skipDepth:f1}");if(á.Ѳ.maxDepth.HasValue&&(ϝ>á.Ѳ.maxDepth.Value)||!á.ѹ(Variables.Get<float>("battery-low-factor"),Variables.Get<float>
("gas-low-factor"))){ϥ();}if((!á.Ѳ.skipDepth.HasValue)||(ϝ>á.Ѳ.skipDepth)){á.ѣ=false;if(ϴ()){Ϛ=Math.Max(ϝ,Ϛ??0);if((!Ϡ.
HasValue)||(Ϡ>ϝ))Ϡ=ϝ;if((!Ϧ.HasValue)||(Ϧ<ϝ))Ϧ=ϝ;if(λ.ƅ.φ("adaptive-mining")){á.Ѳ.skipDepth=Ϡ.Value-2f;á.Ѳ.maxDepth=Ϧ.Value+2f;}
}else{if(Ϛ.HasValue&&(ϝ-Ϛ>2)){ϥ();}}if(ϭ()){ϥ();}}else{á.ѣ=true;}}if((ñ==MinerState.GettingOutTheShaft)||(ñ==MinerState.
WaitingForLockInShaft)){if(Ҋ(0.5f)){if(ϭ()||!á.ѷ(Variables.Get<float>("battery-low-factor"),Variables.Get<float>("gas-low-factor"))){á.Ҁ(
LOCK_NAME_GeneralSection,ϧ=>{ϧ.Ċ(MinerState.GoingToUnload);ϧ.ҝ.ForEach(ƭ=>ƭ.Enabled=false);var ň=á.ҳ(á.Ѳ.getAbovePt.Value);ϧ.ѽ(
"command:create-wp:Name=GoingToUnload,Ng=Forward:"+ђ.т(ň));á.Ѳ.currentWp=ň;});}else{Ѐ();}}}if(ñ==MinerState.ChangingShaft){if(Ҋ(0.5f)){var ϡ=-15;var ň=á.Ѳ.
miningEntryPoint.Value+á.Ѵ()*ϡ;á.ҳ(ň);á.ѽ("command:create-wp:Name=GoingToEntry (ChangingShaft),Ng=Forward:"+ђ.т(ň));á.Ѳ.currentWp=ň;á.Ċ(
MinerState.ReturningToShaft);}}if(ñ==MinerState.ReturningToShaft){if(Ҋ(1)){á.Ҁ(LOCK_NAME_GeneralSection,ϧ=>{ϧ.Ċ(MinerState.
GoingToEntry);á.ҝ.ForEach(ƭ=>ƭ.Enabled=true);var Π=$"command:create-wp:Name=drill entry,Ng=Forward,UpNormal=1;0;0,AimNormal="+
$"{ђ.т(á.Ѵ()).Replace(':',';')}:";double À;if(λ.ƅ.φ("adjust-entry-by-elevation")&&á.Ă.TryGetPlanetElevation(MyPlanetElevation.Surface,out À)){Vector3D Ǎ;
á.Ă.TryGetPlanetPosition(out Ǎ);var Ћ=Vector3D.Normalize(á.Ѳ.miningEntryPoint.Value-Ǎ);var Ɓ=(á.Ҟ.WorldMatrix.Translation
-Ǎ).Length()-À+5f;var Њ=Ǎ+Ћ*Ɓ;ϧ.ѽ(Π+ђ.т(Њ));á.Ѳ.currentWp=Њ;}else{ϧ.ѽ(Π+ђ.т(á.Ѳ.miningEntryPoint.Value));á.Ѳ.currentWp=á.
Ѳ.miningEntryPoint;}});}}if(ñ==MinerState.GoingToUnload){if(Ҋ(0.5f)){á.Ҫ();}}if(ñ==MinerState.WaitingForDocking){var Љ=á.
ѱ("docking");if(Љ.ď.HasValue){if(á.љ==MinerState.ForceFinish){Vector3D Ј;if(á.Ѳ.getAbovePt.HasValue){Ј=á.ҳ(á.Ѳ.getAbovePt
.Value);}else{var Ї=Љ.ď.Value-á.Ɣ.GetPosition();var Ť=Љ.č.Value.Backward;var І=Vector3D.ProjectOnVector(ref Ї,ref Ť);Ј=á.
ҳ(á.Ɣ.GetPosition()+І,Ť);}á.ѽ("command:create-wp:Name=ForceFinish.getAbovePt,SpeedLimit="+Variables.Get<float>(
"speed-clear")+",Ng=Forward:"+ђ.т(Ј)+":command:create-wp:Name=ForceFinish.dock-echelon,Ng=Forward,TransformChannel=docking:"+ђ.т(
Vector3D.Transform(á.ҳ(Љ.ď.Value,Љ.č.Value.Backward)-Љ.č.Value.Backward*Variables.Get<float>("getAbove-altitude"),MatrixD.Invert
(Љ.č.Value)))+":command:pillock-mode:DockingFinal");á.Ċ(MinerState.ForceFinish);}else{á.ѽ(
"command:create-wp:Name=DynamicDock.echelon,Ng=Forward,AimNormal="+ђ.т(á.Ѵ()).Replace(':',';')+",TransformChannel=docking:"+ђ.т(Vector3D.Transform(á.ҳ(Љ.ď.Value,Љ.č.Value.Backward)-Љ.č.
Value.Backward*Variables.Get<float>("getAbove-altitude"),MatrixD.Invert(Љ.č.Value)))+":command:pillock-mode:DockingFinal");á.
Ċ(MinerState.Docking);}}}if(ñ==MinerState.Docking){if(á.Ɣ.Status==MyShipConnectorStatus.Connected){if(!á.Ѥ){á.Ѥ=true;Р.Ƈ(
"Regular docking handled");á.ѽ("command:pillock-mode:Disabled");á.Ă.DampenersOverride=false;á.Ҝ.ForEach(â=>â.ChargeMode=ChargeMode.Recharge);á.Ɣ.
OtherConnector.CustomData="";á.Ң?.Invoke();á.қ.ForEach(â=>â.Stockpile=true);if(á.έ==LOCK_NAME_GeneralSection)á.ѿ(
LOCK_NAME_GeneralSection);}Р.Ƌ("Docking: Connected");if(!Ѕ()){Р.Ƌ("Docking: still have items");}else{if(á.ѷ(Variables.Get<float>(
"battery-low-factor"),Variables.Get<float>("gas-low-factor"))){á.Ҁ(LOCK_NAME_GeneralSection,ϧ=>{á.Ҝ.ForEach(â=>â.ChargeMode=ChargeMode.Auto)
;á.қ.ForEach(â=>â.Stockpile=false);Ϻ();Ϫ(á.Ɣ.OtherConnector);á.Ɣ.Disconnect();á.Ċ(MinerState.ReturningToShaft);});}else{á
.Ċ(MinerState.Maintenance);á.Ѳ.LifetimeWentToMaintenance++;ƀ.ƅ.ƞ(10000).ƚ(()=>á.ѳ()==MinerState.Maintenance).Ɯ(()=>{if(á.
ѷ(Variables.Get<float>("battery-full-factor"),0.99f)){á.Ċ(MinerState.Docking);}});}}}else{if(á.Ѥ)á.Ѥ=false;if(á.Ѳ.
StaticDockOverride.HasValue)á.Ɣ.Connect();}}if(ñ==MinerState.Maintenance){if((á.љ!=MinerState.Docking)&&(á.Ɣ.Status==MyShipConnectorStatus
.Connected)){á.ѽ("command:pillock-mode:Disabled");ƀ.ƅ.ƞ(10000).ƚ(()=>á.ѳ()==MinerState.Maintenance).Ɯ(()=>{if(á.ѷ(
Variables.Get<float>("battery-full-factor"),0.99f)){á.Ċ(MinerState.Docking);}});}}if(ñ==MinerState.ForceFinish){if(á.Ɣ.Status==
MyShipConnectorStatus.Connected){if(!á.Ѥ){á.Ѥ=true;Р.Ƈ("ForceFinish docking handled");á.ѽ("command:pillock-mode:Disabled");á.Ă.
DampenersOverride=false;á.Ҝ.ForEach(â=>â.ChargeMode=ChargeMode.Recharge);á.Ɣ.OtherConnector.CustomData="";á.Ң?.Invoke();á.қ.ForEach(â=>â.
Stockpile=true);á.έ=LOCK_NAME_ForceFinishSection;á.ѿ(LOCK_NAME_ForceFinishSection);á.έ=LOCK_NAME_GeneralSection;á.ѿ(
LOCK_NAME_GeneralSection);}if(!Ѕ()){Р.Ƌ("ForceFinish: still have items");}else{á.Ċ(MinerState.Disabled);Ϻ();á.Ѳ.LifetimeOperationTime+=(int)(
DateTime.Now-ϛ).TotalSeconds;á.ȑ.Save();á.ѩ=null;}}else{if(á.Ѥ)á.Ѥ=false;if(á.Ѳ.StaticDockOverride.HasValue)á.Ɣ.Connect();}}}
bool Ѕ(){var Є=new List<IMyCargoContainer>();á.Ũ.GetBlocksOfType(Є,â=>â.IsSameConstructAs(á.Ɣ.OtherConnector)&&â.
HasInventory&&â.IsFunctional&&(â is IMyCargoContainer));var Ѓ=á.ҟ.Select(á=>á.GetInventory()).Where(ĺ=>ĺ.ItemCount>0);if(Ѓ.Any()){Р.
Ƌ("Docking: still have items");foreach(var ϙ in Ѓ){var Ϥ=new List<MyInventoryItem>();ϙ.GetItems(Ϥ);for(int Ť=0;Ť<Ϥ.Count;
Ť++){var ϣ=Ϥ[Ť];IMyInventory Ϣ;var Ȉ=Variables.Get<string>("preferred-container");if(!string.IsNullOrEmpty(Ȉ))Ϣ=Є.Where(é
=>é.CustomName.Contains(Ȉ)).Select(á=>á.GetInventory()).FirstOrDefault();else Ϣ=Є.Select(á=>á.GetInventory()).Where(ĺ=>ĺ.
CanItemsBeAdded((MyFixedPoint)(1f),ϣ.Type)).OrderBy(ĺ=>(float)ĺ.CurrentVolume).FirstOrDefault();if(Ϣ!=null){if(!ϙ.TransferItemTo(Ϣ,Ϥ[Ť]
)){Р.Ƌ("Docking: failing to transfer from "+(ϙ.Owner as IMyTerminalBlock).CustomName+" to "+(Ϣ.Owner as IMyTerminalBlock)
.CustomName);}}}}return false;}return true;}void ϥ(){ϝ=0;if(á.Ѩ==Role.Agent){á.Ċ(MinerState.WaitingForLockInShaft);var ϡ=
Math.Min(8,(á.Ҟ.WorldMatrix.Translation-á.Ѳ.miningEntryPoint.Value).Length());var ň=á.Ѳ.miningEntryPoint.Value+á.Ѵ()*ϡ;á.ѽ(
"command:create-wp:Name=WaitingForLockInShaft,Ng=Forward"+",AimNormal="+ђ.т(á.Ѵ()).Replace(':',';')+",UpNormal=1;0;0,SpeedLimit="+Variables.Get<float>("speed-clear")+":"+ђ.т(ň))
;á.Ѳ.currentWp=ň;}else if(á.Ѩ==Role.Lone){á.Ċ(MinerState.GettingOutTheShaft);á.ѽ(
"command:create-wp:Name=GettingOutTheShaft,Ng=Forward,UpNormal=1;0;0,SpeedLimit="+Variables.Get<float>("speed-clear")+":"+ђ.т(á.Ѳ.miningEntryPoint.Value));á.Ѳ.currentWp=á.Ѳ.miningEntryPoint;}}public
void ϟ(Ȳ Ϟ,MinerState ñ){var â=ImmutableArray.CreateBuilder<MyTuple<string,string>>(10);â.Add(new MyTuple<string,string>(
"State",ñ.ToString()));â.Add(new MyTuple<string,string>("Adaptive\nmode",λ.ƅ.φ("adaptive-mining")?"Y":"N"));â.Add(new MyTuple<
string,string>("Session\nore mined",Ϝ.ToString("f2")));â.Add(new MyTuple<string,string>("Last found\nore depth",(Ϛ??0f).
ToString("f2")));â.Add(new MyTuple<string,string>("Cargo\nfullness",ϯ.ToString("f2")));â.Add(new MyTuple<string,string>(
"Current\ndepth",ϝ.ToString("f2")));â.Add(new MyTuple<string,string>("Lock\nrequested",á.Ѧ));â.Add(new MyTuple<string,string>(
"Lock\nowned",á.έ));Ϟ.ȧ=â.ToImmutableArray();}StringBuilder ή=new StringBuilder();public override string ToString(){ή.Clear();ή.
AppendFormat("session uptime: {0}\n",(ϛ==default(DateTime)?"-":(DateTime.Now-ϛ).ToString()));ή.AppendFormat(
"session ore mass: {0}\n",Ϝ);ή.AppendFormat("cargoFullness: {0:f2}\n",ϯ);ή.AppendFormat("cargoMass: {0:f2}\n",Ϯ);ή.AppendFormat(
"cargoYield: {0:f2}\n",ϵ);ή.AppendFormat("lastFoundOreDepth: {0}\n",Ϛ.HasValue?Ϛ.Value.ToString("f2"):"-");ή.AppendFormat(
"minFoundOreDepth: {0}\n",Ϡ.HasValue?Ϡ.Value.ToString("f2"):"-");ή.AppendFormat("maxFoundOreDepth: {0}\n",Ϧ.HasValue?Ϧ.Value.ToString("f2"):"-");
ή.AppendFormat("shaft id: {0}\n",á.Ѳ.CurrentShaftId??-1);return ή.ToString();}float ϝ;public float Ϝ;public DateTime ϛ;
float?Ϛ;float?Ϡ{get{return á.Ѳ.minFoundOreDepth;}set{á.Ѳ.minFoundOreDepth=value;}}float?Ϧ{get{return á.Ѳ.maxFoundOreDepth;}
set{á.Ѳ.maxFoundOreDepth=value;}}public float ϰ(){return ϵ+ϸ-Ϸ;}void Ϻ(){Ϝ+=Ϯ;á.Ѳ.LifetimeOreAmount+=Ϯ;á.Ѳ.LifetimeYield+=ϵ
;ϸ+=ϵ-Ϸ;Ϸ=0;ϵ=0;}void Ϲ(){Ϸ=ϵ;ϸ=0;}float ϸ=0;float Ϸ=0;float ϵ=0;bool ϴ(){float ϳ=0;for(int ĺ=0;ĺ<á.ҟ.Count;ĺ++){var ɨ=á.
ҟ[ĺ].GetInventory(0);if(ɨ==null)continue;List<MyInventoryItem>Ϥ=new List<MyInventoryItem>();ɨ.GetItems(Ϥ);Ϥ.Where(ϲ=>ϲ.
Type.ToString().Contains("Ore")&&!ϲ.Type.ToString().Contains("Stone")).ToList().ForEach(é=>ϳ+=(float)é.Amount);}bool ϱ=false
;if((ϵ>0)&&(ϳ>ϵ)){ϱ=true;}ϵ=ϳ;return ϱ;}float ϯ;float Ϯ;bool ϭ(){float Ϭ=0;float ϫ=0;Ϯ=0;for(int ĺ=0;ĺ<á.ҟ.Count;ĺ++){var
ɨ=á.ҟ[ĺ].GetInventory(0);if(ɨ==null)continue;Ϭ+=(float)ɨ.MaxVolume;ϫ+=(float)ɨ.CurrentVolume;Ϯ+=(float)ɨ.CurrentMass;}ϯ=ϫ
/Ϭ;return ϯ>=Variables.Get<float>("cargo-full-factor");}void Ϫ(IMyShipConnector ϩ){string Ϩ=
"command:create-wp:Name=drill getAbovePt,Ng=Forward,AimNormal="+ђ.т(á.Ѵ()).Replace(':',';')+":"+ђ.т(á.ҳ(á.Ѳ.getAbovePt.Value));var Ќ=á.ҳ(ϩ.WorldMatrix.Translation,ϩ.WorldMatrix.
Backward)-ϩ.WorldMatrix.Backward*Variables.Get<float>("getAbove-altitude");var Ѝ=
"[command:pillock-mode:Disabled],[command:create-wp:Name=Dock.Echelon,Ng=Forward:"+ђ.т(Ќ)+":"+Ϩ+"]";á.ѽ(Ѝ);á.Ѳ.currentWp=á.ҳ(á.Ѳ.getAbovePt.Value);}}}static class ђ{public static string т(params
Vector3D[]р){return string.Join(":",р.Select(Ĝ=>string.Format("{0}:{1}:{2}",Ĝ.X,Ĝ.Y,Ĝ.Z)));}public static string п(MatrixD о){
StringBuilder ή=new StringBuilder();for(int ĺ=0;ĺ<4;ĺ++){for(int Ļ=0;Ļ<4;Ļ++){ή.Append(о[ĺ,Ļ]+":");}}return ή.ToString().TrimEnd(':')
;}public static Vector3D н(MatrixD м,Vector3D л,BoundingSphereD с){RayD Â=new RayD(м.Translation,Vector3D.Normalize(л-м.
Translation));double?к=Â.Intersects(с);if(к.HasValue){var й=Vector3D.Normalize(с.Center-м.Translation);if(с.Contains(м.Translation)
==ContainmentType.Contains)return с.Center-й*с.Radius;var и=Vector3D.Cross(Â.Direction,й);Vector3D з;if(и.Length()<double.
Epsilon)й.CalculatePerpendicularVector(out з);else з=Vector3D.Cross(и,-й);return с.Center+Vector3D.Normalize(з)*с.Radius;}
return л;}public static Vector3D ж(Vector3D е,MatrixD д,MatrixD г,Vector3D в,ref Vector3D б){var Ã=д.Up;var а=Vector3D.
ProjectOnPlane(ref е,ref Ã);var у=-(float)Math.Atan2(Vector3D.Dot(Vector3D.Cross(д.Forward,а),Ã),Vector3D.Dot(д.Forward,а));Ã=д.Right;
а=Vector3D.ProjectOnPlane(ref е,ref Ã);var ѓ=-(float)Math.Atan2(Vector3D.Dot(Vector3D.Cross(д.Forward,а),Ã),Vector3D.Dot(
д.Forward,а));float ё=0;if((в!=Vector3D.Zero)&&(Math.Abs(у)<.05f)&&(Math.Abs(ѓ)<.05f)){Ã=д.Forward;а=Vector3D.
ProjectOnPlane(ref в,ref Ã);ё=-(float)Math.Atan2(Vector3D.Dot(Vector3D.Cross(д.Down,а),Ã),Vector3D.Dot(д.Up,а));}var ѐ=new Vector3D(ѓ,
у,ё*Variables.Get<float>("roll-power-factor"));б=new Vector3D(Math.Abs(ѐ.X),Math.Abs(ѐ.Y),Math.Abs(ѐ.Z));var я=Vector3D.
TransformNormal(ѐ,д);var Ȋ=Vector3D.TransformNormal(я,MatrixD.Transpose(г));Ȋ.X*=-1;return Ȋ;}public static void ю(IMyGyro ф,Vector3 Я,
Vector3D Ў){float э=Я.Y;float ь=Я.X;float Ǻ=Я.Z;var ы=IsLargeGrid?30:60;var ъ=new Vector3D(1.92f,1.92f,1.92f);Func<double,double
,double,double>щ=(é,Ĝ,Ȋ)=>{var ш=Math.Abs(é);double Â;if(ш>(Ĝ*Ĝ*1.7)/(2*Ȋ))Â=ы*Math.Sign(é)*Math.Max(Math.Min(ш,1),0.002)
;else{Â=-ы*Math.Sign(é)*Math.Max(Math.Min(ш,1),0.002);}return Â*0.6;};var ч=(float)щ(э,Ў.Y,ъ.Y);var ц=(float)щ(ь,Ў.X,ъ.X)
;var х=(float)щ(Ǻ,Ў.Z,ъ.Z);ф.SetValue("Pitch",ц);ф.SetValue("Yaw",ч);ф.SetValue("Roll",х);}public static void ɲ(IMyGyro ф
,Vector3 Я,Vector3D Н,Vector3D Ў){if(Variables.Get<bool>("amp")){var Л=5f;var К=2f;Func<double,double,double>Й=(é,ƭ)=>é*(
Math.Exp(-ƭ*К)+0.8)*Л/2;Н/=Math.PI/180f;if((Н.X<2)&&(Я.X>0.017))Я.X=(float)Й(Я.X,Н.X);if((Н.Y<2)&&(Я.Y>0.017))Я.Y=(float)Й(Я
.Y,Н.Y);if((Н.Z<2)&&(Я.Z>0.017))Я.Z=(float)Й(Я.Z,Н.Z);}ю(ф,Я,Ў);}public static Vector3D Е(Vector3D И,Vector3D З,Vector3D
Ù,Vector3D Ï,Vector3D М,bool Б){double В=Vector3D.Dot(Vector3D.Normalize(Ù-И),М);if(В<30)В=30;return Е(И,З,Ù,Ï,В,Б);}
public static Vector3D Е(Vector3D Д,Vector3D Г,Vector3D Ù,Vector3D Ï,double В,bool Б){double А=Vector3D.Distance(Д,Ù);Vector3D
Џ=Ù-Д;Vector3D Ж=Vector3D.Normalize(Џ);Vector3D О=Ù;Vector3D Ц;if(Б){var Ю=Vector3D.Reject(Г,Ж);Ï-=Ю;}if(Ï.Length()>float
.Epsilon){Ц=Vector3D.Normalize(Ï);var Ь=Math.PI-Math.Acos(Vector3D.Dot(Ж,Ц));var ȿ=(Ï.Length()*Math.Sin(Ь))/В;if(Math.Abs
(ȿ)<=1){var Ы=Math.Asin(ȿ);var ƃ=А*Math.Sin(Ы)/Math.Sin(Ь+Ы);О=Ù+Ц*ƃ;}}return О;}public static string Ъ(string ã,Vector3D
ë,Color á){return$"GPS:{ã}:{ë.X}:{ë.Y}:{ë.Z}:#{á.R:X02}{á.G:X02}{á.B:X02}:";}}StringBuilder Щ=new StringBuilder();void Ш(
string Э){if(Ч!=null){Щ.AppendLine(Э);}}IMyTextPanel Ч;IMyShipController Х;void Ф(){if(Щ.Length>0){var ƃ=Щ.ToString();Щ.Clear(
);Ч?.WriteText(ƃ);}}string У(float ψ,string Т=""){string С;if(Math.Abs(ψ)>=1000000){if(!string.IsNullOrEmpty(Т))С=string.
Format("{0:0.##} M{1}",ψ/1000000,Т);else С=string.Format("{0:0.##}M",ψ/1000000);}else if(Math.Abs(ψ)>=1000){if(!string.
IsNullOrEmpty(Т))С=string.Format("{0:0.##} k{1}",ψ/1000,Т);else С=string.Format("{0:0.##}k",ψ/1000);}else{if(!string.IsNullOrEmpty(Т)
)С=string.Format("{0:0.##} {1}",ψ,Т);else С=string.Format("{0:0.##}",ψ);}return С;}static class Р{static string П="";
static Action<string>ĉ;static IMyTextSurface ë;static IMyTextSurface Λ;public static double Ŧ;public static void ģ(Action<
string>F,IMyGridTerminalSystem Ŵ,IMyProgrammableBlock Ţ){ĉ=F;ë=Ţ.GetSurface(0);ë.ContentType=ContentType.TEXT_AND_IMAGE;ë.
WriteText("");}public static void Ƌ(string ƃ){if((П=="")||(ƃ.Contains(П)))ĉ(ƃ);}static string Ɗ="";public static void Ɖ(string ƃ)
{Ɗ+=ƃ+"\n";}static List<string>ƈ=new List<string>();public static void Ƈ(string ƃ){ë.WriteText($"{Ŧ:f2}: {ƃ}\n",true);if(
Λ!=null){ƈ.Add(ƃ);}}public static void Ɔ(){Λ?.WriteText("");ƈ.Clear();}public static void Ƅ(IMyTextSurface ƃ){Λ=ƃ;}public
static void Ƃ(){if(!string.IsNullOrEmpty(Ɗ)){var Ɓ=ɿ.ŷ.Where(é=>é.IsUnderControl).FirstOrDefault()as IMyTextSurfaceProvider;if
((Ɓ!=null)&&(Ɓ.SurfaceCount>0))Ɓ.GetSurface(0).WriteText(Ɗ);Ɗ="";}if(ƈ.Any()){if(Λ!=null){ƈ.Reverse();var Ŗ=string.Join(
"\n",ƈ)+"\n"+Λ.GetText();var U=Variables.Get<int>("logger-char-limit");if(Ŗ.Length>U)Ŗ=Ŗ.Substring(0,U-1);Λ.WriteText(
$"{Ŧ:f2}: {Ŗ}");}ƈ.Clear();}}}class ƀ{static ƀ ſ=new ƀ();ƀ(){}public static ƀ ƅ{get{ſ.Ɵ=0;ſ.ƙ=null;return ſ;}}class ž{public DateTime
ƍ;public Action Ƣ;public Func<bool>ƙ;public long ơ;}Queue<ž>Ơ=new Queue<ž>();long Ɵ;Func<bool>ƙ;public ƀ ƞ(int Ɲ){this.Ɵ
+=Ɲ;return this;}public ƀ Ɯ(Action ƛ){Ơ.Enqueue(new ž{ƍ=DateTime.Now.AddMilliseconds(Ɵ),Ƣ=ƛ,ƙ=ƙ,ơ=Ɵ});return this;}public
ƀ ƚ(Func<bool>ƙ){this.ƙ=ƙ;return this;}public void Ƙ(){if(Ơ.Count>0){Р.Ƌ("Scheduled actions count:"+Ơ.Count);var á=Ơ.Peek
();if(á.ƍ<DateTime.Now){if(á.ƙ!=null){if(á.ƙ.Invoke()){á.Ƣ.Invoke();á.ƍ=DateTime.Now.AddMilliseconds(á.ơ);}else{Ơ.Dequeue
();}}else{á.Ƣ.Invoke();Ơ.Dequeue();}}}}public void Ɨ(){Ơ.Clear();Ɵ=0;ƙ=null;}}ƕ Ɩ;class ƕ{public IMyShipConnector Ɣ;
public List<IMyWarhead>Ɠ;IMyRadioAntenna ý;Ǖ ƒ;public ƻ Ƒ;public Func<string,Ĕ>Ɛ;public IMyGyro Ə;public IMyGridTerminalSystem
Ǝ;public IMyIntergridCommunicationSystem Ž;public IMyRemoteControl ů;public à Š;public è Ů;public IMyProgrammableBlock ŭ;
public IMyProgrammableBlock Ŭ;HashSet<IMyTerminalBlock>ū=new HashSet<IMyTerminalBlock>();Ŧ Ū<Ŧ>(string ã,List<IMyTerminalBlock
>ť,bool ũ=false)where Ŧ:class,IMyTerminalBlock{Ŧ Â;Р.Ƌ("Looking for "+ã);var ţ=ť.Where(â=>â is Ŧ&&â.CustomName.Contains(ã
)).Cast<Ŧ>().ToList();Â=ũ?ţ.Single():ţ.FirstOrDefault();if(Â!=null)ū.Add(Â);return Â;}List<Ŧ>ŧ<Ŧ>(List<IMyTerminalBlock>ť
,string Ť=null)where Ŧ:class,IMyTerminalBlock{var ţ=ť.Where(â=>â is Ŧ&&((Ť==null)||(â.CustomName==Ť))).Cast<Ŧ>().ToList()
;foreach(var â in ţ)ū.Add(â);return ţ;}public ƕ(IMyProgrammableBlock Ţ,PersistentState š,IMyGridTerminalSystem Ũ,
IMyIntergridCommunicationSystem Ā,Func<string,Ĕ>Ŷ){Ǝ=Ũ;Ɛ=Ŷ;Ž=Ā;Func<IMyTerminalBlock,bool>ţ=â=>â.IsSameConstructAs(Ţ);var ż=new List<IMyTerminalBlock>(
);Ũ.GetBlocks(ż);ż=ż.Where(â=>ţ(â)).ToList();Ż(ż);}public void Ż(List<IMyTerminalBlock>ź){var ţ=ź;Р.Ƈ("subset: "+ź.Count)
;ŭ=Ū<IMyProgrammableBlock>("a-thrust-provider",ţ);var Ź=ŧ<IMyMotorStator>(ţ);var Ÿ=new List<IMyProgrammableBlock>();Ǝ.
GetBlocksOfType(Ÿ,Ļ=>Ź.Any(é=>(é.Top!=null)&&é.Top.CubeGrid==Ļ.CubeGrid));Ŭ=Ū<IMyProgrammableBlock>("a-tgp",ţ)??Ÿ.FirstOrDefault(é=>é.
CustomName.Contains("a-tgp"));Ə=Ū<IMyGyro>(ForwardGyroTag,ţ,true);var ŷ=ŧ<IMyShipController>(ţ);ɿ.ģ(ŷ);ý=ŧ<IMyRadioAntenna>(ţ).
FirstOrDefault();Ɣ=ŧ<IMyShipConnector>(ţ).First();Ɠ=ŧ<IMyWarhead>(ţ);ů=ŧ<IMyRemoteControl>(ţ).First();ů.CustomData="";var ü=ŧ<
IMyTimerBlock>(ţ);Ů=new è(ü);Ų=new List<IMyTerminalBlock>();Ų.AddRange(ŧ<IMyThrust>(ţ));Ų.AddRange(ŧ<IMyArtificialMassBlock>(ţ));
string ŵ=Variables.Get<string>("ggen-tag");if(!string.IsNullOrEmpty(ŵ)){var Ŵ=new List<IMyGravityGenerator>();var ų=Ǝ.
GetBlockGroupWithName(ŵ);if(ų!=null)ų.GetBlocksOfType(Ŵ,â=>ţ.Contains(â));foreach(var â in Ŵ)ū.Add(â);Ų.AddRange(Ŵ);}else Ų.AddRange(ŧ<
IMyGravityGenerator>(ţ));Š=new à(ů,Ů,Ž,ŭ,Ə,ý,Ű,this,true);ƒ=new Ǖ(this,Ɛ);Ċ(ApckState.Standby);Š.Ċ(à.ß.æ);}List<IMyTerminalBlock>Ų;ʍ ç;int
ű;public ʍ Ű(){if(ç==null)ç=new ʍ(Ə,Ų);else if((ĵ!=ű)&&(ĵ%60==0)){ű=ĵ;if(Ų.Any(é=>!é.IsFunctional)){Ų.RemoveAll(é=>!é.
IsFunctional);ç=new ʍ(Ə,Ų);}}if(Ų.Any(é=>é is IMyThrust&&(é as IMyThrust)?.MaxEffectiveThrust!=(é as IMyThrust)?.MaxThrust))ç.ʝ();
return ç;}public Vector3D?ǎ;public Vector3D?Ǎ;public bool ǌ(){if(Š.A!=null){if(Ǎ==null){Vector3D Ì;if(ů.TryGetPlanetPosition(
out Ì)){Ǎ=Ì;return true;}}return Ǎ.HasValue;}return false;}public void Ĉ(string ć){ApckState ƃ;if(Enum.TryParse(ć,out ƃ)){Ċ
(ƃ);}}public void Ċ(ApckState ƃ){if(ƒ.Ĉ(ƃ)){Ƒ=ƒ.ƪ();if(ý!=null)ý.CustomName=
$"{Ə.CubeGrid.CustomName}> {ƒ.ƥ().Ư} / {ǅ?.Value?.đ}";}}public void ǋ(ApckState Ʀ,ƻ â){ƒ.ƫ(Ʀ,â);}public ƻ Ǌ(ApckState Ʀ){return ƒ.Ƨ(Ʀ).ò;}public void ǉ(ɻ Ŗ){Р.Ƈ("CreateWP "+
Ŗ.đ);ǘ(Ŗ);}public void ǈ(int ĵ,Action<string>ĉ){this.ĵ=ĵ;Ǆ();var Ǉ=ǅ?.Value;ƻ D;if((Ǉ!=null)&&(ƒ.ƥ().Ư==Ǉ.ʢ))D=Ǉ.ò??Ǌ(Ǉ.ʢ
);else D=Ƒ;Š.I(ĵ,ĉ,D);}LinkedList<ɻ>ǆ=new LinkedList<ɻ>();LinkedListNode<ɻ>ǅ;int ĵ;void Ǆ(){if(ǅ!=null){var Ŗ=ǅ.Value;if(
Ŗ.ʺ(ĵ,Š)){Р.Ƈ($"TFin {Ŗ.đ}");Ǘ();}}}public ɻ Ǚ(){return ǅ?.Value;}public void ǘ(ɻ Ŗ){ǆ.AddFirst(Ŗ);Р.Ƈ(
$"Added {Ŗ.đ}, total: {ǆ.Count}");ǅ=ǆ.First;if(ǅ.Next==null)ǅ.Value.ʷ=ƒ.ƥ().Ư;Ŗ.ģ(Š,ĵ);Ċ(Ŗ.ʢ);}public void Ǘ(){var ǖ=ǅ;var á=ǖ.Value;á.ʹ?.Invoke();if(ǖ.
Next!=null){á=ǖ.Next.Value;ǅ=ǖ.Next;á.ģ(Š,ĵ);ǆ.Remove(ǖ);}else{ǅ=null;ǆ.Clear();Ċ(ǖ.Value.ʷ);}}}class Ǖ{ƕ ǔ;Dictionary<
ApckState,ƣ>Ǔ=new Dictionary<ApckState,ƣ>();public Ǖ(ƕ ǒ,Func<string,Ĕ>Ŏ){ǔ=ǒ;var Ǒ=ǔ.Š;var ǐ=new ƻ{đ="Default"};foreach(var ƃ in
Enum.GetValues(typeof(ApckState))){Ǔ.Add((ApckState)ƃ,new ƣ((ApckState)ƃ,ǐ));}Ǔ[ApckState.Standby].ò=new ƻ{đ="Standby"};Ƥ=Ǔ[
ApckState.Standby];Ǔ[ApckState.Formation].ò=new ƻ{đ="follow formation",ƺ=false,í=()=>Ŏ("wingman"),Ƴ=(Ʋ)=>Ǒ.P.GetPosition()+Ŏ(
"wingman").č.Value.Forward*5000,ƹ=ë=>{var Ǐ=new BoundingSphereD(Ŏ("wingman").č.Value.Translation,30);return ђ.н(Ǒ.P.WorldMatrix,ë
,Ǐ);},ư=()=>Ǒ.P.GetPosition()};Ǔ[ApckState.Brake].ò=new ƻ{đ="reverse",î=true,ƹ=Ʋ=>Ǒ.Ő(-150),Ƴ=(Ʋ)=>Ǒ.Ő(1),ï=true};Ǔ[
ApckState.DockingAwait].ò=new ƻ{đ="awaiting docking",ƺ=false,î=true,í=()=>Ŏ("wingman"),Ƴ=Ʋ=>Ǒ.P.GetPosition()+Ǒ.P.WorldMatrix.
Forward,ƹ=Ʋ=>Ŏ("wingman").ď.HasValue?Ŏ("wingman").ď.Value:Ǒ.P.GetPosition(),ƶ=(ƭ,Ƭ,á,Ô,U)=>{if(Ŏ("docking").ď.HasValue&&(ǔ.Ɣ!=
null)){U.Ċ(ApckState.DockingFinal);}}};Ǔ[ApckState.DockingFinal].ò=new ƻ{Ƴ=Ʋ=>ǔ.Ɣ.GetPosition()-Ŏ("docking").č.Value.Forward
*10000,ƹ=ë=>ë+Ŏ("docking").č.Value.Forward*(IsLargeGrid?1.25f:0.5f),Ʊ=()=>ǔ.Ɣ.WorldMatrix,ư=()=>ǔ.Ɣ.GetPosition(),í=()=>Ŏ
("docking"),ƺ=false,Ƶ=()=>Ǒ.Û,ƶ=(ƭ,Ƭ,á,Ô,U)=>{if((ƭ<20)&&(á.õ.Length()<0.8)&&(ǔ.Ɣ!=null)){ǔ.Ɣ.Connect();if(ǔ.Ɣ.Status==
MyShipConnectorStatus.Connected){U.Ċ(ApckState.Inert);ǔ.Ɣ.OtherConnector.CustomData="";á.Z.DampenersOverride=false;}}}};Ǔ[ApckState.Inert].ǂ=
ƃ=>ǒ.Š.Ċ(à.ß.Ý);Ǔ[ApckState.Inert].ǃ=ƃ=>ǒ.Š.Ċ(à.ß.æ);}public void ƫ(ApckState Ʀ,ƻ D){Ǔ[Ʀ].ò=D;}public ƻ ƪ(){return Ƥ.ò;}
public bool Ĉ(ApckState Ʀ){if(Ʀ==Ƥ.Ư)return true;var Ŗ=Ƨ(Ʀ);if(Ŗ!=null){var á=Ʃ.FirstOrDefault(é=>é.ƾ.Ư==Ƥ.Ư||é.ƽ.Ư==Ƥ.Ư||é.ƽ.
Ư==Ʀ||é.ƾ.Ư==Ʀ);if(á!=null){if(!(á.ƾ==Ƥ&&á.ƽ==Ŗ)){return false;}}var ƨ=Ƥ;Р.Ƈ($"{ƨ.Ư} -> {Ŗ.Ư}");Ƥ=Ŗ;ƨ.ǃ?.Invoke(Ƥ.Ư);á?.Ƽ
?.Invoke();Ŗ.ǂ?.Invoke(Ŗ.Ư);return true;}return false;}public ƣ Ƨ(ApckState Ʀ){return Ǔ[Ʀ];}public ƣ ƥ(){return Ƥ;}ƣ Ƥ;
List<ƿ>Ʃ=new List<ƿ>();public class ƣ{public ApckState Ư;public Action<ApckState>ǃ;public Action<ApckState>ǂ;public ƻ ò;
public ƣ(ApckState Ʀ,ƻ â,Action<ApckState>ǁ=null,Action<ApckState>ǀ=null){ò=â;Ư=Ʀ;ǃ=ǁ;ǂ=ǀ;}}public class ƿ{public ƣ ƾ;public ƣ
ƽ;public Action Ƽ;}}class ƻ{public string đ="Default";public bool ƺ=true;public Func<Vector3D,Vector3D>ƹ{get;set;}public
Func<Vector3D,Vector3D>Ƹ{get;set;}public Func<Vector3D>Ʒ{get;set;}public Action<double,double,à,ƻ,ƕ>ƶ{get;set;}public Func<
Vector3D>Ƶ;public bool ƴ=false;public Func<Vector3D,Vector3D>Ƴ=(Ʋ)=>Ʋ;public Func<MatrixD>Ʊ;public Func<Vector3D>ư;public bool Ø
;public float?ð;public bool Á=false;public bool ï=false;public bool î;public Func<Ĕ>í;public static ƻ ì(Vector3D ë,string
ã,Func<Vector3D>ê=null){return new ƻ(){đ=ã,î=true,ƹ=é=>ë,Ƴ=é=>ê?.Invoke()??ë};}}class è{Dictionary<string,IMyTimerBlock>å
=new Dictionary<string,IMyTimerBlock>();List<IMyTimerBlock>ç;public è(List<IMyTimerBlock>å){ç=å;}public bool ä(string ã){
IMyTimerBlock â;if(!å.TryGetValue(ã,out â)){â=ç.FirstOrDefault(á=>á.CustomName.Contains(ã));if(â!=null)å.Add(ã,â);else return false;}
â.GetActionWithName("TriggerNow").Apply(â);return true;}}class à{public enum ß{Þ=0,Ý,æ}public bool Ü;ß ñ=ß.Þ;public void
Ċ(ß Ć){if(Ć==ß.æ)ą();else if(Ć==ß.Ý)Ą(false);else if(Ć==ß.Þ)Ą();ñ=Ć;}public void Ĉ(string ć){ß Ć;if(Enum.TryParse(ć,out Ć
))Ċ(Ć);}void ą(){Z.DampenersOverride=false;}void Ą(bool ă=true){Q.GyroOverride=false;V().ʄ();Z.DampenersOverride=ă;}
public à(IMyRemoteControl Ă,è ā,IMyIntergridCommunicationSystem Ā,IMyProgrammableBlock ÿ,IMyGyro þ,IMyTerminalBlock ý,Func<ʍ>ü
,ƕ û,bool ú){Z=Ă;X=Ā;Q=þ;µ=ý;Y=ā;W=ÿ;V=ü;U=û;Ü=ú;}Vector3D ù;public string ø;public Vector3D ö{get;private set;}public
Vector3D õ{get;private set;}Vector3D ô=Vector3D.Zero;public double ó{get;private set;}public ƻ ò{get;private set;}public
Vector3D Û{get{return Z.GetShipVelocities().LinearVelocity;}}Vector3D?º;public Vector3D?A{get{return(º!=Vector3D.Zero)?º:null;}}
Vector3D ª{get;set;}public Vector3D w{get;set;}public IMyRemoteControl Z;public è Y{get;private set;}
IMyIntergridCommunicationSystem X;IMyProgrammableBlock W;Func<ʍ>V;ƕ U;int S;IMyGyro Q;IMyTerminalBlock µ;public IMyTerminalBlock P{get{return Q;}}
public Vector3D N;public Vector3D M;public Vector3D L;public Vector3D K;public Vector3D J;public void I(int H,Action<string>F,
ƻ D){var B=H-S;S=H;if(B>0)w=(Û-ª)*60f/B;ª=Û;º=Z.GetNaturalGravity();ò=D;MyPlanetElevation O=new MyPlanetElevation();
double À;Z.TryGetPlanetElevation(O,out À);Vector3D Ì;Z.TryGetPlanetPosition(out Ì);Func<Vector3D>Ú=null;bool Ø=false;float?Ö=
null;Ĕ Õ=null;switch(ñ){case ß.Þ:return;case ß.æ:try{var Ô=D;if(Ô==null){Ċ(ß.Þ);return;}if(!Ü&&!Ô.ƴ)return;var Ó=Vector3D.
TransformNormal(Z.GetShipVelocities().AngularVelocity,MatrixD.Transpose(P.WorldMatrix));Ó=new Vector3D(Math.Abs(Ó.X),Math.Abs(Ó.Y),Math
.Abs(Ó.Z));var Ò=(Ó-ô)/Dt;ô=Ó;Ú=Ô.Ƶ;var Ñ=Ô.Ƴ;Ø=Ô.Ø;Ö=Ô.ð;if(Ô.í!=null)Õ=Ô.í();if(Ô.î||((Õ!=null)&&Õ.ď.HasValue)){
Vector3D Ð;Vector3D?Ï=null;if((Õ!=null)&&(Õ.ď.HasValue)){Ð=Õ.ď.Value;if(Ï.IsValid())Ï=Õ.Û;else Р.Ƈ("Ivalid targetVelocity");}
else Ð=Vector3D.Zero;if(Ô.Ƹ!=null)Ð=Ô.Ƹ(Ð);var Î=(Ô.ư!=null)?Ô.ư():P.GetPosition();if((Ú!=null)&&(Ï.HasValue)&&(Ï.Value.
Length()>0)){Vector3D Ù=Ð;Vector3D Í=ђ.Е(Î,Û,Ù,Ï.Value,Ú(),Ô.Á);if((Ð-Í).Length()<2500){Ð=Í;}M=Í;}N=Ð;if(Ô.ƹ!=null)K=Ô.ƹ(Ð);
else K=Ð;double Ë=(Ð-Î).Length();double Ê=(K-Î).Length();if(DbgIgc!=0)ø=$"origD: {Ë:f1}\nshiftD: {Ê:f1}";Ô.ƶ?.Invoke(Ë,Ê,
this,Ô,U);Q.GyroOverride=true;J=Ð;if(Ñ!=null)J=Ñ(J);if((Variables.Get<bool>("hold-thrust-on-rotation")&&(ù.Length()>1))||λ.ƅ
.φ("suppress-transition-control")){Р.Ƌ($"prev cv: {ù.Length():f2} HOLD");ċ(P.WorldMatrix.Translation,P.WorldMatrix.
Translation,false,null,null,false);}else{Р.Ƌ($"prev cv: {ù.Length():f2} OK");ċ(Î,K,Ø,Ï,Ö,Ô.ï);}var É=(Ô.Ʊ!=null)?Ô.Ʊ():P.
WorldMatrix;if(!Ü&&(L!=P.WorldMatrix.Translation))J=L;Vector3D È=Vector3D.Zero;var Ç=J-P.WorldMatrix.Translation;if(Ç!=Vector3D.
Zero){var Æ=Vector3D.Normalize(Ç);var Å=MatrixD.CreateFromDir(Æ);Vector3D Ä=Vector3D.Zero;var Ã=Ô.Ʒ?.Invoke()??Vector3D.Zero
;È=ђ.ж(Æ,É,P.WorldMatrix,Ã,ref Ä);Ä.Z=0;õ=Ä;ö=õ-ù;ù=õ;ó=Vector3D.Dot(Æ,É.Forward);}if(!λ.ƅ.φ("suppress-gyro-control"))ђ.ɲ
(Q,(Vector3)È,ö,ô);}else{Q.GyroOverride=false;if(λ.ƅ.φ("damp-when-idle"))ċ(P.WorldMatrix.Translation,P.WorldMatrix.
Translation,false,null,0,false);else ċ(P.WorldMatrix.Translation,P.WorldMatrix.Translation,false,null,null,false);}}catch(Exception
ex){µ.CustomName+="HC Exception! See remcon cdata or PB screen";var Â=Z;var ĉ=
$"HC EPIC FAIL\nNTV:{Õ?.đ}\nBehavior:{ò.đ}\n{ex}";Â.CustomData+=ĉ;Р.Ƈ(ĉ);Ċ(ß.Þ);throw ex;}finally{Р.Ƃ();}break;}}void ċ(Vector3D Ş,Vector3D Ō,bool Ø,Vector3D?Ŋ,float?Ö,
bool ŉ){L=Ō;if(ñ!=ß.æ)return;var ň=Ō;var Ň=Q.WorldMatrix;Ň.Translation=Ş;var Ç=Ō-Ň.Translation;var ņ=MatrixD.Transpose(Ň);
var Ņ=Vector3D.TransformNormal(Û,ņ);var ń=Ņ;if(!Ü&&(Ç!=Vector3D.Zero)){V().ɷ().ɱ(1f*Math.Max(0.2f,ó));if(Û!=Vector3D.Zero)L
=Vector3D.Normalize(Vector3D.Reflect(Û,Ç))+Ş+Vector3D.Normalize(Ç)*Û.Length()*0.5f;return;}float ŋ=Z.CalculateShipMass().
PhysicalMass;BoundingBoxD Ń=V().ʠ(ŋ);if(Ń.Volume==0)return;Vector3D ł=Vector3D.Zero;if(A!=null){ł=Vector3D.TransformNormal(A.Value,ņ
);Ń+=-ł;}Vector3D Ł=Vector3D.Zero;Vector3D ŀ=Vector3D.Zero;Vector3D Ŀ=new Vector3D();if(Ç.Length()>double.Epsilon){
Vector3D ľ=Vector3D.TransformNormal(Ç,ņ);RayD Ľ=new RayD(-ľ*(MaxAccelInProximity?1000:1),Vector3D.Normalize(ľ));RayD ļ=new RayD(
ľ*(MaxBrakeInProximity?1000:1),Vector3D.Normalize(-ľ));var Ļ=ļ.Intersects(Ń);var ĺ=Ľ.Intersects(Ń);if(!Ļ.HasValue||!ĺ.
HasValue)throw new InvalidOperationException("Not enough thrust to compensate for gravity");var Ĺ=ļ.Position+(Vector3D.Normalize
(ļ.Direction)*Ļ.Value);var Ð=Ľ.Position+(Vector3D.Normalize(Ľ.Direction)*ĺ.Value);var ĸ=Ĺ.Length();Vector3D ō=Vector3D.
Reject(Ņ,Vector3D.Normalize(ľ));if(Ŋ.HasValue){var ş=Vector3D.TransformNormal(Ŋ.Value,ņ);ń=Ņ-ş;ō=Vector3D.Reject(ń,Vector3D.
Normalize(ľ));}else{ń-=ō;}var ŝ=Vector3D.Dot(ń,Vector3D.Normalize(ľ));bool Ŝ=ŝ>0;bool ś=true;var Ś=Math.Pow(Math.Max(0,ŝ),2)/(2*ĸ
*StoppingPowerQuotient);var ř=Ç.Length()-Ś;if(DbgIgc!=0){ø+=$"\nSTP: {Ś:f2}\nRelSP: {ŝ:f2}";}if(Ŝ){if(Ś>Ç.Length())ś=
false;else if(MoreRejectDampening)ō/=Dt;}if(ŉ||ś){if(Ö.HasValue&&(Vector3D.Dot(Vector3D.Normalize(Ç),Û)>=Ö)){Ŀ=Ĺ;Ŀ*=(ŝ-Ö.
Value)/ĸ;}else Ŀ=Ð;}else Ŀ=Ĺ;if(ś){var Ř=Vector3D.Dot(Vector3D.Normalize(Ç),Û);if(Ř>MAX_SP-0.001){Ŀ=Vector3D.Zero;}}ŀ=Ŀ;Ł=ō;
if(ō.IsValid())Ŀ+=ō;}else if(Ö.HasValue&&(Ö==0)){Ŀ+=Ņ/(MoreRejectDampening?Dt:1);}if(A!=null){Ŀ+=ł;}Ŀ-=ɿ.ɽ(P.WorldMatrix)*
1000;if(Ŀ!=Vector3D.Zero){L=Vector3D.TransformNormal(Ŀ,Ň)+Ş;if(DbgIgc!=0){var ŗ=new List<MyTuple<Vector3D,Vector3D,Vector4>>
();var á=Color.SeaGreen;á.A=40;ŗ.Add(new MyTuple<Vector3D,Vector3D,Vector4>(Ş,L,á));var Ŗ=new MyTuple<string,Vector2,
Vector3D,Vector3D,float,string>("Circle",Vector2.One*4,L,Vector3D.Zero,1f,Ŀ.Length().ToString("f2"));X.SendUnicastMessage(DbgIgc
,"draw-projection",Ŗ);á=Color.Blue;á.A=40;ŗ.Add(new MyTuple<Vector3D,Vector3D,Vector4>(Ş,Vector3D.TransformNormal(Ł,Ň)+Ş,
á));á=Color.Red;á.A=40;ŗ.Add(new MyTuple<Vector3D,Vector3D,Vector4>(Ş,Vector3D.TransformNormal(ŀ,Ň)+Ş,á));var ŕ=new RayD(
Ŀ*1000,Vector3D.Normalize(-Ŀ));var ĺ=ŕ.Intersects(Ń);if(ĺ.HasValue){var Œ=ŕ.Position+(Vector3D.Normalize(ŕ.Direction)*ĺ.
Value);var ę=Vector3D.TransformNormal(Œ,Ň)+Ş;var ő=new MyTuple<string,Vector2,Vector3D,Vector3D,float,string>("Circle",
Vector2.One*4,ę,Vector3D.Zero,1f,Œ.Length().ToString("f2"));X.SendUnicastMessage(DbgIgc,"draw-projection",ő);}var Ŕ=new RayD(-Ŀ
*1000,Vector3D.Normalize(Ŀ));var œ=Ŕ.Intersects(Ń);if(œ.HasValue){var Œ=Ŕ.Position+(Vector3D.Normalize(Ŕ.Direction)*œ.
Value);var ę=Vector3D.TransformNormal(Œ,Ň)+Ş;var ő=new MyTuple<string,Vector2,Vector3D,Vector3D,float,string>("Circle",
Vector2.One*4,ę,Vector3D.Zero,1f,Œ.Length().ToString("f2"));X.SendUnicastMessage(DbgIgc,"draw-projection",ő);}X.
SendUnicastMessage(DbgIgc,"draw-lines",ŗ.ToImmutableArray());}}Ŀ.Y*=-1;V().ɲ(Ŀ,ŋ);}public Vector3D Ő(float ŏ){return P.GetPosition()+P.
WorldMatrix.Forward*ŏ;}}Ĕ Ŏ(string ğ){Ĕ Â;if(ě.TryGetValue(ğ,out Â))return Â;throw new InvalidOperationException("No TV named "+ğ);
}void ķ(string ğ,Ĕ Č){if(!ě.ContainsKey(ğ))ě.Add(ğ,Č);else ě[ğ]=Č;ȸ.ȶ++;}void Ġ(string ğ,MyTuple<MyTuple<string,long,long
,byte,byte>,Vector3D,Vector3D,MatrixD,BoundingBoxD>Ğ){ě[ğ].Ħ(Ğ,ο);ȸ.ȶ++;}void ĝ(){foreach(var é in ě.Values.Where(Ĝ=>Ĝ.ď.
HasValue)){Р.Ƌ(é.đ+((é.ď.Value==Vector3D.Zero)?" Zero!":" OK"));é.ı(ο);}}Dictionary<string,Ĕ>ě=new Dictionary<string,Ĕ>();struct
Ě{public Vector3D?ę;public Vector3D?Ę;public MatrixD?ġ;public BoundingBoxD?ė;public MyDetectedEntityType?ĕ;}class Ĕ{
public long ē;int Ē;public string đ;public long Đ;public Vector3D?ď{get;private set;}public Vector3D?Û;public Vector3D?Ď;
public MatrixD?č;public BoundingBoxD?Ė;public int?Ģ=60;public MyDetectedEntityType?Į{get;set;}public delegate void Ķ();public
event Ķ Ĵ;public Ĕ(int Ē,string ã){this.Ē=Ē;đ=ã;}public void ĳ(Vector3D ę,long Ĳ){ď=ę;ē=Ĳ;}public void ı(int İ){if((ē!=0)&&Ģ.
HasValue&&(İ-ē>Ģ.Value))ʏ();}public void į(int ĵ,int Ē){if((Û.HasValue)&&(Û.Value.Length()>double.Epsilon)&&(ĵ-ē)>0){ď+=Û*(ĵ-ē)*
Ē/60;ȸ.ƌ++;}}public enum ĭ:byte{Ĭ=1,ī=2,Ī=4}bool ĩ(ĭ Ĩ,ĭ ħ){return(Ĩ&ħ)==ħ;}public void Ħ(MyTuple<MyTuple<string,long,
long,byte,byte>,Vector3D,Vector3D,MatrixD,BoundingBoxD>ĥ,int Ĥ){var Ʈ=ĥ.Item1;Đ=Ʈ.Item2;Į=(MyDetectedEntityType)Ʈ.Item4;ĭ ǚ=
(ĭ)Ʈ.Item5;ĳ(ĥ.Item2,Ĥ);if(ĩ(ǚ,ĭ.Ĭ)){var ʬ=ĥ.Item3;if(!Û.HasValue)Û=ʬ;Ď=(ʬ-Û.Value)*60/Ē;Û=ʬ;}if(ĩ(ǚ,ĭ.ī))č=ĥ.Item4;if(ĩ(
ǚ,ĭ.Ī))Ė=ĥ.Item5;ȸ.ȶ++;}public static Ĕ ʔ(MyTuple<MyTuple<string,long,long,byte,byte>,Vector3D,Vector3D,MatrixD,
BoundingBoxD>ĥ,Func<string[],Ě>ʒ,int Ĥ){var Ŗ=new Ĕ(1,ĥ.Item1.Item1);Ŗ.Ħ(ĥ,Ĥ);return Ŗ;}public MyTuple<MyTuple<string,long,long,byte
,byte>,Vector3D,Vector3D,MatrixD,BoundingBoxD>ʑ(){var ʐ=0|(Û.HasValue?1:0)|(č.HasValue?2:0)|(Ė.HasValue?4:0);var é=new
MyTuple<MyTuple<string,long,long,byte,byte>,Vector3D,Vector3D,MatrixD,BoundingBoxD>(new MyTuple<string,long,long,byte,byte>(đ,Đ
,DateTime.Now.Ticks,(byte)MyDetectedEntityType.LargeGrid,(byte)ʐ),ď.Value,Û??Vector3D.Zero,č??MatrixD.Identity,Ė??new
BoundingBoxD());return é;}public void ʏ(){ď=null;Û=null;č=null;Ė=null;var ʎ=Ĵ;if(ʎ!=null)Ĵ();ȸ.ȵ++;}}class ʍ{List<ʂ>ʓ;List<ʂ>ʌ;List<
ʂ>ʋ;List<ʂ>ʊ;List<ʂ>ʉ;List<ʂ>ʈ;List<ʂ>ʇ;public double[]ʆ=new double[6];bool ʅ;public void ʄ(){if(!ʅ){ʌ.ForEach(Ȋ=>Ȋ.ʀ());
ʋ.ForEach(Ȋ=>Ȋ.ʀ());ʊ.ForEach(Ȋ=>Ȋ.ʀ());ʉ.ForEach(Ȋ=>Ȋ.ʀ());ʈ.ForEach(Ȋ=>Ȋ.ʀ());ʇ.ForEach(Ȋ=>Ȋ.ʀ());ʅ=true;}}public
BoundingBoxD ʠ(float ŋ){Vector3D ʟ=new Vector3D(-ʆ[5],-ʆ[3],-ʆ[1])/ŋ;Vector3D ʞ=new Vector3D(ʆ[4],ʆ[2],ʆ[0])/ŋ;return new
BoundingBoxD(ʟ,ʞ);}public void ʝ(){ʆ[0]=ɷ().ɯ();ʆ[1]=ɩ().ɯ();ʆ[2]=ɶ().ɯ();ʆ[3]=ɵ().ɯ();ʆ[4]=ɳ().ɯ();ʆ[5]=ɴ().ɯ();}public ʍ(
IMyTerminalBlock ʜ,List<IMyTerminalBlock>ʛ){MatrixD ʚ=ʜ.WorldMatrix;Func<Vector3D,List<ʂ>>ʙ=ʕ=>{var Â=ʛ.Where(â=>â is IMyThrust&&ʕ==â.
WorldMatrix.Forward).Select(é=>é as IMyThrust).ToList();return Â.Select(Ŗ=>new ʃ(Ŗ)).Cast<ʂ>().ToList();};ʌ=ʙ(ʚ.Backward);ʋ=ʙ(ʚ.
Forward);ʊ=ʙ(ʚ.Down);ʉ=ʙ(ʚ.Up);ʈ=ʙ(ʚ.Left);ʇ=ʙ(ʚ.Right);var ʘ=ʛ.Where(â=>â is IMyArtificialMassBlock).Cast<
IMyArtificialMassBlock>().ToList();var ʗ=ʛ.Where(â=>â is IMyGravityGenerator).Cast<IMyGravityGenerator>().ToList();Func<Vector3D,bool,List<ʂ>>
ʖ=(ʕ,ɨ)=>{var Ŵ=ʗ.Where(â=>ʕ==â.WorldMatrix.Up);return Ŵ.Select(ë=>new ɬ(ë,ʘ,ɨ)).Cast<ʂ>().ToList();};ʌ.AddRange(ʖ(ʚ.
Forward,true));ʋ.AddRange(ʖ(ʚ.Forward,false));ʌ.AddRange(ʖ(ʚ.Backward,false));ʋ.AddRange(ʖ(ʚ.Backward,true));ʊ.AddRange(ʖ(ʚ.Up,
true));ʉ.AddRange(ʖ(ʚ.Up,false));ʊ.AddRange(ʖ(ʚ.Down,false));ʉ.AddRange(ʖ(ʚ.Down,true));ʈ.AddRange(ʖ(ʚ.Right,true));ʇ.
AddRange(ʖ(ʚ.Right,false));ʈ.AddRange(ʖ(ʚ.Left,false));ʇ.AddRange(ʖ(ʚ.Left,true));ʝ();}public ʍ ɷ(){ʓ=ʌ;return this;}public ʍ ɩ(
){ʓ=ʋ;return this;}public ʍ ɶ(){ʓ=ʊ;return this;}public ʍ ɵ(){ʓ=ʉ;return this;}public ʍ ɴ(){ʓ=ʈ;return this;}public ʍ ɳ()
{ʓ=ʇ;return this;}public void ɲ(Vector3D Ĝ,float ŋ){ʅ=false;Func<ʂ,bool>ɰ=Ȋ=>!(Ȋ is ɬ);ɩ().ɱ(-Ĝ.Z/ʆ[1]*ŋ);ɷ().ɱ(Ĝ.Z/ʆ[0]*
ŋ);ɵ().ɱ(-Ĝ.Y/ʆ[3]*ŋ);ɶ().ɱ(Ĝ.Y/ʆ[2]*ŋ);ɴ().ɱ(-Ĝ.X/ʆ[5]*ŋ);ɳ().ɱ(Ĝ.X/ʆ[4]*ŋ);}public bool ɱ(double Ơ,Func<ʂ,bool>ɰ=null){
if(ʓ!=null){Ơ=Math.Min(1,Math.Abs(Ơ))*Math.Sign(Ơ);foreach(var ɭ in ɰ==null?ʓ:ʓ.Where(ɰ)){ɭ.ɱ(Ơ);}}ʓ=null;return true;}
public float ɯ(){float ɮ=0;if(ʓ!=null){foreach(var ɭ in ʓ){ɮ+=ɭ.ʁ();}}ʓ=null;return ɮ;}}class ɬ:ʂ{IMyGravityGenerator Ŵ;List<
IMyArtificialMassBlock>ɫ;bool ɪ;public ɬ(IMyGravityGenerator Ŵ,List<IMyArtificialMassBlock>ɫ,bool ɪ){this.Ŵ=Ŵ;this.ɫ=ɫ;this.ɪ=ɪ;}public void ɱ
(double ɾ){if(ɾ>=0)Ŵ.GravityAcceleration=(float)(ɪ?-ɾ:ɾ)*G;}public void ʀ(){Ŵ.GravityAcceleration=0;}public float ʁ(){
return ɫ.Count*50000*G;}}class ʃ:ʂ{IMyThrust Ŗ;public ʃ(IMyThrust Ŗ){this.Ŗ=Ŗ;}public void ɱ(double ɾ){if(ɾ<=0)Ŗ.
ThrustOverride=0.00000001f;else Ŗ.ThrustOverride=(float)ɾ*Ŗ.MaxThrust;}public void ʀ(){Ŗ.ThrustOverride=0;Ŗ.Enabled=true;}public float
ʁ(){return Ŗ.MaxEffectiveThrust;}}interface ʂ{void ɱ(double ɾ);float ʁ();void ʀ();}static class ɿ{public static List<
IMyShipController>ŷ;public static void ģ(List<IMyShipController>á){if(ŷ==null)ŷ=á;}public static Vector3 ɽ(MatrixD ɼ){Vector3 Ȱ=new
Vector3();if(λ.ƅ.φ("ignore-user-thruster"))return Ȱ;var á=ŷ.Where(é=>é.IsUnderControl).FirstOrDefault();if(á!=null&&(á.
MoveIndicator!=Vector3.Zero))return(Vector3)Vector3D.TransformNormal(á.MoveIndicator,ɼ*MatrixD.Transpose(á.WorldMatrix));return Ȱ;}}
class ɻ{public string đ;public Vector3D?ɺ;public Func<Vector3D>ɹ;double?ʡ;public int?ɸ;public ApckState ʢ=ApckState.CwpTask;
public ƻ ò;public Action ʹ;int ʸ;public ApckState ʷ;public ɻ(string ã,ƻ D,int?ʶ=null){ò=D;đ=ã;ɸ=ʶ;}public ɻ(string ã,ApckState
Ʀ,int?ʶ=null){ʢ=Ʀ;đ=ã;ɸ=ʶ;}public void ģ(à Š,int ĵ){if(ʸ==0)ʸ=ĵ;Š.Y.ä(đ+".OnStart");}public static ɻ ʵ(string ã,Vector3D
ë,ƻ D){var Ŗ=new ɻ(ã,D);Ŗ.ʡ=0.5;Ŗ.ɺ=ë;return Ŗ;}public static ɻ ʴ(string ã,Func<Vector3D>ʳ,ƻ D){var Ŗ=new ɻ(ã,D);Ŗ.ʡ=0.5;
Ŗ.ɹ=ʳ;return Ŗ;}public bool ʺ(int ĵ,à Š){if(ɸ.HasValue&&(ĵ-ʸ>ɸ)){return true;}if(ʡ.HasValue){Vector3D ë;var ǖ=ò.ư?.Invoke
()??Š.P.GetPosition();if(ɹ!=null)ë=ɹ();else ë=ɺ.Value;if((ǖ-ë).Length()<ʡ)return true;}return false;}}ɻ ˁ;void ǉ(string[]
ˀ){ʩ();var ʿ=Ɩ;var Ǒ=ʿ.Š;var ʾ=Ǒ.P.GetPosition();var ʽ=ˀ[2].Split(',').ToDictionary(ƃ=>ƃ.Split('=')[0],ƃ=>ƃ.Split('=')[1]
);var D=new ƻ(){đ="Deserialized Behavior",î=true,ƺ=false};ˁ=new ɻ("twp",D);float ʉ=1;var ʼ=ˀ.Take(6).Skip(1).ToArray();
var ę=new Vector3D(double.Parse(ʼ[2]),double.Parse(ʼ[3]),double.Parse(ʼ[4]));Func<Vector3D,Vector3D>ʻ=ë=>ę;ˁ.ɺ=ę;Vector3D?Ť
=null;if(ʽ.ContainsKey("AimNormal")){var Ĝ=ʽ["AimNormal"].Split(';');Ť=new Vector3D(double.Parse(Ĝ[0]),double.Parse(Ĝ[1])
,double.Parse(Ĝ[2]));}if(ʽ.ContainsKey("UpNormal")){var Ĝ=ʽ["UpNormal"].Split(';');var Ã=Vector3D.Normalize(new Vector3D(
double.Parse(Ĝ[0]),double.Parse(Ĝ[1]),double.Parse(Ĝ[2])));D.Ʒ=()=>Ã;}if(ʽ.ContainsKey("Name"))ˁ.đ=ʽ["Name"];if(ʽ.ContainsKey(
"FlyThrough"))D.ï=true;if(ʽ.ContainsKey("SpeedLimit"))D.ð=float.Parse(ʽ["SpeedLimit"]);if(ʽ.ContainsKey("TriggerDistance"))ʉ=float.
Parse(ʽ["TriggerDistance"]);if(ʽ.ContainsKey("PosDirectionOverride")&&(ʽ["PosDirectionOverride"]=="Forward")){if(Ť.HasValue){
ʻ=ë=>ʾ+Ť.Value*((Ǒ.P.GetPosition()-ʾ).Length()+5);}else ʻ=ë=>Ǒ.Ő(50000);}if(ˀ.Length>6){D.ƶ=(ƭ,ʲ,Š,Ô,U)=>{if(ʲ<ʉ){ʩ();є.Ѿ
.ρ(ˀ[7],ˀ.Skip(6).ToArray());}};}if(ʽ.ContainsKey("Ng")){Func<MatrixD>ʕ=()=>Ǒ.P.WorldMatrix;if(ʽ["Ng"]=="Down")D.Ʊ=()=>
MatrixD.CreateFromDir(Ǒ.P.WorldMatrix.Down,Ǒ.P.WorldMatrix.Forward);if(ʿ.ǌ()&&!ʽ.ContainsKey("IgG")){D.Ƴ=ë=>ʿ.Ǎ.Value;D.ƹ=ë=>
Vector3D.Normalize(ʻ(ë)-ʿ.Ǎ.Value)*(ʾ-ʿ.Ǎ.Value).Length()+ʿ.Ǎ.Value;}else{if(Ť.HasValue){D.Ƴ=ë=>Ǒ.P.GetPosition()+Ť.Value*1000;}
else D.Ƴ=ë=>Ǒ.P.GetPosition()+(D.Ʊ??ʕ)().Forward*1000;}}if(ʽ.ContainsKey("TransformChannel")){Func<Vector3D,Vector3D>ʪ=ë=>
Vector3D.Transform(ę,Ŏ(ʽ["TransformChannel"]).č.Value);D.Ƹ=ʪ;ˁ.ɹ=()=>Vector3D.Transform(ę,Ŏ(ʽ["TransformChannel"]).č.Value);D.Á=
true;D.í=()=>Ŏ(ʽ["TransformChannel"]);D.Ƶ=()=>Ǒ.Û;D.ƹ=null;}else D.ƹ=ʻ;ˁ.ʢ=ApckState.CwpTask;ʿ.ǋ(ˁ.ʢ,D);ʿ.ǘ(ˁ);}void ʩ(){if(
ˁ!=null){if(Ɩ.Ǚ()==ˁ)Ɩ.Ǘ();ˁ=null;}}ʨ ʫ;class ʨ{List<IMyShipConnector>ʦ;Dictionary<IMyShipConnector,Vector3D>ʥ=new
Dictionary<IMyShipConnector,Vector3D>();public ʨ(List<IMyShipConnector>ʤ,PersistentState š,IMyGridTerminalSystem Ũ){ʦ=ʤ;ʦ.ForEach(
é=>ʥ.Add(é,é.GetPosition()));}public void ʣ(List<IMyTerminalBlock>â){foreach(var é in â){var ʧ=é.CustomName.Split('/')[1]
;var ț=ʧ.ToCharArray();var ʭ=new Ȟ();ʭ.ț=ț;ʭ.Ț=é.Position;ȟ.Add(ʭ);}}void ʱ(Ȟ Ť){var á=string.Concat(Ť.ț);Р.Ƈ("checkin "+
á);foreach(var ʰ in ȟ.Where(é=>é.ȝ==null&&é.Ȝ.Count==0&&(é!=Ť)&&é.ț.Any(Ŗ=>á.Contains(Ŗ)))){Ť.Ȝ.Add(ʰ);ʰ.ȝ=Ť;ʱ(ʰ);}}
public void ǈ(IMyIntergridCommunicationSystem ĺ,int Ŗ){Р.Ƌ("Navmesh: "+ȟ.Count);var ŗ=new List<MyTuple<Vector3D,Vector3D,
Vector4>>();foreach(var Ť in ȟ.Where(é=>é.ȝ!=null)){var ų=ʦ.First().CubeGrid;ŗ.Add(new MyTuple<Vector3D,Vector3D,Vector4>(ų.
GridIntegerToWorld(Ť.Ț),ų.GridIntegerToWorld(Ť.ȝ.Ț),Color.SeaGreen.ToVector4()));}ĺ.SendUnicastMessage(DbgIgc,"draw-lines",ŗ.
ToImmutableArray());foreach(var ƃ in Ȃ)Р.Ƌ(ƃ+" awaits docking");foreach(var ƃ in ȁ)Р.Ƌ(ƃ+" awaits dep");if(Ȃ.Any()){var ʯ=ʦ.
FirstOrDefault(ƭ=>(string.IsNullOrEmpty(ƭ.CustomData)||(ƭ.CustomData==Ȃ.Peek().ToString()))&&(ƭ.Status==MyShipConnectorStatus.
Unconnected));if(ʯ!=null){var Ȇ=Ȃ.Dequeue();ʯ.CustomData=Ȇ.ToString();var ɨ=MatrixD.Transpose(ʯ.WorldMatrix);var Ȋ=Ȁ(ȃ[Ȇ]).Reverse(
).Select(é=>Vector3D.TransformNormal(é-ʯ.GetPosition(),ɨ)).ToImmutableArray();Р.Ƈ($"Sent {Ȋ.Length}-node approach path");
ĺ.SendUnicastMessage(Ȇ,"apck.docking.approach",Ȋ);}}if(ȁ.Any()){foreach(var ƃ in ȁ){Р.Ƌ(ƃ+" awaits departure");}var Â=ȁ.
Peek();var ʮ=ʦ.FirstOrDefault(ƭ=>ƭ.CustomData==Â.ToString());if(ʮ!=null){ȁ.Dequeue();var ɨ=MatrixD.Transpose(ʮ.WorldMatrix);
var Ȋ=Ȁ(ȃ[Â]).Select(é=>Vector3D.TransformNormal(é-ʮ.GetPosition(),ɨ)).ToImmutableArray();Р.Ƈ(
$"Sent {Ȋ.Length}-node departure path");ĺ.SendUnicastMessage(Â,"apck.depart.approach",Ȋ);}}foreach(var ƭ in ʦ.Where(ƭ=>!string.IsNullOrEmpty(ƭ.CustomData))){
long Ȇ;if(long.TryParse(ƭ.CustomData,out Ȇ)){Р.Ƌ($"Channeling DV to {Ȇ}");var é=new Ĕ(1,"docking");var ȉ=ƭ.WorldMatrix;é.ĳ(ȉ
.Translation+ȉ.Forward*(ƭ.CubeGrid.GridSizeEnum==MyCubeSize.Large?1.25:0.5),Ŗ);if(ʥ[ƭ]!=Vector3D.Zero)é.Û=(ƭ.GetPosition(
)-ʥ[ƭ])/Dt;ʥ[ƭ]=ƭ.GetPosition();é.č=ȉ;var Ȉ=é.ʑ();ĺ.SendUnicastMessage(Ȇ,"apck.ntv.update",Ȉ);}}}public void ȇ(string Ȇ){
ʦ.First(é=>é.CustomData==Ȇ).CustomData="";}public void ȅ(long Ȇ,Vector3D ƭ,bool Ȅ=false){if(Ȅ){if(!ȁ.Contains(Ȇ))ȁ.
Enqueue(Ȇ);}else{if(!Ȃ.Contains(Ȇ))Ȃ.Enqueue(Ȇ);}ȃ[Ȇ]=ƭ;}Dictionary<long,Vector3D>ȃ=new Dictionary<long,Vector3D>();Queue<long>
Ȃ=new Queue<long>();Queue<long>ȁ=new Queue<long>();public IEnumerable<Vector3D>Ȁ(Vector3D ǿ){var ų=ʦ.First().CubeGrid;var
Ť=ȟ.Where(é=>é.Ȝ.Count==0).OrderBy(é=>(ų.GridIntegerToWorld(é.Ț)-ǿ).LengthSquared()).FirstOrDefault();if(Ť!=null){var á=Ť
;do{yield return ų.GridIntegerToWorld(á.Ț);á=á.ȝ;}while(á!=null);}}public Vector3D Ǿ(){return ʦ.First().WorldMatrix.
Forward;}public List<IMyShipConnector>ȋ(){return ʦ;}public List<Ȟ>ȟ=new List<Ȟ>();public class Ȟ{public Ȟ ȝ;public List<Ȟ>Ȝ=new
List<Ȟ>();public char[]ț;public Vector3I Ț;}}Ș ș;class Ș{Vector2 ȗ;List<ɥ>Ȗ=new List<ɥ>();ˠ ȕ;StateWrapper Ȕ;Vector2 ȓ;
public Ș(IMyTextSurface ë,ˠ Ȓ,StateWrapper ȑ){ȕ=Ȓ;Ȕ=ȑ;ȓ=ë.TextureSize;float Ȑ=0.18f;Vector2 Ǫ=new Vector2(85,40);float ȏ=-
0.967f;var Ȏ=ǫ(ë,Ǫ,new Vector2(ȏ+Ȑ,0.85f),"Recall",Color.Black);Ȏ.ɜ=Ǜ=>{Ȓ.Ι();};Ǟ(Ȏ,
"Finish work (broadcast command:force-finish)");Ȗ.Add(Ȏ);var ȍ=ǫ(ë,Ǫ,new Vector2(ȏ+Ȑ*2,0.85f),"Resume",Color.Black);ȍ.ɜ=Ǜ=>{Ȓ.β();};Ǟ(ȍ,
"Resume work (broadcast 'miners.resume' message)");Ȗ.Add(ȍ);var Ȍ=ǫ(ë,Ǫ,new Vector2(ȏ+Ȑ*3,0.85f),"Clear state",Color.Black);Ȍ.ɜ=Ǜ=>{ȑ?.ClearPersistentState();};Ǟ(Ȍ,
"Clear Dispatcher state");Ȗ.Add(Ȍ);var ǯ=ǫ(ë,Ǫ,new Vector2(ȏ+Ȑ*4,0.85f),"Clear log",Color.Black);ǯ.ɜ=Ǜ=>{Р.Ɔ();};Ȗ.Add(ǯ);var ǭ=ǫ(ë,Ǫ,new
Vector2(ȏ+Ȑ*5,0.85f),"Purge locks",Color.Black);ǭ.ɜ=Ǜ=>{Ȓ.Θ();};Ǟ(ǭ,"Clear lock ownership. Last resort in case of deadlock");Ȗ.
Add(ǭ);var Ǭ=ǫ(ë,Ǫ,new Vector2(ȏ+Ȑ*6,0.85f),"EMRG HALT",Color.Black);Ǭ.ɜ=Ǜ=>{Ȓ.α();};Ǟ(Ǭ,
"Halt all activity, restore overrides, release control, clear states");Ȗ.Add(Ǭ);Ɏ=new MySprite(SpriteType.TEXT,"",new Vector2(ȓ.X/1.2f,ȓ.Y*0.9f),null,Color.White,"Debug",TextAlignment.
CENTER,0.5f);Ɍ=new MySprite(SpriteType.TEXT,"",Ȏ.ɤ-Vector2.UnitY*17,null,Color.White,"Debug",TextAlignment.LEFT,0.5f);ɋ=new
MySprite(SpriteType.TEXT,"No active task",new Vector2(ȓ.X/1.2f,ȓ.Y/20f),null,Color.White,"Debug",TextAlignment.CENTER,0.5f);}ɥ ǫ
(IMyTextSurface ë,Vector2 Ǫ,Vector2 ǩ,string Ǩ,Color?ǧ=null){var Ǯ=ë.TextureSize;var Ǧ=new MySprite(SpriteType.TEXTURE,
"SquareSimple",new Vector2(0,0),Ǫ,Color.CornflowerBlue);var Ǥ=Vector2.Zero;if(Ǫ.Y>1)Ǥ.Y=-ë.MeasureStringInPixels(new StringBuilder(Ǩ),
"Debug",0.5f).Y/Ǫ.Y;var ǣ=new MySprite(SpriteType.TEXT,Ǩ,Ǥ,Vector2.One,Color.White,"Debug",TextAlignment.CENTER,0.5f);var Ǣ=new
List<MySprite>(){Ǧ,ǣ};var ǡ=new ɥ(Ǣ,Ǫ,ǩ,Ǯ);if(ǧ!=null){ǡ.ɟ=()=>{ǡ.ȯ(ǟ=>{var Ǡ=ǟ;Ǡ.Color=ǧ;Ǡ.Size=Ǫ*1.05f;return ǟ.Type==
SpriteType.TEXTURE?Ǡ:ǟ;});};ǡ.ɞ=()=>{ǡ.ȯ(ǟ=>ǟ.Type==SpriteType.TEXTURE?Ǧ:ǟ);};}return ǡ;}void Ǟ(ɥ ǝ,string ǜ){ǝ.ɟ+=()=>Ɍ.Data=ǜ;ǝ.
ɞ+=()=>Ɍ.Data="";}bool ǥ;public void ǈ(IMyTextPanel Ƕ,IMyShipController ǽ){bool Ǽ=true;Vector2 Â=Vector2.Zero;bool ǻ=
false;if(ǽ.IsUnderControl&&λ.ƅ.φ("cc")){Â=ǽ.RotationIndicator;var Ǻ=ǽ.RollIndicator;var ǹ=(Ǻ>0);if(!ǹ&&ǥ)ǻ=true;ǥ=ǹ;}if(Â.
LengthSquared()>0||ǻ){Ǽ=true;ȗ.X+=Â.Y;ȗ.Y+=Â.X;ȗ=Vector2.Clamp(ȗ,-Ƕ.TextureSize/2,Ƕ.TextureSize/2);}var Ǹ=ȗ+Ƕ.TextureSize/2;if(Ǽ){
using(var Ǵ=Ƕ.DrawFrame()){ɧ(Ǵ);foreach(var ǝ in Ȗ.Where(é=>é.ɚ).Union(ɏ)){if(ǝ.ȡ(Ǹ)){if(ǻ)ǝ.ɜ?.Invoke(Ǹ);}}foreach(var ǝ in
Ȗ.Where(é=>é.ɚ).Union(ɏ)){Ǵ.AddRange(ǝ.Ȯ());}Ǵ.Add(Ɏ);Ǵ.Add(Ɍ);Ǵ.Add(ɋ);ǵ(Ǵ);var Ƿ=new MySprite(SpriteType.TEXTURE,
"Triangle",Ǹ,new Vector2(7f,10f),Color.White);Ƿ.RotationOrScale=6f;Ǵ.Add(Ƿ);}if(Â.LengthSquared()>0){Ƕ.ContentType=ContentType.
TEXT_AND_IMAGE;Ƕ.ContentType=ContentType.SCRIPT;}}}void ǵ(MySpriteDrawFrame Ǵ){var ŗ=new Vector2(ȓ.X/1.2f,ȓ.Y/2f);foreach(var ǳ in ȕ.ˑ
){var ę=ǳ.Ω.Ȫ.Translation;var Ǉ=ȕ.Η;if(Ǉ!=null){var ǲ=Vector3D.Transform(ę,Ɋ);float Ǳ=3.5f;var ǰ=ŗ+new Vector2((float)ǲ.Y
,(float)ǲ.X)*Ǳ;var Ǫ=Vector2.One*Ǳ*Ǉ.Ε*2;var Ǧ=new MySprite(SpriteType.TEXTURE,"AH_BoreSight",ǰ+new Vector2(0,5),Ǫ*0.8f,ǳ
.Ω.ȩ);Ǧ.RotationOrScale=(float)Math.PI/2f;var Ƞ=new MySprite(SpriteType.TEXTURE,
"Textures\\FactionLogo\\Miners\\MinerIcon_3.dds",ǰ,Ǫ*1.2f,Color.Black);Ǵ.Add(Ƞ);Ǵ.Add(Ǧ);}}}void ɧ(MySpriteDrawFrame Ǵ){bool ɖ=false;int ɕ=0,ɔ=30;foreach(var ɓ in ȕ.ˑ){
if(!ɓ.Ω.ȧ.IsDefault){int ɒ=0,ɑ=100,Ȑ=75;if(!ɖ){foreach(var ɐ in ɓ.Ω.ȧ){Ǵ.Add(new MySprite(SpriteType.TEXTURE,
"SquareSimple",new Vector2(ɑ+ɒ,ɔ),new Vector2(Ȑ-5,40),Color.Black));Ǵ.Add(new MySprite(SpriteType.TEXT,ɐ.Item1,new Vector2(ɑ+ɒ,ɔ-16),
null,Color.White,"Debug",TextAlignment.CENTER,0.5f));ɒ+=Ȑ;}ɖ=true;ɕ+=40;}ɒ=0;foreach(var ɐ in ɓ.Ω.ȧ){Ǵ.Add(new MySprite(
SpriteType.TEXT,ɐ.Item2,new Vector2(ɑ+ɒ,ɔ+ɕ),null,ɓ.Ω.ȩ,"Debug",TextAlignment.CENTER,0.5f));ɒ+=Ȑ;}ɕ+=40;}}}List<ɥ>ɏ=new List<ɥ>();
public Action<int>ɗ;MySprite Ɏ;MySprite Ɍ;MySprite ɋ;MatrixD Ɋ;internal void ɉ(ˠ.Ζ Ɉ){Ɋ=MatrixD.Invert(MatrixD.CreateWorld(Ɉ.Γ
,Ɉ.Δ,Ɉ.Β));ɏ=new List<ɥ>();Vector2 ɇ=new Vector2(ȓ.X/1.2f,ȓ.Y/2f);float Ǳ=3.5f;Vector2 Ǫ=Vector2.One*Ǳ*Ɉ.Ε*1.6f;foreach(
var Ŗ in Ɉ.ΐ){var ę=ɇ+Ŗ.Ν*Ǳ;Color ɍ=Color.White;if(Ŗ.ß==ShaftState.Planned)ɍ=Color.CornflowerBlue;else if(Ŗ.ß==ShaftState.
Complete)ɍ=Color.Darken(Color.CornflowerBlue,0.4f);else if(Ŗ.ß==ShaftState.InProgress)ɍ=Color.Lighten(Color.CornflowerBlue,0.2f)
;else if(Ŗ.ß==ShaftState.Cancelled)ɍ=Color.DarkSlateGray;var Ǧ=new MySprite(SpriteType.TEXTURE,"Circle",new Vector2(0,0),
Ǫ,ɍ);var Ǣ=new List<MySprite>(){Ǧ};var ǡ=new ɥ(Ǣ,Ǫ,ę,ȓ);var ǧ=Color.Red;ǡ.ɝ=ë=>Ɏ.Data=$"id: {Ŗ.Ȩ}, {Ŗ.ß}";ǡ.ɟ=()=>{ǡ.ȯ(ǟ
=>{var Ǡ=ǟ;Ǡ.Color=ǧ;Ǡ.Size=Ǫ*1.05f;return ǟ.Type==SpriteType.TEXTURE?Ǡ:ǟ;});};ǡ.ɞ=()=>{ǡ.ȯ(ǟ=>ǟ.Type==SpriteType.TEXTURE?
Ǧ:ǟ);Ɏ.Data="Hover over shaft for more info,\n tap E to cancel it";};ǡ.ɜ=é=>ɗ?.Invoke(Ŗ.Ȩ);ɏ.Add(ǡ);}}public void ɦ(ˠ ƭ){
if(ƭ?.Η!=null)ɋ.Data=$"Kind: HexSpiral\nShafts: {ƭ.Η.ΐ.Count}\nRadius: {ƭ.Η.Ε:f2}\n"+$"Group: {ƭ.Η.Κ}";}class ɥ{public
Vector2 ɤ,ɣ,ɢ;public List<MySprite>ɡ;public Vector2 ɠ;public Action ɟ{get;set;}public Action ɞ{get;set;}public Action<Vector2>ɝ
{get;set;}public Action<Vector2>ɜ{get;set;}public bool ɛ{get;set;}public bool ɚ=true;Vector2 ə;public ɥ(List<MySprite>Ǣ,
Vector2 ɘ,Vector2 ǩ,Vector2 ȳ){ɡ=Ǣ;if(Math.Abs(ǩ.X)>1)ǩ.X=ǩ.X/(ȳ.X*0.5f)-1;if(Math.Abs(ǩ.Y)>1)ǩ.Y=1-ǩ.Y/(ȳ.Y*0.5f);ə=ȳ;ɠ=new
Vector2(ɘ.X>1?ɘ.X:ɘ.X*ə.X,ɘ.Y>1?ɘ.Y:ɘ.Y*ə.Y);ɢ=ȳ/2f*(Vector2.One+ǩ);ɤ=ɢ-ɠ/2f;ɣ=ɢ+ɠ/2f;}public bool ȡ(Vector2 ȱ){bool Ȱ=(ȱ.X>ɤ.X
)&&(ȱ.X<ɣ.X)&&(ȱ.Y>ɤ.Y)&&(ȱ.Y<ɣ.Y);if(Ȱ){if(!ɛ){ɟ?.Invoke();}ɛ=true;ɝ?.Invoke(ȱ);}else{if(ɛ){ɞ?.Invoke();}ɛ=false;}return
Ȱ;}public void ȯ(Func<MySprite,MySprite>ţ){for(int Ť=0;Ť<ɡ.Count;Ť++){ɡ[Ť]=ţ(ɡ[Ť]);}}public IEnumerable<MySprite>Ȯ(){
foreach(var é in ɡ){var ȭ=ɠ;var Ȭ=é;Ȭ.Position=ɢ+ɠ/2f*é.Position;var ȫ=é.Size.Value;Ȭ.Size=new Vector2(ȫ.X>1?ȫ.X:ȫ.X*ȭ.X,ȫ.Y>1?
ȫ.Y:ȫ.Y*ȭ.Y);yield return Ȭ;}}}}class Ȳ{public MatrixD Ȫ;public Color ȩ;public long Ȩ;public ImmutableArray<MyTuple<
string,string>>ȧ;public void Ȧ(MyTuple<long,MatrixD,Vector4,ImmutableArray<MyTuple<string,string>>>Ğ){Ȩ=Ğ.Item1;Ȫ=Ğ.Item2;ȩ=Ğ.
Item3;ȧ=Ğ.Item4;}public MyTuple<long,MatrixD,Vector4,ImmutableArray<MyTuple<string,string>>>ȥ(){var Ğ=new MyTuple<long,
MatrixD,Vector4,ImmutableArray<MyTuple<string,string>>>();Ğ.Item1=Ȩ;Ğ.Item2=Ȫ;Ğ.Item3=ȩ.ToVector4();Ğ.Item4=ȧ;return Ğ;}}class
Ȥ{static int[]ȣ={1,1,0,-1,-1,0};static int Ȣ(int ĺ)=>ȣ[(ĺ-1)%6];public static int ȴ(int Ť)=>1+3*Ť*(Ť-1);public static int
Ɇ(int ĺ)=>(int)(Math.Ceiling((3+Math.Sqrt(12*ĺ-3))/6));static Vector2 Ʌ(int Ʉ){if(Ʉ==1){return Vector2.Zero;}int Ƀ=Ɇ(Ʉ)-1
;int ɂ=Ʉ-ȴ(Ƀ);int Ɂ;var ɀ=Math.DivRem(ɂ-1,Ƀ,out Ɂ);Ɂ+=1;var é=Ƀ*Ȣ(ɀ+1)+Ɂ*Ȣ(ɀ+3);var ȿ=Ƀ*Ȣ(ɀ+2)+Ɂ*Ȣ(ɀ+4);return new
Vector2(é,ȿ);}public static Vector2 Ⱦ(int ĺ){var Ƚ=Ʌ(ĺ);Ƚ.Y/=2;Ƚ.X-=Ƚ.Y;Ƚ.Y*=(float)Math.Sqrt(3);return(float)Math.Sqrt(3)*Ƚ;}}
List<MyTuple<string,Vector3D,ImmutableArray<string>>>ȼ=new List<MyTuple<string,Vector3D,ImmutableArray<string>>>();void Ȼ(
string ŵ,Vector3D ë,params string[]ƃ){ȼ.Add(new MyTuple<string,Vector3D,ImmutableArray<string>>(ŵ,ë,ƃ.ToImmutableArray()));}
void Ⱥ(long ȹ){IGC.SendUnicastMessage(ȹ,"hud.apck.proj",ȼ.ToImmutableArray());ȼ.Clear();}static ȷ ȸ;struct ȷ{public int ȶ;
public int ƌ;public int ȵ;}