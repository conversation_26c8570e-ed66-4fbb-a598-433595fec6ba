string Ver = "1.0.194";

// (c)AutoPillock by cheerkin
// WS link: https://steamcommunity.com/sharedfiles/filedetails/?id=3158053231

static bool DesignMode;

static Vector3D GRID_ANGULAR_ACCELERATIONS = new Vector3D(0.5f, 0.5f, 1.02f);

static long? DIAG_IGC = null;
static float MAX_SP = 100f;
static float CC_GAIN = 100f; // how far away the destination point is generated for "direction" shifters
const float G = 9.81f;

static float StoppingPowerQuotient = 0.5f; // 0.9
static bool SmoothPointBlankAcceleration = true;
static float DampeningCutoffSquared = 16f;
static float DampeningCutoffFactor = 0.1f;

static string GGEN_GR_TAG = "";
static int LOGGER_MAX_CHARS = 5000;

static float PMW_FF_REACTION_R = 2500;

static bool IsLargeGrid;

public class Variables
{
    Dictionary<string, ISettable> variables = new Dictionary<string, ISettable> 
    {
        { "wb-range-override", new Variable<float> { value = 0, parser = s => float.Parse(s) } },
        { "wb-precision-override", new Variable<float> { value = 0, parser = s => float.Parse(s) } },
        { "hold-thrust-on-rotation", new Variable<bool> { value = false, parser = s => s == "true" } },
        { "torpedo-fuse-offset", new Variable<float> { value = -0.5f, parser = s => float.Parse(s) } },
        { "roll-power-factor", new Variable<float> { value = 0.2f, parser = s => float.Parse(s) } },
        { "sp-limit", new Variable<float> { value = 104.38f, parser = s => { var r = float.Parse(s); MAX_SP = r; return r; } } },
        { "cc-gain", new Variable<float> { value = 100f, parser = s => { var r = float.Parse(s); CC_GAIN = r; return r; } } },
        { "dpath-speed-limit", new Variable<float> { value = 30, parser = s => float.Parse(s) } },
        { "capital-behavior", new Variable<bool> { value = false, parser = s => s == "true" } }, // more conservative control for big ships in certain tasks (i.e. land)
        { "ripple-increment-interval", new Variable<int> { value = 20, parser = s => int.Parse(s) } },
        { "ripple-increment-interval-rail", new Variable<int> { value = 180, parser = s => int.Parse(s) } },
        { "filtering-size", new Variable<float> { value = 6f, parser = s => float.Parse(s) } },
        { "awareness-range", new Variable<float> { value = 3000f, parser = s => float.Parse(s) } },
        { "tg-autolink-range", new Variable<float> { value = 500f, parser = s => float.Parse(s) } },
        { "wb-model-cycle-timeout", new Variable<int> { value = 60, parser = s => int.Parse(s) } },
        { "diag-igc", new Variable<long?> { value = null, parser = s => { var r = long.Parse(s); DIAG_IGC = r; return r; } } },
        { "custom-val", new Variable<float> { value = 0f, parser = s => float.Parse(s) } } // for user set in conditionals
    };
    
    public void Set(string key, string value) 
    { 
        variables[key].Set(value); 
    }
    
    public void Set<T>(string key, T value) 
    { 
        (variables[key] as Variable<T>).value = value; 
    }
    
    public T Get<T>(string key) 
    { 
        return (variables[key] as Variable<T>).value; 
    }
    
    public interface ISettable
    {
        void Set(string v);
    }
    
    public class Variable<T> : ISettable
    {
        public T value;
        public Func<string, T> parser;
        public void Set(string v) { value = parser(v); }
    }
}

public enum TargetSelection { First, Closest, Random, Loop }
public enum ResponseKind { Ignore, FreeFire, Attack, Kite, Flee }
public enum ResponseState { None, FreeFiring, Attacking }
public enum PState { Disabled = 0, Inert, WP }
public enum ThrustDelegation { None = 0, Vtol, Rover, Clang }

class ToggleManager
{
    Dictionary<string, bool> toggleStates = new Dictionary<string, bool>
    {
        {"suppress-transition-control", false},
        {"wingman-circle-rotation", false},
        {"damp-when-idle", true},
        {"ignore-user-thruster", false},
        {"coax-ripple", true},
        {"suppress-gyro-control", false},
        {"aim-to-center", false},
        {"avoid-carrier", true},
        {"freefall-target-filter", false},
        {"wb-snipe-range", false},
        {"wb-jab", false},
        {"patrol-after-response", false},
        {"allow-1t-up-shifter", false},
        {"log", false},
        {"echo", false}
    };
    
    public void SetToggle(string key, bool value)
    {
        toggleStates[key] = value;
    }
    
    public void Toggle(string key)
    {
        toggleStates[key] = !toggleStates[key];
    }
    
    public bool GetToggle(string key)
    {
        return toggleStates[key];
    }
    
    public ImmutableArray<MyTuple<string, string>> GetToggleCommands(string prefix)
    {
        return toggleStates.Select(kvp => new MyTuple<string, string>(
            $"{kvp.Key}: {(kvp.Value ? "on" : "off")}", 
            $"[toggle:{kvp.Key}],[{prefix}]")).ToImmutableArray();
    }
    
    public void ProcessToggle(string key, Agent agent)
    {
        var state = agent.ToggleManager.GetToggle(key);
        switch (key)
        {
            case "suppress-transition-control":
                if (!state)
                    agent.GetFlightController().Reset();
                break;
            case "vtol":
                agent.GetFlightController().SetThrustMode(0);
                break;
            case "log":
                agent.LogLogger = null;
                if (state)
                    agent.LogLogger = new Logger(GlobalLogger.Log, agent);
                break;
            case "echo":
                agent.EchoLogger = null;
                if (state)
                    agent.EchoLogger = new Logger(GlobalLogger.Echo, agent);
                break;
        }
    }
}

class Logger
{
    StringBuilder buffer = new StringBuilder();
    Agent agent;
    Action<string> output;
    
    public Logger(Action<string> outputAction, Agent agent)
    {
        output = outputAction;
        this.agent = agent;
    }
    
    public void Log(string message)
    {
        buffer.AppendLine(message);
    }
    
    public void Flush()
    {
        if (buffer.Length > 0)
            output(buffer.ToString());
        buffer.Clear();
    }
}

Program()
{
    Runtime.UpdateFrequency = UpdateFrequency.Update100;
    gridTerminalSystem = GridTerminalSystem;
    commandProcessor = new CommandProcessor(IGC, new Dictionary<string, Action<string[], Agent>>
    {
        {"set-output", SetOutput},
        {"next", (args, agent) => agent.Captain.Next()},
        {"create-task", CreateTask},
        {"inject-task", (args, agent) => CreateTask(args, agent, true)},
        {"inject-task-inherit-target", (args, agent) => CreateTask(args, agent, true, true)},
        {"remove-task", (args, agent) => agent.Captain.RemoveTask(int.Parse(args[2]))},
        {"infer-task", InferTask},
        {"default-task", SetDefaultTask},
        {"remove-default-task", RemoveDefaultTask},
        {"signal", (args, agent) => agent.Captain.Signal()},
        {"repeat", RepeatCommands},
        {"save-queue", SaveQueue},
        {"exec-queue", ExecuteQueue},
        {"jab", CreateJabTask},
        {"jab2", CreateJab2Task},
        {"thrust-delegation", SetThrustDelegation},
        {"recycle", Recycle},
        {"set-value", (args, agent) => agent.Variables.Set(args[2], args[3])},
        {"set-tag", (args, agent) => agent.Tag = args[2]},
        {"clear-state", ClearState},
        {"clear-defs", ClearDefinitions},
        {"clear-navs", ClearNavigations},
        {"request-docking", RequestDocking},
        {"request-depart", RequestDepart},
        {"cancel-current-route", CancelCurrentRoute},
        {"start-su", StartSubUnit},
        {"refresh-su", RefreshSubUnits},
        {"query-target", QueryTarget},
        {"tmc", RunTmc},
        {"timer", RunTimer},
        {"d-path-add", AddDynamicPath},
        {"d-path-clear", ClearDynamicPath},
        {"replace-behavior-task", ReplaceBehaviorTask},
        {"replace-behavior-current", ReplaceBehaviorCurrent},
        {"chain-behavior-task", ChainBehaviorTask},
        {"chain-behavior-current", ChainBehaviorCurrent},
        {"set-response", SetResponse},
        {"set-targeting-strategy", SetTargetingStrategy},
        {"detonate", Detonate},
        {"set-response-ovr", SetResponseOverride},
        {"w-mod-value", SetWorldModValue},
        {"wb-cycle-face", CycleWeaponFace},
        {"add-condition", AddCondition},
        {"cmdr-draw-pos", DrawPosition},
        {"cmdr-draw-targetable", DrawTargetable},
        {"delay-cmd", DelayCommand},
        {"get-toggles", GetToggles},
        {"get-storage", GetStorage}
    });
}

void SetOutput(string[] args, Agent agent)
{
    IMyTextSurface surface;
    var surfaceProvider = agent.BlockManager.AllBlocks.FirstOrDefault(block => 
        block.CustomName.Contains(args[2]) && (block is IMyTextSurfaceProvider)) as IMyTextSurfaceProvider;
    
    surface = surfaceProvider?.GetSurface(int.Parse(args[3]));
    
    if (surface == null)
    {
        List<IMyTextPanel> panels = new List<IMyTextPanel>();
        GridTerminalSystem.GetBlocksOfType(panels, block => 
            block.IsSameConstructAs(Me) && block.CustomName.Contains(args[2]));
        surface = panels.FirstOrDefault();
    }
    
    if (surface != null)
    {
        GlobalLogger.SetOutput = (message) => surface.WriteText(message + "\n", true);
        GlobalLogger.Clear = () => surface.WriteText("");
        surface.ContentType = ContentType.TEXT_AND_IMAGE;
    }
}

// Global variables and managers
IMyGridTerminalSystem gridTerminalSystem;
CommandProcessor commandProcessor;
Agent mainAgent;
List<string> commandQueue = new List<string>();
StateManager stateManager;

// Placeholder classes that would need full implementation
public class CommandProcessor 
{
    public CommandProcessor(IMyIntergridCommunicationSystem igc, Dictionary<string, Action<string[], Agent>> commands) { }
    public void Execute(string command, Agent agent) { }
}

public class Agent
{
    public Variables Variables = new Variables();
    public ToggleManager ToggleManager = new ToggleManager();
    public BlockManager BlockManager;
    public Captain Captain;
    public FlightController FlightController;
    public WeaponController WeaponController;
    public Logger LogLogger;
    public Logger EchoLogger;
    public string Tag = "";
    public bool IsActive;
    public Vector3D Position => FlightController?.Position ?? Vector3D.Zero;
    public IMyLandingGear LandingGear;
    public SubordinateManager SubordinateManager = new SubordinateManager();

    public FlightController GetFlightController() { return FlightController; }

    public bool HasGravity()
    {
        return FlightController?.GravityVector.HasValue == true;
    }
}

public class SubordinateManager
{
    public Target CurrentTarget = new Target("none", null);
}

public class Captain 
{
    public void Next() { }
    public void RemoveTask(int id) { }
    public void Signal() { }
}

public class BlockManager 
{
    public List<IMyTerminalBlock> AllBlocks = new List<IMyTerminalBlock>();
}

public class FlightController
{
    public PState State { get; private set; }
    public ThrustDelegation ThrustMode { get; private set; }
    public int ThrustDimension { get; private set; }
    public IMyRemoteControl RemoteControl { get; private set; }
    public IMyGyro Gyro { get; private set; }
    public Vector3D Velocity => RemoteControl.GetShipVelocities().LinearVelocity;
    public Vector3D? GravityVector { get; private set; }
    public Vector3D GravityDirection { get; private set; }
    public double GravityStrength { get; private set; }
    public double? Altitude { get; private set; }
    public Vector3D Position => RemoteControl.GetPosition();
    public Vector3D Acceleration { get; private set; }
    public BoundingBoxD ThrustBounds { get; private set; }

    private Agent agent;
    private Func<ThrustManager> getThrustManager;
    private Vector3D previousVelocity;
    private Vector3D previousAngularVelocity;
    private int lastTick;
    private double deltaTime;
    private bool gyroOverride;

    public FlightController(IMyRemoteControl remoteControl, IMyGyro gyro, Func<ThrustManager> thrustManager, Agent agent)
    {
        this.RemoteControl = remoteControl;
        this.Gyro = gyro;
        this.getThrustManager = thrustManager;
        this.agent = agent;

        var bounds = getThrustManager().GetAccelerationBounds(1);
        ThrustBounds = bounds;

        if (bounds.Volume > 0)
            ThrustDimension = 3;
        else if (bounds.Max.Z > 0)
            ThrustDimension = 1;
    }

    public void SetState(PState state)
    {
        if (state == PState.WP)
            EnableDampeners(false);
        else if (state == PState.Inert)
            DisableControl(false);
        else if (state == PState.Disabled)
            DisableControl();
        State = state;
    }

    public void SetThrustMode(ThrustDelegation mode)
    {
        ThrustMode = mode;
    }

    public void Reset()
    {
        Gyro.GyroOverride = false;
        getThrustManager().Reset();
        RemoteControl.DampenersOverride = true;
    }

    private void EnableDampeners(bool enable = true)
    {
        RemoteControl.DampenersOverride = !enable;
    }

    private void DisableControl(bool dampeners = true)
    {
        Gyro.GyroOverride = false;
        getThrustManager().Reset();
        RemoteControl.DampenersOverride = dampeners;
    }

    public void Update(BehaviorData behavior)
    {
        var currentTick = GlobalTimer.CurrentTick;
        var tickDelta = currentTick - lastTick;
        lastTick = currentTick;

        if (tickDelta > 0)
            deltaTime = tickDelta / 60f;
        else
            return;

        // Update acceleration calculation
        Acceleration = (Velocity - previousVelocity) / deltaTime;
        previousVelocity = Velocity;

        // Update gravity
        UpdateGravity();

        if (State != PState.WP)
            return;

        try
        {
            if (behavior?.CalculatedPosition.HasValue == true)
            {
                var currentPos = Position;
                var targetPos = behavior.CalculatedPosition.Value;
                var targetVel = behavior.PrimaryTarget?.Velocity;
                var speedLimit = behavior.SpeedLimit;
                var useCircumnavigation = behavior.UseCircumnavigation;

                ProcessMovement(currentPos, targetPos, targetVel, speedLimit, useCircumnavigation);
            }

            if (behavior?.AimDirection.HasValue == true)
            {
                ProcessRotation(behavior.AimDirection.Value, behavior.UpDirection, behavior.CalculatedOrientation);
            }
        }
        catch (Exception ex)
        {
            agent.LogLogger?.Log($"FlightController error: {ex}");
            SetState(PState.Disabled);
            throw;
        }
    }

    private void UpdateGravity()
    {
        var gravity = RemoteControl.GetNaturalGravity();
        if (gravity != Vector3D.Zero)
        {
            GravityStrength = gravity.Length();
            GravityDirection = gravity / GravityStrength;
            GravityVector = gravity;

            double elevation;
            if (RemoteControl.TryGetPlanetElevation(MyPlanetElevation.Surface, out elevation))
                Altitude = elevation;
        }
        else
        {
            GravityVector = null;
            Altitude = null;
        }
    }

    private void ProcessMovement(Vector3D currentPos, Vector3D targetPos, Vector3D? targetVel, float? speedLimit, bool useCircumnavigation)
    {
        if (ThrustDimension < 3)
            return;

        var mass = RemoteControl.CalculateShipMass().PhysicalMass;
        var thrustBounds = getThrustManager().GetAccelerationBounds(mass);

        if (thrustBounds.Volume == 0 || mass == 0)
            return;

        var direction = targetPos - currentPos;
        var distance = direction.Length();

        if (distance > double.Epsilon)
        {
            var normalizedDirection = direction / distance;
            var currentVel = Velocity;
            var relativeVel = currentVel;

            if (targetVel.HasValue)
                relativeVel = currentVel - targetVel.Value;

            // Calculate desired acceleration
            var desiredAccel = CalculateDesiredAcceleration(direction, relativeVel, speedLimit, thrustBounds);

            // Apply thrust
            getThrustManager().ApplyAcceleration(desiredAccel, mass);
        }
    }

    private Vector3D CalculateDesiredAcceleration(Vector3D direction, Vector3D velocity, float? speedLimit, BoundingBoxD thrustBounds)
    {
        var distance = direction.Length();
        var normalizedDir = direction.Normalized();
        var speed = velocity.Length();
        var maxAccel = thrustBounds.Extents.Length();

        // Calculate stopping distance
        var stoppingDistance = (speed * speed) / (2 * maxAccel);

        Vector3D accel = Vector3D.Zero;

        if (distance > stoppingDistance)
        {
            // Accelerate towards target
            accel = normalizedDir * maxAccel;

            // Apply speed limit
            if (speedLimit.HasValue)
            {
                var speedInDirection = Vector3D.Dot(velocity, normalizedDir);
                if (speedInDirection > speedLimit.Value)
                {
                    accel = Vector3D.Zero; // Don't accelerate if over speed limit
                }
            }
        }
        else
        {
            // Decelerate
            accel = -velocity.Normalized() * maxAccel;
        }

        // Add gravity compensation
        if (GravityVector.HasValue)
            accel -= GravityVector.Value;

        return accel;
    }

    private void ProcessRotation(Vector3D aimDirection, Vector3D upDirection, MatrixD? targetOrientation)
    {
        if (agent.ToggleManager.GetToggle("suppress-gyro-control"))
            return;

        var currentMatrix = Gyro.WorldMatrix;
        var targetMatrix = targetOrientation ?? MatrixD.CreateFromDir(aimDirection, upDirection);

        var rotationVector = CalculateRotationVector(aimDirection, targetMatrix, currentMatrix, upDirection);

        gyroOverride = true;
        if (gyroOverride != Gyro.GyroOverride)
            Gyro.GyroOverride = gyroOverride;

        ApplyGyroRotation(rotationVector);
    }

    private Vector3D CalculateRotationVector(Vector3D aimDirection, MatrixD targetMatrix, MatrixD currentMatrix, Vector3D upDirection)
    {
        var forward = targetMatrix.Forward;
        var up = targetMatrix.Up;
        var right = targetMatrix.Right;

        // Calculate pitch (around right axis)
        var pitchAngle = -Math.Atan2(Vector3D.Dot(Vector3D.Cross(currentMatrix.Forward, forward), right), Vector3D.Dot(currentMatrix.Forward, forward));

        // Calculate yaw (around up axis)
        var yawAngle = -Math.Atan2(Vector3D.Dot(Vector3D.Cross(currentMatrix.Forward, forward), up), Vector3D.Dot(currentMatrix.Forward, forward));

        // Calculate roll (around forward axis)
        var rollAngle = 0f;
        if (upDirection != Vector3D.Zero)
        {
            var projectedUp = Vector3D.ProjectOnPlane(ref upDirection, ref forward).Normalized();
            rollAngle = Math.Atan2(Vector3D.Dot(Vector3D.Cross(currentMatrix.Up, projectedUp), forward), Vector3D.Dot(currentMatrix.Up, projectedUp));
        }

        var rollPowerFactor = agent.Variables.Get<float>("roll-power-factor");
        return new Vector3D(pitchAngle, yawAngle, rollAngle * rollPowerFactor);
    }

    private void ApplyGyroRotation(Vector3D rotationVector)
    {
        var angularVelocity = RemoteControl.GetShipVelocities().AngularVelocity;
        var localAngularVel = Vector3D.TransformNormal(angularVelocity, MatrixD.Transpose(Gyro.WorldMatrix));

        var maxAngularAccel = new Vector3D(30, 30, 30); // Default values, should be configurable
        var dampingFactor = 2 * Math.PI / 60f;

        // PID-style control for each axis
        var pitch = CalculateAxisControl(rotationVector.X, localAngularVel.X, maxAngularAccel.X, dampingFactor);
        var yaw = CalculateAxisControl(rotationVector.Y, localAngularVel.Y, maxAngularAccel.Y, dampingFactor);
        var roll = CalculateAxisControl(rotationVector.Z, localAngularVel.Z, maxAngularAccel.Z, dampingFactor);

        Gyro.SetValue("Pitch", -pitch);
        Gyro.SetValue("Yaw", yaw);
        Gyro.SetValue("Roll", roll);
    }

    private float CalculateAxisControl(double targetAngle, double currentAngularVel, double maxAccel, double dampingFactor)
    {
        var error = targetAngle;
        var maxAngularVelForError = Math.Sqrt(2 * Math.Abs(error) * maxAccel);

        float output;
        if (Math.Abs(error) > maxAngularVelForError * maxAngularVelForError / (2 * maxAccel))
        {
            output = (float)(30 * Math.Sign(error) * Math.Max(Math.Min(Math.Abs(error), 1), 0.0002));
        }
        else
        {
            output = (float)(-30 * Math.Sign(error) * Math.Max(Math.Min(Math.Abs(error), 1), 0.0002));
        }

        return output - (float)(currentAngularVel / dampingFactor);
    }
}

public class WeaponController
{
    public float Range { get; set; } = 800f;
    public void CycleFace(int face) { }
    public void SetTarget(Vector3D position) { }
    public Vector3D CalculateBallisticSolution(Vector3D targetPos, Vector3D targetVel, Vector3D shooterVel, Vector3D gravity)
    {
        return targetPos; // Simplified implementation
    }
    public BallisticSolution GetSolution() { return new BallisticSolution(); }
}

public struct BallisticSolution
{
    public Vector3D Position;
    public Vector3D Direction;
}

public class StateManager
{
    public void Save() { }
    public void Load(string data) { }
    public void Clear() { }
}

// Thrust Management System
public class ThrustManager
{
    private List<ThrusterGroup> thrusterGroups;
    private List<IMyTerminalBlock> allThrusters;
    private double[] maxForces = new double[6]; // Forward, Backward, Up, Down, Left, Right
    private bool initialized = false;
    private IMyTerminalBlock referenceBlock;

    public ThrustManager(IMyTerminalBlock reference, List<IMyTerminalBlock> thrusters)
    {
        referenceBlock = reference;
        allThrusters = thrusters;
        InitializeThrusterGroups();
    }

    private void InitializeThrusterGroups()
    {
        if (initialized) return;

        var matrix = referenceBlock.WorldMatrix;
        thrusterGroups = new List<ThrusterGroup>();

        // Create thruster groups for each direction
        thrusterGroups.Add(new ThrusterGroup(matrix.Backward, allThrusters)); // Forward thrust
        thrusterGroups.Add(new ThrusterGroup(matrix.Forward, allThrusters));  // Backward thrust
        thrusterGroups.Add(new ThrusterGroup(matrix.Down, allThrusters));     // Up thrust
        thrusterGroups.Add(new ThrusterGroup(matrix.Up, allThrusters));       // Down thrust
        thrusterGroups.Add(new ThrusterGroup(matrix.Left, allThrusters));     // Right thrust
        thrusterGroups.Add(new ThrusterGroup(matrix.Right, allThrusters));    // Left thrust

        UpdateMaxForces();
        initialized = true;
    }

    public void UpdateMaxForces()
    {
        for (int i = 0; i < thrusterGroups.Count && i < 6; i++)
        {
            maxForces[i] = thrusterGroups[i].GetMaxForce();
        }
    }

    public BoundingBoxD GetAccelerationBounds(float mass)
    {
        if (mass == 0) return new BoundingBoxD();

        Vector3D min = new Vector3D(-maxForces[5], -maxForces[3], -maxForces[1]) / mass;
        Vector3D max = new Vector3D(maxForces[4], maxForces[2], maxForces[0]) / mass;
        return new BoundingBoxD(min, max);
    }

    public void ApplyAcceleration(Vector3D acceleration, float mass)
    {
        if (!initialized) return;

        // Transform acceleration to local coordinates
        var localAccel = Vector3D.TransformNormal(acceleration, MatrixD.Transpose(referenceBlock.WorldMatrix));

        // Apply to each axis
        ApplyAxisThrust(0, localAccel.Z, mass); // Forward/Backward
        ApplyAxisThrust(1, -localAccel.Z, mass);
        ApplyAxisThrust(2, localAccel.Y, mass);  // Up/Down
        ApplyAxisThrust(3, -localAccel.Y, mass);
        ApplyAxisThrust(4, localAccel.X, mass);  // Left/Right
        ApplyAxisThrust(5, -localAccel.X, mass);
    }

    private void ApplyAxisThrust(int axisIndex, double acceleration, float mass)
    {
        if (axisIndex >= thrusterGroups.Count) return;

        var force = acceleration * mass;
        var maxForce = maxForces[axisIndex];

        if (maxForce > 0)
        {
            var thrustRatio = Math.Max(0, Math.Min(1, force / maxForce));
            thrusterGroups[axisIndex].SetThrust(thrustRatio);
        }
    }

    public void Reset()
    {
        foreach (var group in thrusterGroups)
        {
            group.Reset();
        }
    }
}

public class ThrusterGroup
{
    private List<IMyThrust> thrusters;
    private double maxForce;

    public ThrusterGroup(Vector3D direction, List<IMyTerminalBlock> allThrusters)
    {
        thrusters = new List<IMyThrust>();

        foreach (var block in allThrusters)
        {
            if (block is IMyThrust thruster)
            {
                // Check if thruster points in the desired direction
                if (Vector3D.Dot(-thruster.WorldMatrix.Forward, direction) > 0.7)
                {
                    thrusters.Add(thruster);
                }
            }
        }

        UpdateMaxForce();
    }

    public void UpdateMaxForce()
    {
        maxForce = 0;
        foreach (var thruster in thrusters)
        {
            if (thruster.IsFunctional)
                maxForce += thruster.MaxEffectiveThrust;
        }
    }

    public double GetMaxForce()
    {
        return maxForce;
    }

    public void SetThrust(double ratio)
    {
        foreach (var thruster in thrusters)
        {
            if (thruster.IsFunctional)
            {
                if (ratio <= 0)
                    thruster.ThrustOverride = 0.00000001f;
                else
                    thruster.ThrustOverride = (float)(ratio * thruster.MaxThrust);
            }
        }
    }

    public void Reset()
    {
        foreach (var thruster in thrusters)
        {
            thruster.ThrustOverride = 0;
            thruster.Enabled = true;
        }
    }
}

public class GlobalLogger 
{
    public static Action<string> SetOutput;
    public static Action Clear;
    public static Action<string> Log;
    public static Action<string> Echo;
}

// Placeholder method implementations
void CreateTask(string[] args, Agent agent, bool inject = false, bool inheritTarget = false) { }
void InferTask(string[] args) { }
void SetDefaultTask(string[] args, Agent agent) { }
void RemoveDefaultTask(string[] args, Agent agent) { }
void RepeatCommands(string[] args, Agent agent) { }
void SaveQueue(string[] args, Agent agent) { }
void ExecuteQueue(string[] args, Agent agent) { }
void CreateJabTask(string[] args, Agent agent) { }
void CreateJab2Task(string[] args, Agent agent) { }
void SetThrustDelegation(string[] args, Agent agent) { }
void Recycle() { }
void ClearState(string[] args, Agent agent) { }
void ClearDefinitions() { }
void ClearNavigations() { }
void RequestDocking(string[] args, Agent agent) { }
void RequestDepart(string[] args, Agent agent) { }
void CancelCurrentRoute(string[] args, Agent agent) { }
void StartSubUnit(string[] args, Agent agent) { }
void RefreshSubUnits(string[] args, Agent agent) { }
void QueryTarget(string[] args, Agent agent) { }
void RunTmc(string[] args, Agent agent) { }
void RunTimer(string[] args, Agent agent) { }
void AddDynamicPath(string[] args, Agent agent) { }
void ClearDynamicPath(string[] args, Agent agent) { }
void ReplaceBehaviorTask(string[] args, Agent agent) { }
void ReplaceBehaviorCurrent(string[] args, Agent agent) { }
void ChainBehaviorTask(string[] args, Agent agent) { }
void ChainBehaviorCurrent(string[] args, Agent agent) { }
void SetResponse(string[] args, Agent agent) { }
void SetTargetingStrategy(string[] args) { }
void Detonate(string[] args, Agent agent) { }
void SetResponseOverride(string[] args, Agent agent) { }
void SetWorldModValue(string[] args) { }
void CycleWeaponFace(string[] args, Agent agent) { }
void AddCondition(string[] args, Agent agent) { }
void DrawPosition(string[] args, Agent agent) { }
void DrawTargetable(string[] args, Agent agent) { }
void DelayCommand(string[] args, Agent agent) { }
void GetToggles(string[] args, Agent agent) { }
void GetStorage(string[] args, Agent agent) { }

// Task Management System
public abstract class Task
{
    public string Name;
    public string Type { get; protected set; }
    public int Id { get; private set; }
    public bool IsInterruptible;
    public bool IsRepeatable;
    public Vector3D? Position;
    public int? TimeoutTicks;
    public BehaviorData BehaviorData;
    public Action OnComplete;
    public Action OnStart;
    public int StartTick;
    bool hasStarted;
    protected Agent agent;

    protected Task(string type, Agent agent, bool allowDepart = true)
    {
        Type = type;
        this.agent = agent;
        Name = type;
        BehaviorData = new BehaviorData();
        hasStarted = allowDepart;
    }

    public void SetId(int id) { Id = id; }

    public virtual void SetTargets(Target primary, Target secondary)
    {
        BehaviorData.PrimaryTarget = primary;
        BehaviorData.SecondaryTarget = secondary;
    }

    public abstract bool IsComplete(int currentTick, FlightController flightController);

    public bool CheckCompletion(int currentTick, FlightController flightController)
    {
        if (TimeoutTicks.HasValue && (currentTick - StartTick > TimeoutTicks))
            return true;
        return IsComplete(currentTick, flightController);
    }

    bool hasInitialized;

    public void Initialize(Agent agent, int currentTick)
    {
        if ((agent.Connector?.Status == MyShipConnectorStatus.Connected) && hasStarted)
        {
            agent.LogLogger?.Log($"{Name}: forcing depart");
            agent.Captain.RequestDepart(BehaviorData.SecondaryTarget);
            return;
        }

        if (agent.LandingGear?.IsLocked == true)
        {
            agent.LandingGear.Unlock();
            agent.FlightController.SetState(PState.WP);
        }

        if (StartTick == 0)
            StartTick = currentTick;

        agent.TimerManager.Trigger(Type + ".OnStart");

        if (!hasInitialized)
        {
            hasInitialized = true;
            agent.LogLogger?.Log($"Starting task {Type}-{Id}");
            OnStart?.Invoke();
        }
        else
        {
            agent.LogLogger?.Log($"Resuming task {Type}-{Id}");
        }
    }
}

public class WaitTask : Task
{
    Func<bool> condition;

    public WaitTask(Agent agent, int ticks = 0) : base(ticks == 0 ? "wait-for-signal" : $"wait-{ticks}t", agent, false)
    {
        if (ticks != 0)
            TimeoutTicks = ticks;
        BehaviorData.Name = "Wait";
    }

    public WaitTask(Agent agent, Func<bool> condition, int ticks = 0) : base("wait-for-condition", agent, false)
    {
        if (ticks != 0)
            TimeoutTicks = ticks;
        BehaviorData.Name = "Wait";
        this.condition = condition;
    }

    public override bool IsComplete(int currentTick, FlightController flightController)
    {
        return TimeoutTicks.HasValue && (TimeoutTicks.Value < (currentTick - StartTick)) ||
               (condition?.Invoke() == true);
    }
}

public class ExecuteTask : Task
{
    public ExecuteTask(Action action) : base("exec", null, false)
    {
        OnComplete = action;
    }

    public override bool IsComplete(int currentTick, FlightController flightController)
    {
        return true;
    }
}

public class MoveTask : Task
{
    double? proximitySquared;

    public MoveTask(Vector3D position, Agent agent, double? proximity, bool flyThrough) : base("move", agent)
    {
        if (proximity != null)
            proximitySquared = proximity * proximity;

        BehaviorData.UseAvoidance = true;
        IsInterruptible = true;

        var target = new Target(Name, null);
        OnStart = () => target.SetPosition(position);
        Position = position;
        BehaviorData.UseCircumnavigation = true;
        BehaviorData.SecondaryTarget = target;
        BehaviorData.PrimaryTarget = target;
        BehaviorData.FlyThrough = flyThrough;

        if (flyThrough && proximity.HasValue)
            proximitySquared = 400;

        BehaviorData.UpNormalFunction = BehaviorFunctions.NegativeGravity;
    }

    public override bool IsComplete(int currentTick, FlightController flightController)
    {
        if (!proximitySquared.HasValue)
            return false;

        var distanceSquared = (BehaviorData.GetPosition?.Invoke() ?? agent.Position - BehaviorData.SecondaryTarget.Position.Value).LengthSquared();

        if (distanceSquared < 100)
            BehaviorData.PrimaryTarget = null;

        return distanceSquared < proximitySquared;
    }
}

public class LandTask : MoveTask
{
    Vector3D surfaceNormal;
    Vector3D landingPosition;

    public LandTask(Vector3D? position, Agent agent, Vector3D? normal) : base(Vector3D.Zero, agent, 0, false)
    {
        Type = "land";
        IsInterruptible = true;

        OnStart = () =>
        {
            if (!position.HasValue)
            {
                if (agent.HasGravity())
                {
                    var altitude = agent.FlightController.Altitude.Value;
                    landingPosition = agent.Position + agent.FlightController.GravityDirection * altitude;
                }
                else
                {
                    agent.Captain.Next();
                }
            }
            else
            {
                landingPosition = position.Value;
            }

            Position = landingPosition;

            if (!normal.HasValue)
            {
                if (agent.HasGravity())
                    surfaceNormal = agent.FlightController.GravityDirection;
                else
                    agent.Captain.Next();
            }
            else
            {
                surfaceNormal = normal.Value;
            }

            var offset = landingPosition - agent.Position;
            var distance = offset.Length();

            if (distance > 500)
            {
                if (agent.Variables.Get<bool>("capital-behavior"))
                    agent.Captain.InsertTask(new MoveTask(landingPosition - offset / distance * 400 - surfaceNormal * offset.Length() / 4f, agent, 50, false));
                else
                    agent.Captain.InsertTask(new MoveTask(landingPosition - offset / distance * 400 - surfaceNormal * offset.Length() / 4f, agent, 5, true));
            }
        };

        BehaviorData.IsRelativeMovement = true;
        BehaviorData.AimFunction = (context, param) => agent.LandingGear.GetPosition() + surfaceNormal * 1000;
        BehaviorData.MoveFunction = (context, param) => landingPosition;
        BehaviorData.GetPosition = () => agent.LandingGear.GetPosition() + agent.LandingGear.WorldMatrix.Down * (IsLargeGrid ? 3.2 : 0.55);
        BehaviorData.GetOrientation = () =>
        {
            var matrix = MatrixD.CreateFromDir(agent.LandingGear.WorldMatrix.Down);
            matrix.Translation = agent.LandingGear.WorldMatrix.Translation;
            return matrix;
        };
    }

    public override bool IsComplete(int currentTick, FlightController flightController)
    {
        if (agent.LandingGear.IsLocked)
        {
            agent.FlightController.SetState(PState.Inert);
            return true;
        }
        return false;
    }
}

// Behavior and targeting system
public class BehaviorData
{
    public string Name = "Default";
    public Func<BehaviorContext, BehaviorParameter?, Vector3D> MoveFunction;
    public Func<BehaviorContext, BehaviorParameter?, Vector3D> AimFunction;
    public Func<BehaviorContext, BehaviorParameter?, Vector3D> UpNormalFunction;
    public Func<MatrixD> GetOrientation;
    public Func<Vector3D> GetPosition;
    public float? SpeedLimit;
    public bool FlyThrough = false;
    public bool IsRelativeMovement;
    public bool UseCircumnavigation;
    public bool UseAvoidance;
    public Target PrimaryTarget;
    public Target SecondaryTarget;
    public Vector3D? CalculatedPosition;
    public Vector3D? AimDirection;
    public Vector3D UpDirection;
    public MatrixD? CalculatedOrientation;
}

public struct BehaviorContext
{
    public Vector3D Position;
    public Vector3D Velocity;
    public Agent Agent;
    public BehaviorData BehaviorData;
}

public struct BehaviorParameter
{
    public float? Value;
    public Func<BehaviorContext, BehaviorParameter?, Vector3D> Function;
}

public class Target
{
    public string Name;
    public long? EntityId;
    public Vector3D? Position;
    public Vector3D? Velocity;
    public MatrixD? Orientation;
    public BoundingBoxD? BoundingBox;

    public Target(string name, long? entityId)
    {
        Name = name;
        EntityId = entityId;
    }

    public void SetPosition(Vector3D position)
    {
        Position = position;
    }

    public bool IsValid()
    {
        return Position.HasValue;
    }

    public bool IsExpired()
    {
        // Implementation for target expiration logic
        return false;
    }
}

// Behavior Functions - Core movement and aiming behaviors
public static class BehaviorFunctions
{
    public static Vector3D OrbitPoint(BehaviorContext context, BehaviorParameter? param)
    {
        var agent = context.Agent;
        var target = context.Position;
        var distance = param?.Value ?? 500d;
        var upVector = GetUpVector(context);
        var offset = agent.Position - target;
        var length = offset.Length();
        offset /= length;
        var position = agent.Position;
        var dot = Vector3D.Dot(upVector, offset);

        if (dot > 0.7 || dot < -0.4)
        {
            var perpendicular = Math.Sign(dot) * Vector3D.Cross(Vector3D.Cross(upVector, offset), offset).Normalized();
            position += perpendicular * CC_GAIN * 0.5;
        }

        var tangent = VectorMath.Cross(offset, upVector).Normalized();
        position += tangent * CC_GAIN;
        var radial = ((length < distance) ? 1 : -1) * offset * Math.Min(CC_GAIN * 1.5, Math.Abs(length - distance) * 2);
        position += radial;
        return position;
    }

    public static Vector3D CqbShifter(BehaviorContext context, BehaviorParameter? param)
    {
        var agent = context.Agent;
        var range = agent.WeaponController.Range;
        var parameter = new BehaviorParameter { Value = range };
        var target = context.Position;
        var offset = target - agent.Position;
        var length = offset.Length();

        if (length > range * 1.3)
        {
            VectorMath.ClosestPointOnSphere(agent.Position + offset / length * 3, agent.Position,
                new BoundingSphereD(target, range), ref target);
            context.Position = target;
        }
        else
        {
            context.Position = OrbitPoint(context, parameter);
            context.Position = AddRandomSway(context.Position, GetUpVector(context) * CC_GAIN * 0.5);
        }

        context.Position = AltitudeShifter(context, parameter);
        return context.Position;
    }

    public static Vector3D AltitudeShifter(BehaviorContext context, BehaviorParameter? param)
    {
        var altitude = context.Agent.FlightController.Altitude;
        if (altitude < 100)
        {
            return context.Position - context.Agent.FlightController.GravityDirection * (2 - altitude.Value / 100) * CC_GAIN;
        }
        return context.Position;
    }

    public static Vector3D BallisticSolver(BehaviorContext context, BehaviorParameter? param)
    {
        var agent = context.Agent;
        var velocity = agent.FlightController.Velocity;
        var position = agent.Position;

        agent.WeaponController.SetTarget(context.Position);
        var solution = agent.WeaponController.CalculateBallisticSolution(context.Position, context.Velocity, velocity,
            agent.FlightController.GravityVector ?? Vector3D.Zero);
        var result = agent.WeaponController.GetSolution();

        if (result.Direction != Vector3D.Zero)
        {
            MatrixD matrix;
            var up = agent.FlightController.RemoteControl.WorldMatrix.Up;
            if (Vector3D.ArePerpendicular(ref result.Direction, ref up))
                matrix = MatrixD.CreateFromDir(result.Direction, agent.FlightController.RemoteControl.WorldMatrix.Up);
            else
                matrix = MatrixD.CreateFromDir(result.Direction, agent.FlightController.RemoteControl.WorldMatrix.Forward);
            matrix.Translation = result.Position;
            context.BehaviorData.GetOrientation = () => matrix;
        }
        else
        {
            context.BehaviorData.GetOrientation = null;
        }

        if (agent.SubordinateManager.CurrentTarget.IsValid())
        {
            var ray = new RayD(position, (solution - position).Normalized());
            var boundingBox = agent.SubordinateManager.CurrentTarget.BoundingBox.Value;
            var sphere = new BoundingSphereD(boundingBox.Min, boundingBox.Max.X);
            if (!ray.Intersects(sphere).HasValue)
                agent.WeaponController.SetTarget(solution);
        }
        else
        {
            agent.WeaponController.SetTarget(solution);
        }

        return solution;
    }

    public static Vector3D AimRestrictPlane(BehaviorContext context, BehaviorParameter? param)
    {
        var agent = context.Agent;
        if (!agent.HasGravity())
            return context.Position;

        var position = agent.Position;
        var gravityDirection = agent.FlightController.GravityDirection;
        var offset = context.Position - position;
        var projected = Vector3D.ProjectOnPlane(ref offset, ref gravityDirection);
        var forward = agent.FlightController.RemoteControl.WorldMatrix.Forward;
        var dot = Vector3D.Dot(offset.Normalized(), forward);

        if (dot < 0)
            projected = Vector3D.ProjectOnPlane(ref projected, ref forward);

        return position + projected;
    }

    public static Vector3D NegativeGravity(BehaviorContext context, BehaviorParameter? param)
    {
        if (context.Agent.HasGravity())
            return -context.Agent.FlightController.GravityDirection;
        else
            return Vector3D.Zero;
    }

    public static Vector3D NegativeGravityVector(BehaviorContext context, BehaviorParameter? param)
    {
        return context.Agent.Position + NegativeGravity(context, param) * (param?.Value ?? 1f);
    }

    public static Vector3D NegativeGravityRejectTilt(BehaviorContext context, BehaviorParameter? param)
    {
        return context.Agent.Position + GetNegativeGravityWithTiltRejection(context, param);
    }

    public static Vector3D GetNegativeGravityWithTiltRejection(BehaviorContext context, BehaviorParameter? param)
    {
        var agent = context.Agent;
        var factor = param?.Value ?? 1f;

        if (agent.HasGravity())
        {
            var gravityVector = agent.FlightController.GravityVector.Value;
            if (agent.FlightController.Velocity.LengthSquared() > 0 && context.Position != agent.Position)
            {
                var tiltRejection = Vector3D.Reject(agent.FlightController.Velocity, (context.Position - agent.Position).Normalized()) / 5f * factor;
                return -(gravityVector + tiltRejection).Normalized();
            }
            return -gravityVector;
        }
        else
        {
            return Vector3D.Zero;
        }
    }

    public static Vector3D Closer(BehaviorContext context, BehaviorParameter? param)
    {
        return context.Position - (context.Position - context.Agent.Position).Normalized() * (param?.Value ?? 1f);
    }

    public static Vector3D Higher(BehaviorContext context, BehaviorParameter? param)
    {
        return context.Agent.HasGravity() ?
            context.Position - context.Agent.FlightController.GravityDirection * (param?.Value ?? 1f) :
            context.Position;
    }

    public static Vector3D GetUpVector(BehaviorContext context)
    {
        return context.Agent.HasGravity() ?
            -context.Agent.FlightController.GravityDirection :
            (context.Velocity.LengthSquared() > 20 * 20 ? context.Velocity.Normalized() : Vector3D.UnitY);
    }

    public static Vector3D SwayTargetXYZ(BehaviorContext context, BehaviorParameter? param)
    {
        var target = context.BehaviorData.PrimaryTarget;
        if (target?.Orientation.HasValue == true)
        {
            var direction = (context.Position - context.Agent.Position).Normalized();
            var axis = target.Orientation.Value.Left;

            if (Math.Abs(Vector3D.Dot(direction, target.Orientation.Value.Up)) < Math.Abs(Vector3D.Dot(direction, axis)))
                axis = target.Orientation.Value.Up;
            if (Math.Abs(Vector3D.Dot(direction, target.Orientation.Value.Forward)) < Math.Abs(Vector3D.Dot(direction, axis)))
                axis = target.Orientation.Value.Forward;

            if (param?.Value == null && target.BoundingBox.HasValue)
                param = new BehaviorParameter { Value = (float)target.BoundingBox.Value.HalfExtents.Length() / 2f };

            return context.Position + CreateSway(axis, param);
        }
        return context.Position;
    }

    static Vector3D CreateSway(Vector3D axis, BehaviorParameter? param)
    {
        return axis * Math.Sin(GlobalTimer.ElapsedTime / 3) * (param?.Value ?? 30);
    }

    public static Vector3D SwayX(BehaviorContext context, BehaviorParameter? param)
    {
        return context.Position + CreateSway(context.Agent.FlightController.RemoteControl.WorldMatrix.Left, param);
    }

    public static Vector3D SwayY(BehaviorContext context, BehaviorParameter? param)
    {
        return context.Position + CreateSway(context.Agent.FlightController.RemoteControl.WorldMatrix.Up, param);
    }

    public static Vector3D AddRandomSway(Vector3D position, Vector3D swayVector)
    {
        return position + swayVector * Math.Sin(GlobalTimer.ElapsedTime / 2);
    }

    public static Vector3D Empty(BehaviorContext context, BehaviorParameter? param)
    {
        return context.Position;
    }
}

// Utility classes
public static class VectorMath
{
    public static Vector3D Cross(Vector3D left, Vector3D right)
    {
        return Vector3D.Cross(left, right);
    }

    public static void ClosestPointOnSphere(Vector3D point, Vector3D center, BoundingSphereD sphere, ref Vector3D result)
    {
        var direction = (point - center).Normalized();
        result = center + direction * sphere.Radius;
    }
}

public static class GlobalTimer
{
    public static double ElapsedTime;
    public static int CurrentTick;
}

// This completes the core behavior system of the deminified APCK script
// The original implements a sophisticated autopilot with advanced movement behaviors,
// targeting systems, and flight control for Space Engineers ships
